# DOMSTK_RANK - 국내주식 수익자산지표 순위
# Generated by KIS API Generator (Single API Mode)
import logging
import sys
import time
from typing import Optional

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 수익자산지표 순위[v1_국내주식-090]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/ranking/profit-asset-index"

def profit_asset_index(
        fid_cond_mrkt_div_code: str,  # 조건 시장 분류 코드
        fid_trgt_cls_code: str,  # 대상 구분 코드
        fid_cond_scr_div_code: str,  # 조건 화면 분류 코드
        fid_input_iscd: str,  # 입력 종목코드
        fid_div_cls_code: str,  # 분류 구분 코드
        fid_input_price_1: str,  # 입력 가격1
        fid_input_price_2: str,  # 입력 가격2
        fid_vol_cnt: str,  # 거래량 수
        fid_input_option_1: str,  # 입력 옵션1
        fid_input_option_2: str,  # 입력 옵션2
        fid_rank_sort_cls_code: str,  # 순위 정렬 구분 코드
        fid_blng_cls_code: str,  # 소속 구분 코드
        fid_trgt_exls_cls_code: str,  # 대상 제외 구분 코드
        tr_cont: str = "",  # 연속 거래 여부
        dataframe: Optional[pd.DataFrame] = None  # 누적 데이터프레임
) -> Optional[pd.DataFrame]:
    """
    [국내주식] 순위분석 
    국내주식 수익자산지표 순위[v1_국내주식-090]
    국내주식 수익자산지표 순위 API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_cond_mrkt_div_code (str): 조건 시장 분류 코드 (필수)
        fid_trgt_cls_code (str): 대상 구분 코드 (필수)
        fid_cond_scr_div_code (str): 조건 화면 분류 코드 (필수)
        fid_input_iscd (str): 입력 종목코드 (필수)
        fid_div_cls_code (str): 분류 구분 코드 (필수)
        fid_input_price_1 (str): 입력 가격1 (필수)
        fid_input_price_2 (str): 입력 가격2 (필수)
        fid_vol_cnt (str): 거래량 수 (필수)
        fid_input_option_1 (str): 입력 옵션1 (필수)
        fid_input_option_2 (str): 입력 옵션2 (필수)
        fid_rank_sort_cls_code (str): 순위 정렬 구분 코드 (필수)
        fid_blng_cls_code (str): 소속 구분 코드 (필수)
        fid_trgt_exls_cls_code (str): 대상 제외 구분 코드 (필수)
        tr_cont (str): 연속 거래 여부 (옵션)
        dataframe (Optional[pd.DataFrame]): 누적 데이터프레임 (옵션)
        
    Returns:
        Optional[pd.DataFrame]: 국내주식 수익자산지표 순위 데이터
        
    Example:
        >>> df = profit_asset_index(
        ...     fid_cond_mrkt_div_code="J",
        ...     fid_trgt_cls_code="0",
        ...     fid_cond_scr_div_code="20173",
        ...     fid_input_iscd="0000",
        ...     fid_div_cls_code="0",
        ...     fid_input_price_1="",
        ...     fid_input_price_2="",
        ...     fid_vol_cnt="",
        ...     fid_input_option_1="2023",
        ...     fid_input_option_2="0",
        ...     fid_rank_sort_cls_code="0",
        ...     fid_blng_cls_code="0",
        ...     fid_trgt_exls_cls_code="0"
        ... )
        >>> print(df)
    """
    # 필수 파라미터 검증
    if fid_cond_mrkt_div_code != "J":
        raise ValueError("조건 시장 분류 코드 확인요망!!!")
    if fid_trgt_cls_code != "0":
        raise ValueError("대상 구분 코드 확인요망!!!")
    if fid_cond_scr_div_code != "20173":
        raise ValueError("조건 화면 분류 코드 확인요망!!!")
    if fid_input_iscd not in ["0000", "0001", "1001", "2001"]:
        raise ValueError("입력 종목코드 확인요망!!!")
    if fid_div_cls_code != "0":
        raise ValueError("분류 구분 코드 확인요망!!!")
    if fid_input_option_1 != "2023":
        raise ValueError("입력 옵션1 확인요망!!!")
    if fid_input_option_2 not in ["0", "1", "2", "3"]:
        raise ValueError("입력 옵션2 확인요망!!!")
    if fid_rank_sort_cls_code not in ["0", "1", "2", "3", "4", "5", "6"]:
        raise ValueError("순위 정렬 구분 코드 확인요망!!!")
    if fid_blng_cls_code != "0":
        raise ValueError("소속 구분 코드 확인요망!!!")
    if fid_trgt_exls_cls_code != "0":
        raise ValueError("대상 제외 구분 코드 확인요망!!!")


    tr_id = "FHPST01730000"

    params = {
        "fid_cond_mrkt_div_code": fid_cond_mrkt_div_code,
        "fid_trgt_cls_code": fid_trgt_cls_code,
        "fid_cond_scr_div_code": fid_cond_scr_div_code,
        "fid_input_iscd": fid_input_iscd,
        "fid_div_cls_code": fid_div_cls_code,
        "fid_input_price_1": fid_input_price_1,
        "fid_input_price_2": fid_input_price_2,
        "fid_vol_cnt": fid_vol_cnt,
        "fid_input_option_1": fid_input_option_1,
        "fid_input_option_2": fid_input_option_2,
        "fid_rank_sort_cls_code": fid_rank_sort_cls_code,
        "fid_blng_cls_code": fid_blng_cls_code,
        "fid_trgt_exls_cls_code": fid_trgt_exls_cls_code,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        # 응답 데이터 처리
        if hasattr(res.getBody(), 'output'):
            current_data = pd.DataFrame(res.getBody().output)
        else:
            current_data = pd.DataFrame()

        # 데이터프레임 병합
        if dataframe is not None:
            dataframe = pd.concat([dataframe, current_data], ignore_index=True)
        else:
            dataframe = current_data

        # 연속 거래 여부 확인
        tr_cont = res.getHeader().tr_cont

        if tr_cont == "M":
            print("Call Next")
            ka.smart_sleep()
            return profit_asset_index(
                fid_cond_mrkt_div_code,
                fid_trgt_cls_code,
                fid_cond_scr_div_code,
                fid_input_iscd,
                fid_div_cls_code,
                fid_input_price_1,
                fid_input_price_2,
                fid_vol_cnt,
                fid_input_option_1,
                fid_input_option_2,
                fid_rank_sort_cls_code,
                fid_blng_cls_code,
                fid_trgt_exls_cls_code,
                "N", dataframe
            )
        else:
            print("The End")
            return dataframe
    else:
        # 오류 처리
        res.printError(API_URL)
        return pd.DataFrame()
