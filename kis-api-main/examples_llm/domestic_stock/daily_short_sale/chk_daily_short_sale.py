"""
Created on 20250601 
@author: LaivData SJPark with cursor
"""

import sys
import logging

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka
from daily_short_sale import daily_short_sale

# 로깅 설정
logging.basicConfig(level=logging.INFO)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 공매도 일별추이[국내주식-134]
##############################################################################################

COLUMN_MAPPING = {
    'stck_prpr': '주식 현재가',
    'prdy_vrss': '전일 대비',
    'prdy_vrss_sign': '전일 대비 부호',
    'prdy_ctrt': '전일 대비율',
    'acml_vol': '누적 거래량',
    'prdy_vol': '전일 거래량',
    'stck_bsop_date': '주식 영업 일자',
    'stck_clpr': '주식 종가',
    'prdy_vrss': '전일 대비',
    'prdy_vrss_sign': '전일 대비 부호',
    'prdy_ctrt': '전일 대비율',
    'acml_vol': '누적 거래량',
    'stnd_vol_smtn': '기준 거래량 합계',
    'ssts_cntg_qty': '공매도 체결 수량',
    'ssts_vol_rlim': '공매도 거래량 비중',
    'acml_ssts_cntg_qty': '누적 공매도 체결 수량',
    'acml_ssts_cntg_qty_rlim': '누적 공매도 체결 수량 비중',
    'acml_tr_pbmn': '누적 거래 대금',
    'stnd_tr_pbmn_smtn': '기준 거래대금 합계',
    'ssts_tr_pbmn': '공매도 거래 대금',
    'ssts_tr_pbmn_rlim': '공매도 거래대금 비중',
    'acml_ssts_tr_pbmn': '누적 공매도 거래 대금',
    'acml_ssts_tr_pbmn_rlim': '누적 공매도 거래 대금 비중',
    'stck_oprc': '주식 시가2',
    'stck_hgpr': '주식 최고가',
    'stck_lwpr': '주식 최저가',
    'avrg_prc': '평균가격'
}

NUMERIC_COLUMNS = []

def main():
    """
    국내주식 공매도 일별추이 조회 테스트 함수
    
    이 함수는 국내주식 공매도 일별추이 API를 호출하여 결과를 출력합니다.
    테스트 데이터로 삼성전자(005930)를 사용합니다.
    
    Returns:
        None
    """

    # pandas 출력 옵션 설정
    pd.set_option('display.max_columns', None)  # 모든 컬럼 표시
    pd.set_option('display.width', None)  # 출력 너비 제한 해제
    pd.set_option('display.max_rows', None)  # 모든 행 표시
    
    # 인증 토큰 발급
    ka.auth()
    
    # case1 조회
    logging.info("=== case1 조회 ===")
    try:
        result1, result2 = daily_short_sale(
            fid_cond_mrkt_div_code="J",
            fid_input_iscd="005930",
            fid_input_date_1="20240301",
            fid_input_date_2="20240328"
        )
    except ValueError as e:
        logging.error("에러 발생: %s" % str(e))
        return
    
    # output1 결과 처리
    logging.info("=== output1 결과 ===")
    logging.info("사용 가능한 컬럼: %s", result1.columns.tolist())
    
    # 컬럼명 한글 변환 및 데이터 출력
    result1 = result1.rename(columns=COLUMN_MAPPING)
    
    # 숫자형 컬럼 소수점 둘째자리까지 표시 (메타데이터에 number 타입 명시된 것이 없으므로 비어있음)

    for col in NUMERIC_COLUMNS:
        if col in result1.columns:
            result1[col] = pd.to_numeric(result1[col], errors='coerce').round(2)
    
    logging.info("결과:")
    print(result1)
    
    # output2 결과 처리
    logging.info("=== output2 결과 ===")
    logging.info("사용 가능한 컬럼: %s" % result2.columns.tolist())
    
    # 컬럼명 한글 변환 및 데이터 출력
    result2 = result2.rename(columns=COLUMN_MAPPING)
    
    # 숫자형 컬럼 소수점 둘째자리까지 표시 (메타데이터에 number 타입 명시된 것이 없으므로 비어있음)

    for col in NUMERIC_COLUMNS:
        if col in result2.columns:
            result2[col] = pd.to_numeric(result2[col], errors='coerce').round(2)
    
    logging.info("결과:")
    print(result2)

if __name__ == "__main__":
    main() 