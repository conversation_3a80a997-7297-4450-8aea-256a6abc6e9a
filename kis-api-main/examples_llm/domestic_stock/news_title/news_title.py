# [국내주식] 업종/기타 - 종합 시황/공시(제목)
# Generated by KIS API Generator (Single API Mode)
# -*- coding: utf-8 -*-

"""
Created on 2025-06-17

@author: LaivData jjlee with cursor
"""

import logging
import sys
import time
from typing import Optional

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 종목정보 > 종합 시황/공시(제목) [국내주식-141]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/quotations/news-title"

def news_title(
        fid_news_ofer_entp_code: str,  # 뉴스 제공 업체 코드
        fid_cond_mrkt_cls_code: str,  # 조건 시장 구분 코드
        fid_input_iscd: str,  # 입력 종목코드
        fid_titl_cntt: str,  # 제목 내용
        fid_input_date_1: str,  # 입력 날짜
        fid_input_hour_1: str,  # 입력 시간
        fid_rank_sort_cls_code: str,  # 순위 정렬 구분 코드
        fid_input_srno: str,  # 입력 일련번호
        tr_cont: str = "",  # 연속 거래 여부
        dataframe: Optional[pd.DataFrame] = None,  # 누적 데이터프레임
        depth: int = 0,  # 현재 재귀 깊이
        max_depth: int = 10  # 최대 재귀 깊이
) -> Optional[pd.DataFrame]:
    """
    [국내주식] 업종/기타 
    종합 시황_공시(제목)[국내주식-141]
    종합 시황_공시(제목) API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_news_ofer_entp_code (str): 뉴스 제공 업체 코드
        fid_cond_mrkt_cls_code (str): 조건 시장 구분 코드
        fid_input_iscd (str): 입력 종목코드
        fid_titl_cntt (str): 제목 내용
        fid_input_date_1 (str): 입력 날짜
        fid_input_hour_1 (str): 입력 시간
        fid_rank_sort_cls_code (str): 순위 정렬 구분 코드
        fid_input_srno (str): 입력 일련번호
        tr_cont (str): 연속 거래 여부
        dataframe (Optional[pd.DataFrame]): 누적 데이터프레임
        depth (int): 현재 재귀 깊이
        max_depth (int): 최대 재귀 깊이 (기본값: 10)
        
    Returns:
        Optional[pd.DataFrame]: 종합 시황_공시(제목) 데이터
        
    Example:
        >>> df = news_title(
        ...     fid_news_ofer_entp_code='2',
        ...     fid_cond_mrkt_cls_code='00',
        ...     fid_input_iscd='005930',
        ...     fid_titl_cntt='',
        ...     fid_input_date_1='20231010',
        ...     fid_input_hour_1='090000',
        ...     fid_rank_sort_cls_code='01',
        ...     fid_input_srno='1'
        ... )
        >>> print(df)
    """
    # 최대 재귀 깊이 체크
    if depth >= max_depth:
        logger.warning("Maximum recursion depth (%d) reached. Stopping further requests.", max_depth)
        return dataframe if dataframe is not None else pd.DataFrame()

    # API URL 및 거래 ID 설정
    tr_id = "FHKST01011800"

    # 요청 파라미터 설정
    params = {
        "FID_NEWS_OFER_ENTP_CODE": fid_news_ofer_entp_code,
        "FID_COND_MRKT_CLS_CODE": fid_cond_mrkt_cls_code,
        "FID_INPUT_ISCD": fid_input_iscd,
        "FID_TITL_CNTT": fid_titl_cntt,
        "FID_INPUT_DATE_1": fid_input_date_1,
        "FID_INPUT_HOUR_1": fid_input_hour_1,
        "FID_RANK_SORT_CLS_CODE": fid_rank_sort_cls_code,
        "FID_INPUT_SRNO": fid_input_srno,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    # API 응답 처리
    if res.isOK():
        if hasattr(res.getBody(), 'output'):
            output_data = res.getBody().output
            if not isinstance(output_data, list):
                output_data = [output_data]
            current_data = pd.DataFrame(output_data)
        else:
            current_data = pd.DataFrame()

        # 데이터프레임 병합
        if dataframe is not None:
            dataframe = pd.concat([dataframe, current_data], ignore_index=True)
        else:
            dataframe = current_data

        # 연속 거래 여부 확인
        tr_cont = res.getHeader().tr_cont
        if tr_cont == "M":
            logger.info("Calling next page...")
            ka.smart_sleep()
            return news_title(
                fid_news_ofer_entp_code,
                fid_cond_mrkt_cls_code,
                fid_input_iscd,
                fid_titl_cntt,
                fid_input_date_1,
                fid_input_hour_1,
                fid_rank_sort_cls_code,
                fid_input_srno,
                "N", dataframe, depth + 1, max_depth
            )
        else:
            logger.info("Data fetch complete.")
            return dataframe
    else:
        logger.error("API call failed: %s - %s", res.getErrorCode(), res.getErrorMessage())
        res.printError(API_URL)
        return pd.DataFrame()
