# DOMSTK_RANK - 국내주식 체결강도 상위
# Generated by KIS API Generator (Single API Mode)
import logging
import sys
import time
from typing import Optional

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 체결강도 상위[v1_국내주식-101]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/ranking/volume-power"

def volume_power(
        fid_trgt_exls_cls_code: str,  # 대상 제외 구분 코드
        fid_cond_mrkt_div_code: str,  # 조건 시장 분류 코드
        fid_cond_scr_div_code: str,  # 조건 화면 분류 코드
        fid_input_iscd: str,  # 입력 종목코드
        fid_div_cls_code: str,  # 분류 구분 코드
        fid_input_price_1: str,  # 입력 가격1
        fid_input_price_2: str,  # 입력 가격2
        fid_vol_cnt: str,  # 거래량 수
        fid_trgt_cls_code: str,  # 대상 구분 코드
        tr_cont: str = "",
        dataframe: Optional[pd.DataFrame] = None,
        depth: int = 0,
        max_depth: int = 10
) -> Optional[pd.DataFrame]:
    """
    [국내주식] 순위분석 
    국내주식 체결강도 상위[v1_국내주식-101]
    국내주식 체결강도 상위 API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_trgt_exls_cls_code (str): 0 : 전체
        fid_cond_mrkt_div_code (str): 시장구분코드 (주식 J)
        fid_cond_scr_div_code (str): Unique key( 20168 )
        fid_input_iscd (str): 0000:전체, 0001:거래소, 1001:코스닥, 2001:코스피200
        fid_div_cls_code (str): 0: 전체,  1: 보통주 2: 우선주
        fid_input_price_1 (str): 입력값 없을때 전체 (가격 ~)
        fid_input_price_2 (str): 입력값 없을때 전체 (~ 가격)
        fid_vol_cnt (str): 입력값 없을때 전체 (거래량 ~)
        fid_trgt_cls_code (str): 0 : 전체
        tr_cont (str): 연속 거래 여부
        dataframe (Optional[pd.DataFrame]): 누적 데이터프레임
        depth (int): 현재 재귀 깊이
        max_depth (int): 최대 재귀 깊이 (기본값: 10)
        
    Returns:
        Optional[pd.DataFrame]: 국내주식 체결강도 상위 데이터
        
    Example:
        >>> df = volume_power(
        ...     fid_trgt_exls_cls_code="0",
        ...     fid_cond_mrkt_div_code="J",
        ...     fid_cond_scr_div_code="20168",
        ...     fid_input_iscd="0000",
        ...     fid_div_cls_code="0",
        ...     fid_input_price_1="",
        ...     fid_input_price_2="",
        ...     fid_vol_cnt="",
        ...     fid_trgt_cls_code="0"
        ... )
        >>> print(df)
    """
    # 필수 파라미터 검증
    if not fid_trgt_exls_cls_code:
        logger.error("fid_trgt_exls_cls_code is required. (e.g. '0')")
        raise ValueError("fid_trgt_exls_cls_code is required. (e.g. '0')")
    if fid_cond_mrkt_div_code != "J":
        logger.error("fid_cond_mrkt_div_code must be 'J'.")
        raise ValueError("fid_cond_mrkt_div_code must be 'J'.")
    if fid_cond_scr_div_code != "20168":
        logger.error("fid_cond_scr_div_code must be '20168'.")
        raise ValueError("fid_cond_scr_div_code must be '20168'.")
    if fid_input_iscd not in ["0000", "0001", "1001", "2001"]:
        logger.error("fid_input_iscd must be one of ['0000', '0001', '1001', '2001'].")
        raise ValueError("fid_input_iscd must be one of ['0000', '0001', '1001', '2001'].")
    if fid_div_cls_code not in ["0", "1", "2"]:
        logger.error("fid_div_cls_code must be one of ['0', '1', '2'].")
        raise ValueError("fid_div_cls_code must be one of ['0', '1', '2'].")
    if not fid_trgt_cls_code:
        logger.error("fid_trgt_cls_code is required. (e.g. '0')")
        raise ValueError("fid_trgt_cls_code is required. (e.g. '0')")

    # 최대 재귀 깊이 체크
    if depth >= max_depth:
        logger.warning("Maximum recursion depth (%d) reached. Stopping further requests.", max_depth)
        return dataframe if dataframe is not None else pd.DataFrame()


    tr_id = "FHPST01680000"

    params = {
        "fid_trgt_exls_cls_code": fid_trgt_exls_cls_code,
        "fid_cond_mrkt_div_code": fid_cond_mrkt_div_code,
        "fid_cond_scr_div_code": fid_cond_scr_div_code,
        "fid_input_iscd": fid_input_iscd,
        "fid_div_cls_code": fid_div_cls_code,
        "fid_input_price_1": fid_input_price_1,
        "fid_input_price_2": fid_input_price_2,
        "fid_vol_cnt": fid_vol_cnt,
        "fid_trgt_cls_code": fid_trgt_cls_code,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        # 응답 데이터 처리
        if hasattr(res.getBody(), 'output'):
            current_data = pd.DataFrame(res.getBody().output)
        else:
            current_data = pd.DataFrame()

        if dataframe is not None:
            dataframe = pd.concat([dataframe, current_data], ignore_index=True)
        else:
            dataframe = current_data

        tr_cont = res.getHeader().tr_cont

        if tr_cont == "M":
            logger.info("Calling next page...")
            ka.smart_sleep()
            return volume_power(
                fid_trgt_exls_cls_code,
                fid_cond_mrkt_div_code,
                fid_cond_scr_div_code,
                fid_input_iscd,
                fid_div_cls_code,
                fid_input_price_1,
                fid_input_price_2,
                fid_vol_cnt,
                fid_trgt_cls_code,
                "N", dataframe, depth + 1, max_depth
            )
        else:
            logger.info("Data fetch complete.")
            return dataframe
    else:
        # API 에러 처리
        logger.error("API call failed: %s - %s", res.getErrorCode(), res.getErrorMessage())
        res.printError(API_URL)
        return pd.DataFrame()
