# DOMSTK_RANK - 국내주식 호가잔량 순위
# Generated by KIS API Generator (Single API Mode)
import sys
import time
from typing import Optional
import logging

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 기본시세 > 국내주식 호가잔량 순위[국내주식-089]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/ranking/quote-balance"

def quote_balance(
        fid_vol_cnt: str,  # 거래량 수
        fid_cond_mrkt_div_code: str,  # 조건 시장 분류 코드
        fid_cond_scr_div_code: str,  # 조건 화면 분류 코드
        fid_input_iscd: str,  # 입력 종목코드
        fid_rank_sort_cls_code: str,  # 순위 정렬 구분 코드
        fid_div_cls_code: str,  # 분류 구분 코드
        fid_trgt_cls_code: str,  # 대상 구분 코드
        fid_trgt_exls_cls_code: str,  # 대상 제외 구분 코드
        fid_input_price_1: str,  # 입력 가격1
        fid_input_price_2: str,  # 입력 가격2
        tr_cont: str = "",  # 연속 거래 여부
        dataframe: Optional[pd.DataFrame] = None  # 누적 데이터프레임
) -> Optional[pd.DataFrame]:
    """
    [국내주식] 순위분석 
    국내주식 호가잔량 순위[국내주식-089]
    국내주식 호가잔량 순위 API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_vol_cnt (str): 입력값 없을때 전체 (거래량 ~)
        fid_cond_mrkt_div_code (str): 시장구분코드 (주식 J)
        fid_cond_scr_div_code (str): Unique key( 20172 )
        fid_input_iscd (str): 0000(전체) 코스피(0001), 코스닥(1001), 코스피200(2001)
        fid_rank_sort_cls_code (str): 0: 순매수잔량순, 1:순매도잔량순, 2:매수비율순, 3:매도비율순
        fid_div_cls_code (str): 0:전체
        fid_trgt_cls_code (str): 0:전체
        fid_trgt_exls_cls_code (str): 0:전체
        fid_input_price_1 (str): 입력값 없을때 전체 (가격 ~)
        fid_input_price_2 (str): 입력값 없을때 전체 (~ 가격)
        tr_cont (str): 연속 거래 여부
        dataframe (Optional[pd.DataFrame]): 누적 데이터프레임
        
    Returns:
        Optional[pd.DataFrame]: 국내주식 호가잔량 순위 데이터
        
    Example:
        >>> df = quote_balance(
        ...     fid_vol_cnt="",
        ...     fid_cond_mrkt_div_code="J",
        ...     fid_cond_scr_div_code="20172",
        ...     fid_input_iscd="0000",
        ...     fid_rank_sort_cls_code="0",
        ...     fid_div_cls_code="0",
        ...     fid_trgt_cls_code="0",
        ...     fid_trgt_exls_cls_code="0",
        ...     fid_input_price_1="",
        ...     fid_input_price_2=""
        ... )
        >>> print(df)
    """
    # 필수 파라미터 검증
    if fid_cond_mrkt_div_code != "J":
        raise ValueError("조건 시장 분류 코드 확인요망!!!")
    if fid_cond_scr_div_code != "20172":
        raise ValueError("조건 화면 분류 코드 확인요망!!!")
    if fid_input_iscd not in ["0000", "0001", "1001", "2001"]:
        raise ValueError("입력 종목코드 확인요망!!!")
    if fid_rank_sort_cls_code not in ["0", "1", "2", "3"]:
        raise ValueError("순위 정렬 구분 코드 확인요망!!!")
    if fid_div_cls_code != "0":
        raise ValueError("분류 구분 코드 확인요망!!!")
    if fid_trgt_cls_code != "0":
        raise ValueError("대상 구분 코드 확인요망!!!")
    if fid_trgt_exls_cls_code != "0":
        raise ValueError("대상 제외 구분 코드 확인요망!!!")


    tr_id = "FHPST01720000"

    params = {
        "fid_vol_cnt": fid_vol_cnt,
        "fid_cond_mrkt_div_code": fid_cond_mrkt_div_code,
        "fid_cond_scr_div_code": fid_cond_scr_div_code,
        "fid_input_iscd": fid_input_iscd,
        "fid_rank_sort_cls_code": fid_rank_sort_cls_code,
        "fid_div_cls_code": fid_div_cls_code,
        "fid_trgt_cls_code": fid_trgt_cls_code,
        "fid_trgt_exls_cls_code": fid_trgt_exls_cls_code,
        "fid_input_price_1": fid_input_price_1,
        "fid_input_price_2": fid_input_price_2,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        # 응답 데이터 처리
        if hasattr(res.getBody(), 'output'):
            current_data = pd.DataFrame(res.getBody().output)
        else:
            current_data = pd.DataFrame()

        # 데이터프레임 병합
        if dataframe is not None:
            dataframe = pd.concat([dataframe, current_data], ignore_index=True)
        else:
            dataframe = current_data

        # 연속 거래 여부 확인
        tr_cont = res.getHeader().tr_cont

        if tr_cont == "M":
            print("Call Next")
            ka.smart_sleep()
            return quote_balance(
                fid_vol_cnt,
                fid_cond_mrkt_div_code,
                fid_cond_scr_div_code,
                fid_input_iscd,
                fid_rank_sort_cls_code,
                fid_div_cls_code,
                fid_trgt_cls_code,
                fid_trgt_exls_cls_code,
                fid_input_price_1,
                fid_input_price_2,
                "N", dataframe
            )
        else:
            print("The End")
            return dataframe
    else:
        # 오류 출력
        res.printError(API_URL)
        return pd.DataFrame()
