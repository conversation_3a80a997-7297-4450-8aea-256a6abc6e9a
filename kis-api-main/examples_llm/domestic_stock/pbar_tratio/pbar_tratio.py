"""
Created on 20250601 
@author: LaivData SJPark with cursor
"""


import sys
import logging
from typing import Tuple

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 매물대/거래비중 [국내주식-196]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/quotations/pbar-tratio"

def pbar_tratio(
    fid_cond_mrkt_div_code: str,  # [필수] 조건 시장 분류 코드 (ex. J)
    fid_input_iscd: str,         # [필수] 입력 종목코드 (ex. 123456)
    fid_cond_scr_div_code: str,  # [필수] 조건화면분류코드 (ex. 20113)
    fid_input_hour_1: str = "",  # 입력시간 (기본값: "")
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    국내주식 매물대/거래비중 API입니다.
    한국투자 HTS(eFriend Plus) > [0113] 당일가격대별 매물대 화면의 데이터 중 일부를 API로 개발한 사항으로, 해당 화면을 참고하시면 기능을 이해하기 쉽습니다.
    
    Args:
        fid_cond_mrkt_div_code (str): [필수] 조건 시장 분류 코드 (ex. J)
        fid_input_iscd (str): [필수] 입력 종목코드 (ex. 123456)
        fid_cond_scr_div_code (str): [필수] 조건화면분류코드 (ex. 20113)
        fid_input_hour_1 (str): 입력시간 (기본값: "")

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: (output1 데이터, output2 데이터)
        
    Example:
        >>> df1, df2 = pbar_tratio("J", "005930", "20113")
        >>> print(df1)
        >>> print(df2)
    """

    # 필수 파라미터 검증
    if fid_cond_mrkt_div_code == "":
        raise ValueError("fid_cond_mrkt_div_code is required (e.g. 'J')")
    
    if fid_input_iscd == "":
        raise ValueError("fid_input_iscd is required (e.g. '123456')")
    
    if fid_cond_scr_div_code == "":
        raise ValueError("fid_cond_scr_div_code is required (e.g. '20113')")

    tr_id = "FHPST01130000"

    params = {
        "FID_COND_MRKT_DIV_CODE": fid_cond_mrkt_div_code,
        "FID_INPUT_ISCD": fid_input_iscd,
        "FID_COND_SCR_DIV_CODE": fid_cond_scr_div_code,
        "FID_INPUT_HOUR_1": fid_input_hour_1
    }
    
    res = ka._url_fetch(API_URL, tr_id, "", params)
    
    if res.isOK():
        # output1 (object) - 단일 객체를 DataFrame으로 변환
        output1_data = pd.DataFrame([res.getBody().output1])
        
        # output2 (array) - 배열을 DataFrame으로 변환
        output2_data = pd.DataFrame(res.getBody().output2)
        
        return output1_data, output2_data
    else:
        res.printError(url=API_URL)
        return pd.DataFrame(), pd.DataFrame() 