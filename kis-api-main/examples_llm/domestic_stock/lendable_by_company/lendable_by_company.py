# [국내주식] 종목정보 - 당사 대주가능 종목
# Generated by KIS API Generator (Single API Mode)
# -*- coding: utf-8 -*-
"""
Created on 2025-06-17

@author: LaivData jjlee with cursor
"""

import logging
import sys
import time
from typing import Optional, Tuple

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 종목정보 > 당사 대주가능 종목 [국내주식-195]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/quotations/lendable-by-company"

def lendable_by_company(
    excg_dvsn_cd: str,  # 거래소구분코드
    pdno: str,  # 상품번호
    thco_stln_psbl_yn: str,  # 당사대주가능여부
    inqr_dvsn_1: str,  # 조회구분1
    ctx_area_fk200: str,  # 연속조회검색조건200
    ctx_area_nk100: str,  # 연속조회키100
    dataframe1: Optional[pd.DataFrame] = None,  # 누적 데이터프레임 (output1)
    dataframe2: Optional[pd.DataFrame] = None,  # 누적 데이터프레임 (output2)
    tr_cont: str = "",
    depth: int = 0,
    max_depth: int = 10
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    [국내주식] 종목정보 
    당사 대주가능 종목[국내주식-195]
    당사 대주가능 종목 API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        excg_dvsn_cd (str): 00(전체), 02(거래소), 03(코스닥)
        pdno (str): 공백 : 전체조회, 종목코드 입력 시 해당종목만 조회
        thco_stln_psbl_yn (str): Y
        inqr_dvsn_1 (str): 0 : 전체조회, 1: 종목코드순 정렬
        ctx_area_fk200 (str): 미입력 (다음조회 불가)
        ctx_area_nk100 (str): 미입력 (다음조회 불가)
        dataframe1 (Optional[pd.DataFrame]): 누적 데이터프레임 (output1)
        dataframe2 (Optional[pd.DataFrame]): 누적 데이터프레임 (output2)
        tr_cont (str): 연속 거래 여부
        depth (int): 현재 재귀 깊이
        max_depth (int): 최대 재귀 깊이 (기본값: 10)
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: 당사 대주가능 종목 데이터
        
    Example:
        >>> df1, df2 = lendable_by_company('00', '', 'Y', '0', '', '')
        >>> print(df1)
        >>> print(df2)
    """
    # 로깅 설정
    logger = logging.getLogger(__name__)

    # 필수 파라미터 검증
    if not excg_dvsn_cd:
        logger.error("excg_dvsn_cd is required. (e.g. '00')")
        raise ValueError("excg_dvsn_cd is required. (e.g. '00')")

    if not thco_stln_psbl_yn:
        logger.error("thco_stln_psbl_yn is required. (e.g. 'Y')")
        raise ValueError("thco_stln_psbl_yn is required. (e.g. 'Y')")

    if not inqr_dvsn_1:
        logger.error("inqr_dvsn_1 is required. (e.g. '0')")
        raise ValueError("inqr_dvsn_1 is required. (e.g. '0')")

    # 최대 재귀 깊이 체크
    if depth >= max_depth:
        logger.warning("Maximum recursion depth (%d) reached. Stopping further requests.", max_depth)
        return dataframe1 if dataframe1 is not None else pd.DataFrame(), dataframe2 if dataframe2 is not None else pd.DataFrame()
    
    tr_id = "CTSC2702R"

    params = {
        "EXCG_DVSN_CD": excg_dvsn_cd,
        "PDNO": pdno,
        "THCO_STLN_PSBL_YN": thco_stln_psbl_yn,
        "INQR_DVSN_1": inqr_dvsn_1,
        "CTX_AREA_FK200": ctx_area_fk200,
        "CTX_AREA_NK100": ctx_area_nk100,
    }

    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        # output1 처리
        if hasattr(res.getBody(), 'output1'):
            output_data = res.getBody().output1
            if output_data:
                # output1은 단일 객체, output2는 배열일 수 있음
                if isinstance(output_data, list):
                    current_data1 = pd.DataFrame(output_data)
                else:
                    # 단일 객체인 경우 리스트로 감싸서 DataFrame 생성
                    current_data1 = pd.DataFrame([output_data])
                
                if dataframe1 is not None:
                    dataframe1 = pd.concat([dataframe1, current_data1], ignore_index=True)
                else:
                    dataframe1 = current_data1
            else:
                if dataframe1 is None:
                    dataframe1 = pd.DataFrame()
        else:
            if dataframe1 is None:
                dataframe1 = pd.DataFrame()
        # output2 처리
        if hasattr(res.getBody(), 'output2'):
            output_data = res.getBody().output2
            if output_data:
                # output1은 단일 객체, output2는 배열일 수 있음
                if isinstance(output_data, list):
                    current_data2 = pd.DataFrame(output_data)
                else:
                    # 단일 객체인 경우 리스트로 감싸서 DataFrame 생성
                    current_data2 = pd.DataFrame([output_data])
                
                if dataframe2 is not None:
                    dataframe2 = pd.concat([dataframe2, current_data2], ignore_index=True)
                else:
                    dataframe2 = current_data2
            else:
                if dataframe2 is None:
                    dataframe2 = pd.DataFrame()
        else:
            if dataframe2 is None:
                dataframe2 = pd.DataFrame()
        tr_cont = res.getHeader().tr_cont
        
        if tr_cont in ["M", "F"]:
            logger.info("Calling next page...")
            ka.smart_sleep()
            return lendable_by_company(
                excg_dvsn_cd,
                pdno,
                thco_stln_psbl_yn,
                inqr_dvsn_1,
                ctx_area_fk200,
                ctx_area_nk100,
                dataframe1, dataframe2, "N", depth + 1, max_depth
            )
        else:
            logger.info("Data fetch complete.")
            return dataframe1, dataframe2
    else:
        logger.error("API call failed: %s - %s", res.getErrorCode(), res.getErrorMessage())
        res.printError(API_URL)
        return pd.DataFrame(), pd.DataFrame()
