# DOMSTK_RANK - 국내주식 신고/신저근접종목 상위
# Generated by KIS API Generator (Single API Mode)
import logging
import sys
import time
from typing import Optional

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 신고_신저근접종목 상위[v1_국내주식-105]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/ranking/near-new-highlow"

def near_new_highlow(
        fid_aply_rang_vol: str,  # 적용 범위 거래량
        fid_cond_mrkt_div_code: str,  # 조건 시장 분류 코드
        fid_cond_scr_div_code: str,  # 조건 화면 분류 코드
        fid_div_cls_code: str,  # 분류 구분 코드
        fid_input_cnt_1: str,  # 입력 수1
        fid_input_cnt_2: str,  # 입력 수2
        fid_prc_cls_code: str,  # 가격 구분 코드
        fid_input_iscd: str,  # 입력 종목코드
        fid_trgt_cls_code: str,  # 대상 구분 코드
        fid_trgt_exls_cls_code: str,  # 대상 제외 구분 코드
        fid_aply_rang_prc_1: str,  # 적용 범위 가격1
        fid_aply_rang_prc_2: str,  # 적용 범위 가격2
        tr_cont: str = "",  # 연속 거래 여부
        dataframe: Optional[pd.DataFrame] = None  # 누적 데이터프레임
) -> Optional[pd.DataFrame]:
    """
    [국내주식] 순위분석 
    국내주식 신고_신저근접종목 상위[v1_국내주식-105]
    국내주식 신고_신저근접종목 상위 API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_aply_rang_vol (str): 0: 전체, 100: 100주 이상
        fid_cond_mrkt_div_code (str): 시장구분코드 (주식 J)
        fid_cond_scr_div_code (str): Unique key(20187)
        fid_div_cls_code (str): 0:전체, 1:관리종목, 2:투자주의, 3:투자경고
        fid_input_cnt_1 (str): 괴리율 최소
        fid_input_cnt_2 (str): 괴리율 최대
        fid_prc_cls_code (str): 0:신고근접, 1:신저근접
        fid_input_iscd (str): 0000:전체, 0001:거래소, 1001:코스닥, 2001:코스피200, 4001: KRX100
        fid_trgt_cls_code (str): 0: 전체
        fid_trgt_exls_cls_code (str): 0:전체, 1:관리종목, 2:투자주의, 3:투자경고, 4:투자위험예고, 5:투자위험, 6:보통주, 7:우선주
        fid_aply_rang_prc_1 (str): 가격 ~
        fid_aply_rang_prc_2 (str): ~ 가격
        tr_cont (str): 연속 거래 여부
        dataframe (Optional[pd.DataFrame]): 누적 데이터프레임
        
    Returns:
        Optional[pd.DataFrame]: 국내주식 신고_신저근접종목 상위 데이터
        
    Example:
        >>> df = near_new_highlow(
        ...     fid_aply_rang_vol="0",
        ...     fid_cond_mrkt_div_code="J",
        ...     fid_cond_scr_div_code="20187",
        ...     fid_div_cls_code="0",
        ...     fid_input_cnt_1="0",
        ...     fid_input_cnt_2="100",
        ...     fid_prc_cls_code="0",
        ...     fid_input_iscd="0000",
        ...     fid_trgt_cls_code="0",
        ...     fid_trgt_exls_cls_code="0",
        ...     fid_aply_rang_prc_1="0",
        ...     fid_aply_rang_prc_2="1000000"
        ... )
        >>> print(df)
    """
    # 필수 파라미터 검증
    if fid_aply_rang_vol not in ["0", "100"]:
        raise ValueError("적용 범위 거래량 확인요망!!!")
    if fid_cond_mrkt_div_code != "J":
        raise ValueError("조건 시장 분류 코드 확인요망!!!")
    if fid_cond_scr_div_code != "20187":
        raise ValueError("조건 화면 분류 코드 확인요망!!!")
    if fid_div_cls_code not in ["0", "1", "2", "3"]:
        raise ValueError("분류 구분 코드 확인요망!!!")
    if fid_prc_cls_code not in ["0", "1"]:
        raise ValueError("가격 구분 코드 확인요망!!!")
    if fid_input_iscd not in ["0000", "0001", "1001", "2001", "4001"]:
        raise ValueError("입력 종목코드 확인요망!!!")
    if fid_trgt_cls_code != "0":
        raise ValueError("대상 구분 코드 확인요망!!!")
    if fid_trgt_exls_cls_code not in ["0", "1", "2", "3", "4", "5", "6", "7"]:
        raise ValueError("대상 제외 구분 코드 확인요망!!!")


    tr_id = "FHPST01870000"

    params = {
        "fid_aply_rang_vol": fid_aply_rang_vol,
        "fid_cond_mrkt_div_code": fid_cond_mrkt_div_code,
        "fid_cond_scr_div_code": fid_cond_scr_div_code,
        "fid_div_cls_code": fid_div_cls_code,
        "fid_input_cnt_1": fid_input_cnt_1,
        "fid_input_cnt_2": fid_input_cnt_2,
        "fid_prc_cls_code": fid_prc_cls_code,
        "fid_input_iscd": fid_input_iscd,
        "fid_trgt_cls_code": fid_trgt_cls_code,
        "fid_trgt_exls_cls_code": fid_trgt_exls_cls_code,
        "fid_aply_rang_prc_1": fid_aply_rang_prc_1,
        "fid_aply_rang_prc_2": fid_aply_rang_prc_2,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        # 응답 데이터 처리
        if hasattr(res.getBody(), 'output'):
            current_data = pd.DataFrame(res.getBody().output)
        else:
            current_data = pd.DataFrame()

        # 기존 데이터프레임과 병합
        if dataframe is not None:
            dataframe = pd.concat([dataframe, current_data], ignore_index=True)
        else:
            dataframe = current_data

        # 연속 거래 여부 확인
        tr_cont = res.getHeader().tr_cont

        if tr_cont == "M":
            print("Call Next")
            ka.smart_sleep()
            return near_new_highlow(
                fid_aply_rang_vol,
                fid_cond_mrkt_div_code,
                fid_cond_scr_div_code,
                fid_div_cls_code,
                fid_input_cnt_1,
                fid_input_cnt_2,
                fid_prc_cls_code,
                fid_input_iscd,
                fid_trgt_cls_code,
                fid_trgt_exls_cls_code,
                fid_aply_rang_prc_1,
                fid_aply_rang_prc_2,
                "N", dataframe
            )
        else:
            print("The End")
            return dataframe
    else:
        # 오류 발생 시 처리
        res.printError(API_URL)
        return pd.DataFrame()
