# [국내주식] 업종/기타 - 국내주식업종기간별시세(일/주/월/년)
# Generated by KIS API Generator (Single API Mode)
# -*- coding: utf-8 -*-
"""
Created on 2025-06-17

@author: LaivData jjlee with cursor
"""

import logging
import time
from typing import Optional, <PERSON>ple
import sys

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 기본시세 > 국내주식업종기간별시세(일_주_월_년)[v1_국내주식-021]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/quotations/inquire-daily-indexchartprice"

def inquire_daily_indexchartprice(
        fid_cond_mrkt_div_code: str,  # 조건 시장 분류 코드
        fid_input_iscd: str,  # 업종 상세코드
        fid_input_date_1: str,  # 조회 시작일자
        fid_input_date_2: str,  # 조회 종료일자
        fid_period_div_code: str,  # 기간분류코드
        env_dv: str = "real",  # [추가] 실전모의구분 (real:실전, demo:모의)
        dataframe1: Optional[pd.DataFrame] = None,  # 누적 데이터프레임 (output1)
        dataframe2: Optional[pd.DataFrame] = None,  # 누적 데이터프레임 (output2)
        tr_cont: str = "",
        depth: int = 0,
        max_depth: int = 10
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    [국내주식] 업종/기타 
    국내주식업종기간별시세(일_주_월_년)[v1_국내주식-021]
    국내주식업종기간별시세(일_주_월_년) API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_cond_mrkt_div_code (str): 업종 : U
        fid_input_iscd (str): 0001 : 종합 0002 : 대형주 ... 포탈 (FAQ : 종목정보 다운로드(국내) - 업종코드 참조)
        fid_input_date_1 (str): 조회 시작일자 (ex. 20220501)
        fid_input_date_2 (str): 조회 종료일자 (ex. 20220530)
        fid_period_div_code (str): D:일봉 W:주봉, M:월봉, Y:년봉
        env_dv (str): [추가] 실전모의구분 (real:실전, demo:모의, 기본값: 'real')
        dataframe1 (Optional[pd.DataFrame]): 누적 데이터프레임 (output1)
        dataframe2 (Optional[pd.DataFrame]): 누적 데이터프레임 (output2)
        tr_cont (str): 연속 거래 여부
        depth (int): 현재 재귀 깊이
        max_depth (int): 최대 재귀 깊이 (기본값: 10)
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: 국내주식업종기간별시세(일_주_월_년) 데이터
        
    Example:
        >>> df1, df2 = inquire_daily_indexchartprice(
        ...     fid_cond_mrkt_div_code="U",
        ...     fid_input_iscd="0001",
        ...     fid_input_date_1="20220501",
        ...     fid_input_date_2="20220530",
        ...     fid_period_div_code="D",
        ...     env_dv="real"  # 실전투자
        ... )
        >>> df1, df2 = inquire_daily_indexchartprice(
        ...     fid_cond_mrkt_div_code="U",
        ...     fid_input_iscd="0001",
        ...     fid_input_date_1="20220501",
        ...     fid_input_date_2="20220530",
        ...     fid_period_div_code="D",
        ...     env_dv="demo"  # 모의투자
        ... )
        >>> print(df1)
        >>> print(df2)
    """
    # 필수 파라미터 검증
    if not fid_cond_mrkt_div_code:
        logger.error("fid_cond_mrkt_div_code is required. (e.g. 'U')")
        raise ValueError("fid_cond_mrkt_div_code is required. (e.g. 'U')")

    if not fid_input_iscd:
        logger.error("fid_input_iscd is required. (e.g. '0001')")
        raise ValueError("fid_input_iscd is required. (e.g. '0001')")

    if not fid_input_date_1:
        logger.error("fid_input_date_1 is required. (e.g. '20220501')")
        raise ValueError("fid_input_date_1 is required. (e.g. '20220501')")

    if not fid_input_date_2:
        logger.error("fid_input_date_2 is required. (e.g. '20220530')")
        raise ValueError("fid_input_date_2 is required. (e.g. '20220530')")

    if not fid_period_div_code:
        logger.error("fid_period_div_code is required. (e.g. 'D')")
        raise ValueError("fid_period_div_code is required. (e.g. 'D')")

    # env_dv 파라미터 검증 (모의투자 지원 로직)
    if env_dv not in ["real", "demo"]:
        logger.error("env_dv must be 'real' or 'demo'")
        raise ValueError("env_dv must be 'real' or 'demo'")

    # 최대 재귀 깊이 체크
    if depth >= max_depth:
        logger.warning("Maximum recursion depth (%d) reached. Stopping further requests.", max_depth)
        return dataframe1 if dataframe1 is not None else pd.DataFrame(), dataframe2 if dataframe2 is not None else pd.DataFrame()

    # API 호출 URL 설정


    # TR ID 설정 (모의투자 지원 로직)
    if env_dv == "real" or env_dv == "demo":
        tr_id = "FHKUP03500100"  # 실전투자용 TR ID
    else:
        raise ValueError("env_dv can only be 'real' or 'demo'")

    params = {
        "FID_COND_MRKT_DIV_CODE": fid_cond_mrkt_div_code,
        "FID_INPUT_ISCD": fid_input_iscd,
        "FID_INPUT_DATE_1": fid_input_date_1,
        "FID_INPUT_DATE_2": fid_input_date_2,
        "FID_PERIOD_DIV_CODE": fid_period_div_code,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        # output1 처리
        if hasattr(res.getBody(), 'output1'):
            output_data = res.getBody().output1
            if output_data:
                current_data1 = pd.DataFrame(output_data if isinstance(output_data, list) else [output_data])
                dataframe1 = pd.concat([dataframe1, current_data1],
                                       ignore_index=True) if dataframe1 is not None else current_data1
            else:
                dataframe1 = dataframe1 if dataframe1 is not None else pd.DataFrame()
        else:
            dataframe1 = dataframe1 if dataframe1 is not None else pd.DataFrame()

        # output2 처리
        if hasattr(res.getBody(), 'output2'):
            output_data = res.getBody().output2
            if output_data:
                current_data2 = pd.DataFrame(output_data if isinstance(output_data, list) else [output_data])
                dataframe2 = pd.concat([dataframe2, current_data2],
                                       ignore_index=True) if dataframe2 is not None else current_data2
            else:
                dataframe2 = dataframe2 if dataframe2 is not None else pd.DataFrame()
        else:
            dataframe2 = dataframe2 if dataframe2 is not None else pd.DataFrame()

        tr_cont = res.getHeader().tr_cont

        if tr_cont in ["M", "F"]:
            logger.info("Calling next page...")
            ka.smart_sleep()
            return inquire_daily_indexchartprice(
                fid_cond_mrkt_div_code,
                fid_input_iscd,
                fid_input_date_1,
                fid_input_date_2,
                fid_period_div_code,
                env_dv,  # env_dv 파라미터 추가
                dataframe1,
                dataframe2,
                "N", depth + 1, max_depth
            )
        else:
            logger.info("Data fetch complete.")
            return dataframe1, dataframe2
    else:
        logger.error("API call failed: %s - %s", res.getErrorCode(), res.getErrorMessage())
        res.printError(API_URL)
        return pd.DataFrame(), pd.DataFrame()
