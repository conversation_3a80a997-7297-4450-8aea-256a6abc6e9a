# DOMSTK_RANK - 국내주식 공매도 상위종목
# Generated by KIS API Generator (Single API Mode)
# -*- coding: utf-8 -*-
"""
Created on 2025-06-16

@author: LaivData jjlee with cursor
"""

import logging
import sys
import time
from typing import Optional

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 공매도 상위종목[국내주식-133]
##############################################################################################

# 상수 정의
API_URL = "/uapi/domestic-stock/v1/ranking/short-sale"

def short_sale(
        fid_aply_rang_vol: str,  # FID 적용 범위 거래량
        fid_cond_mrkt_div_code: str,  # 조건 시장 분류 코드
        fid_cond_scr_div_code: str,  # 조건 화면 분류 코드
        fid_input_iscd: str,  # 입력 종목코드
        fid_period_div_code: str,  # 조회구분 (일/월)
        fid_input_cnt_1: str,  # 조회가간(일수)
        fid_trgt_exls_cls_code: str,  # 대상 제외 구분 코드
        fid_trgt_cls_code: str,  # FID 대상 구분 코드
        fid_aply_rang_prc_1: str,  # FID 적용 범위 가격1
        fid_aply_rang_prc_2: str,  # FID 적용 범위 가격2
        tr_cont: str = "",
        dataframe: Optional[pd.DataFrame] = None,
        depth: int = 0,
        max_depth: int = 10
) -> Optional[pd.DataFrame]:
    """
    [국내주식] 순위분석 
    국내주식 공매도 상위종목[국내주식-133]
    국내주식 공매도 상위종목 API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_aply_rang_vol (str): FID 적용 범위 거래량
        fid_cond_mrkt_div_code (str): 시장구분코드 (주식 J)
        fid_cond_scr_div_code (str): Unique key(20482)
        fid_input_iscd (str): 0000:전체, 0001:코스피, 1001:코스닥, 2001:코스피200, 4001: KRX100, 3003: 코스닥150
        fid_period_div_code (str): 조회구분 (일/월) D: 일, M:월
        fid_input_cnt_1 (str): 조회가간(일수): 조회구분(D) 0:1일, 1:2일, 2:3일, 3:4일, 4:1주일, 9:2주일, 14:3주일, 조회구분(M) 1:1개월, 2:2개월, 3:3개월
        fid_trgt_exls_cls_code (str): 대상 제외 구분 코드
        fid_trgt_cls_code (str): FID 대상 구분 코드
        fid_aply_rang_prc_1 (str): FID 적용 범위 가격1
        fid_aply_rang_prc_2 (str): FID 적용 범위 가격2
        tr_cont (str): 연속 거래 여부
        dataframe (Optional[pd.DataFrame]): 누적 데이터프레임
        depth (int): 현재 재귀 깊이
        max_depth (int): 최대 재귀 깊이 (기본값: 10)
        
    Returns:
        Optional[pd.DataFrame]: 국내주식 공매도 상위종목 데이터
        
    Example:
        >>> df = short_sale(
        ...     fid_aply_rang_vol="1000",
        ...     fid_cond_mrkt_div_code="J",
        ...     fid_cond_scr_div_code="20482",
        ...     fid_input_iscd="0000",
        ...     fid_period_div_code="D",
        ...     fid_input_cnt_1="0",
        ...     fid_trgt_exls_cls_code="",
        ...     fid_trgt_cls_code="",
        ...     fid_aply_rang_prc_1="1000",
        ...     fid_aply_rang_prc_2="5000"
        ... )
        >>> print(df)
    """
    # 필수 파라미터 검증
    if not fid_cond_mrkt_div_code:
        logger.error("fid_cond_mrkt_div_code is required. (e.g. 'J')")
        return None
    if not fid_cond_scr_div_code:
        logger.error("fid_cond_scr_div_code is required. (e.g. '20482')")
        return None
    if not fid_input_iscd:
        logger.error("fid_input_iscd is required. (e.g. '0000')")
        return None
    if not fid_period_div_code:
        logger.error("fid_period_div_code is required. (e.g. 'D')")
        return None
    if not fid_input_cnt_1:
        logger.error("fid_input_cnt_1 is required. (e.g. '0')")
        return None

    # 최대 재귀 깊이 체크
    if depth >= max_depth:
        logger.warning("Maximum recursion depth (%d) reached. Stopping further requests.", max_depth)
        return dataframe if dataframe is not None else pd.DataFrame()

    tr_id = "FHPST04820000"

    params = {
        "FID_APLY_RANG_VOL": fid_aply_rang_vol,
        "FID_COND_MRKT_DIV_CODE": fid_cond_mrkt_div_code,
        "FID_COND_SCR_DIV_CODE": fid_cond_scr_div_code,
        "FID_INPUT_ISCD": fid_input_iscd,
        "FID_PERIOD_DIV_CODE": fid_period_div_code,
        "FID_INPUT_CNT_1": fid_input_cnt_1,
        "FID_TRGT_EXLS_CLS_CODE": fid_trgt_exls_cls_code,
        "FID_TRGT_CLS_CODE": fid_trgt_cls_code,
        "FID_APLY_RANG_PRC_1": fid_aply_rang_prc_1,
        "FID_APLY_RANG_PRC_2": fid_aply_rang_prc_2,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        if hasattr(res.getBody(), 'output'):
            current_data = pd.DataFrame(res.getBody().output)
        else:
            current_data = pd.DataFrame()

        if dataframe is not None:
            dataframe = pd.concat([dataframe, current_data], ignore_index=True)
        else:
            dataframe = current_data

        tr_cont = res.getHeader().tr_cont

        if tr_cont == "M":
            logger.info("Calling next page...")
            ka.smart_sleep()
            return short_sale(
                fid_aply_rang_vol,
                fid_cond_mrkt_div_code,
                fid_cond_scr_div_code,
                fid_input_iscd,
                fid_period_div_code,
                fid_input_cnt_1,
                fid_trgt_exls_cls_code,
                fid_trgt_cls_code,
                fid_aply_rang_prc_1,
                fid_aply_rang_prc_2,
                "N", dataframe, depth + 1, max_depth
            )
        else:
            logger.info("Data fetch complete.")
            return dataframe
    else:
        logger.error("API call failed: %s - %s", res.getErrorCode(), res.getErrorMessage())
        res.printError(API_URL)
        return pd.DataFrame()
