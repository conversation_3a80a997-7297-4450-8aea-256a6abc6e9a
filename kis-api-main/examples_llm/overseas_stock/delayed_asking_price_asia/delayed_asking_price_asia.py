"""
Created on 20250601
@author: LaivData SJPark with cursor
"""

import logging
import sys

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO)

##############################################################################################
# [해외주식] 실시간시세 > 해외주식 지연호가(아시아)[실시간-008]
##############################################################################################

def delayed_asking_price_asia(
        tr_type: str,
        tr_key: str,
) -> tuple[dict, list[str]]:
    """
    해외주식 지연호가(아시아)의 경우 아시아 무료시세(지연호가)가 제공됩니다.

    HTS(efriend Plus) [7781] 시세신청(실시간) 화면에서 유료 서비스 신청 시, 
    "해외주식 실시간호가 HDFSASP0" 을 이용하여 아시아국가 유료시세(실시간호가)를 받아보실 수 있습니다. (24.11.29 반영)

    ※ 지연시세 지연시간 : 홍콩, 베트남, 중국, 일본 - 15분지연

    Args:
        tr_type (str): [필수] 등록/해제
        tr_key (str): [필수] 종목코드

    Returns:
        message (dict): 메시지 데이터
        columns (list[str]): 컬럼 정보

    Example:
        >>> msg, columns = delayed_asking_price_asia("1", "DHKS00003")
        >>> print(msg, columns)
    """

    # 필수 파라미터 검증
    if tr_type == "":
        raise ValueError("tr_type is required")

    if tr_key == "":
        raise ValueError("tr_key is required")

    tr_id = "HDFSASP1"

    params = {
        "tr_key": tr_key,
    }

    msg = ka.data_fetch(tr_id, tr_type, params)

    columns = [
        "symb",
        "zdiv",
        "xymd",
        "xhms",
        "kymd",
        "khms",
        "bvol",
        "avol",
        "bdvl",
        "advl",
        "pbid1",
        "pask1",
        "vbid1",
        "vask1",
        "dbid1",
        "dask1"
    ]

    return msg, columns 