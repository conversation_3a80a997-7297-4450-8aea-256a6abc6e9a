# [국내주식] ELW시세 - ELW 기초자산별 종목시세
# Generated by KIS API Generator (Single API Mode)
# -*- coding: utf-8 -*-
"""
Created on 2025-06-18

@author: LaivData jjlee with cursor
"""

import logging
import time
from typing import Optional
import sys

import pandas as pd

sys.path.extend(['../..', '.'])
import kis_auth as ka

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

##############################################################################################
# [국내주식] ELW시세 - ELW 기초자산별 종목시세[국내주식-186]
##############################################################################################

# 상수 정의
API_URL = "/uapi/elw/v1/quotations/udrl-asset-price"

def udrl_asset_price(
    fid_cond_mrkt_div_code: str,  # 조건시장분류코드
    fid_cond_scr_div_code: str,  # 조건화면분류코드
    fid_mrkt_cls_code: str,  # 시장구분코드
    fid_input_iscd: str,  # 입력종목코드
    fid_unas_input_iscd: str,  # 기초자산입력종목코드
    fid_vol_cnt: str,  # 거래량수
    fid_trgt_exls_cls_code: str,  # 대상제외구분코드
    fid_input_price_1: str,  # 입력가격1
    fid_input_price_2: str,  # 입력가격2
    fid_input_vol_1: str,  # 입력거래량1
    fid_input_vol_2: str,  # 입력거래량2
    fid_input_rmnn_dynu_1: str,  # 입력잔존일수1
    fid_input_rmnn_dynu_2: str,  # 입력잔존일수2
    fid_option: str,  # 옵션
    fid_input_option_1: str,  # 입력옵션1
    fid_input_option_2: str,  # 입력옵션2
    tr_cont: str = "",
    dataframe: Optional[pd.DataFrame] = None,
    depth: int = 0,
    max_depth: int = 10
) -> Optional[pd.DataFrame]:
    """
    [국내주식] ELW시세 
    ELW 기초자산별 종목시세[국내주식-186]
    ELW 기초자산별 종목시세 API를 호출하여 DataFrame으로 반환합니다.
    
    Args:
        fid_cond_mrkt_div_code (str): 시장구분(W)
        fid_cond_scr_div_code (str): Uniquekey(11541)
        fid_mrkt_cls_code (str): 전체(A),콜(C),풋(P)
        fid_input_iscd (str): '00000(전체), 00003(한국투자증권) , 00017(KB증권), 00005(미래에셋주식회사)'
        fid_unas_input_iscd (str): 기초자산입력종목코드
        fid_vol_cnt (str): 전일거래량(정수량미만)
        fid_trgt_exls_cls_code (str): 거래불가종목제외(0:미체크,1:체크)
        fid_input_price_1 (str): 가격~원이상
        fid_input_price_2 (str): 가격~월이하
        fid_input_vol_1 (str): 거래량~계약이상
        fid_input_vol_2 (str): 거래량~계약이하
        fid_input_rmnn_dynu_1 (str): 잔존일(~일이상)
        fid_input_rmnn_dynu_2 (str): 잔존일(~일이하)
        fid_option (str): 옵션상태(0:없음,1:ATM,2:ITM,3:OTM)
        fid_input_option_1 (str): 입력옵션1
        fid_input_option_2 (str): 입력옵션2
        tr_cont (str): 연속 거래 여부
        dataframe (Optional[pd.DataFrame]): 누적 데이터프레임
        depth (int): 현재 재귀 깊이
        max_depth (int): 최대 재귀 깊이 (기본값: 10)
        
    Returns:
        Optional[pd.DataFrame]: ELW 기초자산별 종목시세 데이터
        
    Example:
        >>> df = udrl_asset_price(
        ...     fid_cond_mrkt_div_code='W',
        ...     fid_cond_scr_div_code='11541',
        ...     fid_mrkt_cls_code='A',
        ...     fid_input_iscd='00000',
        ...     fid_unas_input_iscd='005930',
        ...     fid_vol_cnt='1000',
        ...     fid_trgt_exls_cls_code='0',
        ...     fid_input_price_1='1000',
        ...     fid_input_price_2='5000',
        ...     fid_input_vol_1='100',
        ...     fid_input_vol_2='500',
        ...     fid_input_rmnn_dynu_1='10',
        ...     fid_input_rmnn_dynu_2='20',
        ...     fid_option='0',
        ...     fid_input_option_1='',
        ...     fid_input_option_2=''
        ... )
        >>> print(df)
    """
    # 로깅 설정
    logger = logging.getLogger(__name__)

    # 필수 파라미터 검증
    if not fid_cond_mrkt_div_code:
        logger.error("fid_cond_mrkt_div_code is required. (e.g. 'W')")
        raise ValueError("fid_cond_mrkt_div_code is required. (e.g. 'W')")

    if not fid_cond_scr_div_code:
        logger.error("fid_cond_scr_div_code is required. (e.g. '11541')")
        raise ValueError("fid_cond_scr_div_code is required. (e.g. '11541')")

    if not fid_mrkt_cls_code:
        logger.error("fid_mrkt_cls_code is required. (e.g. 'A')")
        raise ValueError("fid_mrkt_cls_code is required. (e.g. 'A')")

    if not fid_input_iscd:
        logger.error("fid_input_iscd is required. (e.g. '00000')")
        raise ValueError("fid_input_iscd is required. (e.g. '00000')")

    if not fid_unas_input_iscd:
        logger.error("fid_unas_input_iscd is required. (e.g. '00001')")
        raise ValueError("fid_unas_input_iscd is required. (e.g. '00001')")

    if not fid_trgt_exls_cls_code:
        logger.error("fid_trgt_exls_cls_code is required. (e.g. '0')")
        raise ValueError("fid_trgt_exls_cls_code is required. (e.g. '0')")

    if not fid_option:
        logger.error("fid_option is required. (e.g. '0')")
        raise ValueError("fid_option is required. (e.g. '0')")


    # 최대 재귀 깊이 체크
    if depth >= max_depth:
        logger.warning("Maximum recursion depth (%d) reached. Stopping further requests.", max_depth)
        return dataframe if dataframe is not None else pd.DataFrame()

    tr_id = "FHKEW154101C0"

    params = {
        "FID_COND_MRKT_DIV_CODE": fid_cond_mrkt_div_code,
        "FID_COND_SCR_DIV_CODE": fid_cond_scr_div_code,
        "FID_MRKT_CLS_CODE": fid_mrkt_cls_code,
        "FID_INPUT_ISCD": fid_input_iscd,
        "FID_UNAS_INPUT_ISCD": fid_unas_input_iscd,
        "FID_VOL_CNT": fid_vol_cnt,
        "FID_TRGT_EXLS_CLS_CODE": fid_trgt_exls_cls_code,
        "FID_INPUT_PRICE_1": fid_input_price_1,
        "FID_INPUT_PRICE_2": fid_input_price_2,
        "FID_INPUT_VOL_1": fid_input_vol_1,
        "FID_INPUT_VOL_2": fid_input_vol_2,
        "FID_INPUT_RMNN_DYNU_1": fid_input_rmnn_dynu_1,
        "FID_INPUT_RMNN_DYNU_2": fid_input_rmnn_dynu_2,
        "FID_OPTION": fid_option,
        "FID_INPUT_OPTION_1": fid_input_option_1,
        "FID_INPUT_OPTION_2": fid_input_option_2,
    }

    # API 호출
    res = ka._url_fetch(API_URL, tr_id, tr_cont, params)

    if res.isOK():
        if hasattr(res.getBody(), 'output'):
            output_data = res.getBody().output
            if not isinstance(output_data, list):
                output_data = [output_data]
            current_data = pd.DataFrame(output_data)
        else:
            current_data = pd.DataFrame()
            
        if dataframe is not None:
            dataframe = pd.concat([dataframe, current_data], ignore_index=True)
        else:
            dataframe = current_data
            
        tr_cont = res.getHeader().tr_cont
        
        if tr_cont == "M":
            logger.info("Calling next page...")
            ka.smart_sleep()
            return udrl_asset_price(
                fid_cond_mrkt_div_code,
                fid_cond_scr_div_code,
                fid_mrkt_cls_code,
                fid_input_iscd,
                fid_unas_input_iscd,
                fid_vol_cnt,
                fid_trgt_exls_cls_code,
                fid_input_price_1,
                fid_input_price_2,
                fid_input_vol_1,
                fid_input_vol_2,
                fid_input_rmnn_dynu_1,
                fid_input_rmnn_dynu_2,
                fid_option,
                fid_input_option_1,
                fid_input_option_2,
                "N", dataframe, depth + 1, max_depth
            )
        else:
            logger.info("Data fetch complete.")
            return dataframe
    else:
        logger.error("API call failed: %s - %s", res.getErrorCode(), res.getErrorMessage())
        res.printError(API_URL)
        return pd.DataFrame()
