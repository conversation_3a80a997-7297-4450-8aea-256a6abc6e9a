# 한국투자증권 OpenAPI 코드 컨벤션


## 정의
- 한줄호출함수: 사용자 코드에 import 해서 한줄로 API를 실행할 수 있도록 만들어 주는 함수들이 담긴 파일이다
- 체크함수: 한줄호출함수 파일을 import하여, API를 실행한 후 결과 값을 출력하는 파일을 테스트 파일


## 단어에 대한 정의
- 1개의 용어에 대해서는 1개의 단어를 사용함으로써, 통일성을 유지하고, LLM이 혼란스럽지 않도록 사용한다.
- 동음이의어, 이음동의어와 같은 문맥상 파악이 필요한 단어를 최대한 지양한다.


## 네이밍 컨벤션
- 네이밍 파이썬 기본 규칙에 벗어나지 않으며, 역할과 의미가 명확해야 한다.
- 널리 알려진 축약어( URL,ID 등) 외에는 축약어와 모호한 이름은 사용하지 않는다.
- 형식
    - 모듈 : snake_case
    - 변수: snake_case
    - 함수 : snake_case
    - 클래스 : PascalCase
    - 상수 : UPPER_SNAKE_CASE


## 폴더 명명 규칙
-  API 주소에서 차용하여 생성
```text
url: /uapi/domestic-stock/v1/quotations/comp-program-trade-daily
폴더이름: domestic_stock
```
- 웹소켓은 URL 이 없으므로, 홈페이의 분류명에 맞추어 적용


## 파일 명명 규칙
1. Rest API 한줄호출함수 파일 명명 규칙
- 규칙 : API 주소에서 차용
- 예시
```text
url: /uapi/domestic-stock/v1/quotations/comp-program-trade-daily
폴더이름: comp_program_trade_daily
파일이름: comp_program_trade_daily.py
```

2. Websocket 한줄호출함수 파일 명명 규칙
- 규칙 : 웹소켓 함수 직역과 함께, 유사한 이름의 RestAPI를 참고하여 생성

3. 테스트 파일 명명 규칙
- 규칙 : "chk_한줄호출함수 파일이름.py"
- 예시 
```text
한줄호출함수 파일이름: comp_program_trade_daily.py

체크함수 : chk_comp_program_trade_daily.py
```


## Docstring 작성
- 코드 블록의 목적, 인자, 반환 값, 예외등을 상세히 기술
- Google, Sphinx, NumPy 등 널리 사용되는 Docstring 형식을 따라야 한다.
- 예제 코드를 포함해야 한다.


## 주석
- 파일 상단에 모듈 전체의 목적과 주요 기능, 다른 모듈과의 관계, 주요 구성 요소와 그들간의 상호작용 방식을 설명하는 주석 필요 (인코딩, 작성 시각과 작성자)
- 자연어를 기반으로 학습되므로, 가급적 완전한 문장 형태의 자연스러운 설명을 사용 할것 (축약되거나 암호를 사용하지 말것)
- 코드 자체는 “무엇”을 하는지 자체로 명확해야하며, 주석은 “왜” 그렇게 작성했는지를 적어야함 (복잡한 로직, 비직관적인 해결 방법, 특정 디자인 결정의 배경 등)
    - 함수/클래스 : 해당 코드 블록이 무엇을 하는지 설명
    - 매개변수 : 이름,타입,  역할, 필수 여부, 기본 값 등을 상세 기술
    - 반환 값 : 무엇을 반환하는지, 타입
    - 예외처리 : 어떤 상황에서 어떤 예외가 발생할 수 있는지 명시
    - 사용예시 : 실제 코드를 사용하여, 어떻게 사용하는지를 보여주는 예시
    - 사전/사후 조건 : 함수 실행 전후에 보장되어야 하는 조건을 명시
- Input, Ouput 파라미터에 대체 가능한 옵션들에 대한 설명을 추가 
    - 파라미터는 Request Header/Query/Body, Response Header/Body 총 5개로 구성되어 있음
    - 각각을 Class화하고 타입을 명시적으로 선언, 필수값은 일반 타입이지만, 선택 값은 Optional을 사용
- 코드가 변경되면 주석을 변경
- TODO, FIXME 와 같은 태그를 활용하여 개선 혹은 수정할 내용을 명시


## 코드의 모듈화 및 단일 책임 원칙
- 함수와 클래스는 가능한 한 작고, 하나의 명확한 기능만을 갖도록 설계
- 재사용성을 함께 제공하면서, LLM이 독립적으로 이해하는 데 도움이 된다.
- 긴 함수/클래스는 지양한다.
- 추상화 계층이나, 복잡한 디자인 패턴은 LLM이 이해하기 어렵게 만들 수 있으므로, 직관적인 코드를 넣을 것
- 장황하더라도 명확한 코드가 좋음 (한줄 마법은 지양)


## 설정 관리
- API 키, 경로, 임계 값등 자주 변경되거나, 환경에 따라 달라지는 설정은 코드에서 분리 
    - .env
    - config.py
    - etc..


## import 정리
- Wildcard는 지양하며, 필요한 것만 명시적으로 import 할 것
- 라이브러리 from에 따라 그룹화하고, 알파벳순으로 정렬
- 라이브러리 import 순서
    1) 표준 라이브러리
    2) 써드파티
    3) 로컬 어플리케이션/Lib


## 변수 선언
- 변수의 타입을 명시적으로 선언한다.
- 함수의 파라미터, 리턴값, 변수의 타입을 명시적으로 선언해야 한다.
- 복잡한 타입 (List, Dict, tuple, Optional, Union, Callable 등)을 활용하여 데이터 구조도를 명확하게 표현한다.


## 에러 처리
- try-except 블록을 사용하되, 구체적인 예외 타입을 명시할 것
- 중요한 이벤트, 에러, 상태 변경 등을 “logging” 을 사용하여 기록 할 것
