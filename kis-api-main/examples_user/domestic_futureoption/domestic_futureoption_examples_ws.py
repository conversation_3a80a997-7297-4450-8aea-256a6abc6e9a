import sys
import logging

import pandas as pd

sys.path.extend(['..', '.'])
import kis_auth as ka
from domestic_futureoption_functions_ws import *

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 인증
ka.auth()
ka.auth_ws()
trenv = ka.getTREnv()

# 웹소켓 선언
kws = ka.KISWebSocket(api_url="/tryitout")

##############################################################################################
# [국내선물옵션] 실시간시세 > 상품선물 실시간체결가[실시간-022]
##############################################################################################

kws.subscribe(request=commodity_futures_realtime_conclusion, data=["165W09"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 상품선물 실시간호가[실시간-023]
##############################################################################################

kws.subscribe(request=commodity_futures_realtime_quote, data=["165W09"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 선물옵션 실시간체결통보[실시간-012]
##############################################################################################

kws.subscribe(request=fuopt_ccnl_notice, data=[trenv.my_htsid])

##############################################################################################
# [국내선물옵션] 실시간시세 > 주식선물 실시간예상체결 [실시간-031]
##############################################################################################

kws.subscribe(request=futures_exp_ccnl, data=["111W07"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 지수선물 실시간체결가[실시간-010]
##############################################################################################

kws.subscribe(request=index_futures_realtime_conclusion, data=["101W09"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 지수선물 실시간호가[실시간-011]
##############################################################################################

kws.subscribe(request=index_futures_realtime_quote, data=["101W09"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 지수옵션 실시간체결가[실시간-014]
##############################################################################################

kws.subscribe(request=index_option_realtime_conclusion, data=["201W08427"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 지수옵션 실시간호가[실시간-015]
##############################################################################################

kws.subscribe(request=index_option_realtime_quote, data=["201W08427"])

##############################################################################################
# [국내선물옵션] 실시간시세 > KRX야간선물 실시간호가 [실시간-065]
##############################################################################################

kws.subscribe(request=krx_ngt_futures_asking_price, data=["101W09"])

##############################################################################################
# [국내선물옵션] 실시간시세 > KRX야간선물 실시간종목체결 [실시간-064]
##############################################################################################

kws.subscribe(request=krx_ngt_futures_ccnl, data=["101W9000"])

##############################################################################################
# [국내선물옵션] 실시간시세 > KRX야간선물 실시간체결통보 [실시간-066]
##############################################################################################

kws.subscribe(request=krx_ngt_futures_ccnl_notice, data=[trenv.my_htsid])

##############################################################################################
# [국내선물옵션] 실시간시세 > KRX야간옵션 실시간호가 [실시간-033]
##############################################################################################

kws.subscribe(request=krx_ngt_option_asking_price, data=["101W9000"])

##############################################################################################
# [국내선물옵션] 실시간시세 > KRX야간옵션 실시간체결가 [실시간-032]
##############################################################################################

kws.subscribe(request=krx_ngt_option_ccnl, data=["101W9000"])

##############################################################################################
# [국내선물옵션] 실시간시세 > KRX야간옵션실시간예상체결 [실시간-034]
##############################################################################################

kws.subscribe(request=krx_ngt_option_exp_ccnl, data=["101W9000"])

##############################################################################################
# [국내선물옵션] 실시간시세 > KRX야간옵션실시간체결통보 [실시간-067]
##############################################################################################

kws.subscribe(request=krx_ngt_option_notice, data=[trenv.my_htsid])

##############################################################################################
# [국내선물옵션] 실시간시세 > 주식옵션 실시간예상체결 [실시간-046]
##############################################################################################

kws.subscribe(request=option_exp_ccnl, data=["339W08088"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 주식선물 실시간체결가 [실시간-029]
##############################################################################################

kws.subscribe(request=stock_futures_realtime_conclusion, data=["111W08"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 주식선물 실시간호가 [실시간-030]
##############################################################################################

kws.subscribe(request=stock_futures_realtime_quote, data=["111W08"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 주식옵션 실시간호가 [실시간-045]
##############################################################################################

kws.subscribe(request=stock_option_asking_price, data=["239W08090"])

##############################################################################################
# [국내선물옵션] 실시간시세 > 주식옵션 실시간체결가 [실시간-044]
##############################################################################################

kws.subscribe(request=stock_option_ccnl, data=["339W08088"])


# 시작
def on_result(ws, tr_id, result, data_info):
    print(result)


kws.start(on_result=on_result)
