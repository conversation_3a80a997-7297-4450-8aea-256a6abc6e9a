import sys
import logging

import pandas as pd

sys.path.extend(['..', '.'])
import kis_auth as ka
from domestic_stock_functions_ws import *

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 인증
ka.auth()
ka.auth_ws()
trenv = ka.getTREnv()

# 웹소켓 선언
kws = ka.KISWebSocket(api_url="/tryitout")

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간호가 (KRX) [실시간-004]
##############################################################################################

kws.subscribe(request=asking_price_krx, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간호가 (NXT)
##############################################################################################

kws.subscribe(request=asking_price_nxt, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간호가 (통합)
##############################################################################################

kws.subscribe(request=asking_price_total, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간체결가(KRX) [실시간-003]
##############################################################################################

kws.subscribe(request=ccnl_krx, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 주식체결통보 [실시간-005]
##############################################################################################

kws.subscribe(request=ccnl_notice, data=[trenv.my_htsid])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간체결가 (NXT)
##############################################################################################

kws.subscribe(request=ccnl_nxt, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간체결가 (통합)
##############################################################################################

kws.subscribe(request=ccnl_total, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간예상체결 (KRX) [실시간-041]
##############################################################################################

kws.subscribe(request=exp_ccnl_krx, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간예상체결 (NXT)
##############################################################################################

kws.subscribe(
    request=exp_ccnl_nxt,
    data=["005930", "000660", "005380"]
)

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간예상체결(통합)
##############################################################################################

kws.subscribe(request=exp_ccnl_total, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내지수 실시간체결 [실시간-026]
##############################################################################################

kws.subscribe(request=index_ccnl, data=["0001", "0128"])

##############################################################################################
# [국내주식] 실시간시세 > 국내지수 실시간예상체결 [실시간-027]
##############################################################################################

kws.subscribe(request=index_exp_ccnl, data=["0001"])

##############################################################################################
# [국내주식] 실시간시세 > 국내지수 실시간프로그램매매 [실시간-028]
##############################################################################################

kws.subscribe(request=index_program_trade, data=["0001", "0128"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 장운영정보 (KRX) [실시간-049]
##############################################################################################

kws.subscribe(request=market_status_krx, data=["417450", "308100"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 장운영정보(NXT)
##############################################################################################

kws.subscribe(request=market_status_nxt, data=["006220"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 장운영정보(통합)
##############################################################################################

kws.subscribe(request=market_status_total, data=["158430"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간회원사 (KRX) [실시간-047]
##############################################################################################

kws.subscribe(request=member_krx, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간회원사 (NXT)
##############################################################################################

kws.subscribe(request=member_nxt, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간회원사 (통합)
##############################################################################################

kws.subscribe(request=member_total, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 시간외 실시간호가 (KRX) [실시간-025]
##############################################################################################

kws.subscribe(request=overtime_asking_price_krx, data=["023460"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 시간외 실시간체결가 (KRX) [실시간-042]
##############################################################################################

kws.subscribe(request=overtime_ccnl_krx, data=["023460", "199480", "462860", "440790", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 시간외 실시간예상체결 (KRX) [실시간-024]
##############################################################################################

kws.subscribe(request=overtime_exp_ccnl_krx, data=["023460"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간프로그램매매 (KRX)  [실시간-048]
##############################################################################################

kws.subscribe(request=program_trade_krx, data=["005930", "000660"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간프로그램매매 (NXT)
##############################################################################################

kws.subscribe(request=program_trade_nxt, data=["032640", "010950"])

##############################################################################################
# [국내주식] 실시간시세 > 국내주식 실시간프로그램매매 (통합)
##############################################################################################

kws.subscribe(request=program_trade_total, data=["005930", "000660"])


# 시작
def on_result(ws, tr_id, result, data_info):
    print(result)


kws.start(on_result=on_result)
