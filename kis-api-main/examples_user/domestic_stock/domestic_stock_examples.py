import sys
import logging

import pandas as pd

sys.path.extend(['..', '.'])
import kis_auth as ka
from domestic_stock_functions import *

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 인증
ka.auth()
trenv = ka.getTREnv()

##############################################################################################
# [국내주식] 기본시세 > 국내주식 시간외잔량 순위[v1_국내주식-093]
##############################################################################################

df = after_hour_balance(fid_input_price_1="", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20176",
                        fid_rank_sort_cls_code="1", fid_div_cls_code="0", fid_input_iscd="0000",
                        fid_trgt_exls_cls_code="0", fid_trgt_cls_code="0", fid_vol_cnt="", fid_input_price_2="")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 대량체결건수 상위[국내주식-107]
##############################################################################################

df = bulk_trans_num(fid_aply_rang_prc_2="", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="11909",
                    fid_input_iscd="0000", fid_rank_sort_cls_code="0", fid_div_cls_code="0", fid_input_price_1="",
                    fid_aply_rang_prc_1="", fid_input_iscd_2="", fid_trgt_exls_cls_code="0", fid_trgt_cls_code="0",
                    fid_vol_cnt="")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 상하한가 포착 [국내주식-190]
##############################################################################################

result = capture_uplowprice(
    fid_cond_mrkt_div_code="J",
    fid_cond_scr_div_code="11300",
    fid_prc_cls_code="0",
    fid_div_cls_code="0",
    fid_input_iscd="0000"
)
print(result)

##############################################################################################
# [국내주식] 업종/기타 > 국내휴장일조회[국내주식-040]
##############################################################################################

result = chk_holiday(bass_dt="20250630")
print(result)

##############################################################################################
# [국내주식] 업종/기타 > 금리 종합(국내채권_금리)[국내주식-155]
##############################################################################################

df1, df2 = comp_interest(fid_cond_mrkt_div_code="I", fid_cond_scr_div_code="20702", fid_div_cls_code="1",
                         fid_div_cls_code1="")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 시세분석 > 프로그램매매 종합현황(일별)[국내주식-115]
##############################################################################################

result = comp_program_trade_daily(
    fid_cond_mrkt_div_code="J",
    fid_mrkt_cls_code="K",
    fid_input_date_1="20250101",
    fid_input_date_2="20250617"
)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 프로그램매매 종합현황(시간) [국내주식-114]
##############################################################################################

result = comp_program_trade_today(fid_cond_mrkt_div_code="J", fid_mrkt_cls_code="K")
print(result)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 신용잔고 상위 [국내주식-109]
##############################################################################################

df1, df2 = credit_balance(fid_cond_scr_div_code="11701", fid_input_iscd="0000", fid_option="2",
                          fid_cond_mrkt_div_code="J", fid_rank_sort_cls_code="0")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 당사 신용가능종목[국내주식-111]
##############################################################################################

df = credit_by_company(fid_rank_sort_cls_code="0", fid_slct_yn="0", fid_input_iscd="0000",
                       fid_cond_scr_div_code="20477", fid_cond_mrkt_div_code="J")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 신용잔고 일별추이[국내주식-110]
##############################################################################################

result1 = daily_credit_balance(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20476",
                               fid_input_iscd="068270", fid_input_date_1="20240508")
print(result1)

##############################################################################################
# [국내주식] 시세분석 > 종목별 일별 대차거래추이 [국내주식-135]
##############################################################################################

result = daily_loan_trans(
    mrkt_div_cls_code="1",
    mksc_shrn_iscd="005930",
    start_date="20240301",
    end_date="20240328"
)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 공매도 일별추이[국내주식-134]
##############################################################################################

result1, result2 = daily_short_sale(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_input_date_1="20240301",
    fid_input_date_2="20240328"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 이격도 순위 [v1_국내주식-095]
##############################################################################################

df = disparity(fid_input_price_2="", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20178", fid_div_cls_code="0",
               fid_rank_sort_cls_code="0", fid_hour_cls_code="5", fid_input_iscd="0000", fid_trgt_cls_code="0",
               fid_trgt_exls_cls_code="0", fid_input_price_1="", fid_vol_cnt="")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 배당률 상위[국내주식-106]
##############################################################################################

df = dividend_rate(cts_area="", gb1="0", upjong="0001", gb2="0", gb3="1", f_dt="20230101", t_dt="20231231", gb4="0")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 종목추정실적[국내주식-187]
##############################################################################################

df1, df2, df3, df4 = estimate_perform(sht_cd="265520")
print(df1)
print(df2)
print(df3)
print(df4)

##############################################################################################
# [국내주식] 기본시세 > 국내주식 장마감 예상체결가[국내주식-120]
##############################################################################################

result = exp_closing_price(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="0001",
    fid_rank_sort_cls_code="0",
    fid_cond_scr_div_code="11173",
    fid_blng_cls_code="0"
)
print(result)

##############################################################################################
# [국내주식] 업종/기타 > 국내주식 예상체결지수 추이[국내주식-121]
##############################################################################################

df = exp_index_trend(fid_mkop_cls_code="1", fid_input_hour_1="", fid_input_iscd="0001", fid_cond_mrkt_div_code="U")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 예상체결가 추이[국내주식-118]
##############################################################################################

output1, output2 = exp_price_trend(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_mkop_cls_code="0"
)
print(output1)
print(output2)

##############################################################################################
# [국내주식] 업종/기타 > 국내주식 예상체결 전체지수[국내주식-122]
##############################################################################################

df1, df2 = exp_total_index(fid_mrkt_cls_code="0", fid_cond_mrkt_div_code="U", fid_cond_scr_div_code="11175",
                           fid_input_iscd="0000", fid_mkop_cls_code="1")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 예상체결 상승_하락상위[v1_국내주식-103]
##############################################################################################

df = exp_trans_updown(fid_rank_sort_cls_code="0", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20182",
                      fid_input_iscd="0000", fid_div_cls_code="0", fid_aply_rang_prc_1="", fid_vol_cnt="", fid_pbmn="",
                      fid_blng_cls_code="0", fid_mkop_cls_code="0")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 대차대조표 [v1_국내주식-078]
##############################################################################################

df = finance_balance_sheet(fid_div_cls_code="0", fid_cond_mrkt_div_code="J", fid_input_iscd="000660")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 재무비율 [v1_국내주식-080]
##############################################################################################

df = finance_financial_ratio(fid_div_cls_code="0", fid_cond_mrkt_div_code="J", fid_input_iscd="000660")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 성장성비율 [v1_국내주식-085]
##############################################################################################

df = finance_growth_ratio(fid_input_iscd="000660", fid_div_cls_code="0", fid_cond_mrkt_div_code="J")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 손익계산서 [v1_국내주식-079]
##############################################################################################

df = finance_income_statement(fid_div_cls_code="0", fid_cond_mrkt_div_code="J", fid_input_iscd="000660")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 기타주요비율[v1_국내주식-082]
##############################################################################################

df = finance_other_major_ratios(fid_input_iscd="000660", fid_div_cls_code="0", fid_cond_mrkt_div_code="J")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 수익성비율[v1_국내주식-081]
##############################################################################################

df = finance_profit_ratio(fid_input_iscd="000660", fid_div_cls_code="0", fid_cond_mrkt_div_code="J")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 재무비율 순위[v1_국내주식-092]
##############################################################################################

df = finance_ratio(fid_trgt_cls_code="0", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20175",
                   fid_input_iscd="0000", fid_div_cls_code="0", fid_input_price_1="", fid_input_price_2="",
                   fid_vol_cnt="", fid_input_option_1="2023", fid_input_option_2="3", fid_rank_sort_cls_code="7",
                   fid_blng_cls_code="0", fid_trgt_exls_cls_code="0")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 안정성비율[v1_국내주식-083]
##############################################################################################

df = finance_stability_ratio(fid_input_iscd="000660", fid_div_cls_code="0", fid_cond_mrkt_div_code="J")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 등락률 순위[v1_국내주식-088]
##############################################################################################

df = fluctuation(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20170", fid_input_iscd="0000",
                 fid_rank_sort_cls_code="0", fid_input_cnt_1="0", fid_prc_cls_code="0", fid_input_price_1="",
                 fid_input_price_2="", fid_vol_cnt="", fid_trgt_cls_code="0", fid_trgt_exls_cls_code="0",
                 fid_div_cls_code="0", fid_rsfl_rate1="", fid_rsfl_rate2="")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 국내기관_외국인 매매종목가집계[국내주식-037]
##############################################################################################

result = foreign_institution_total(
    fid_cond_mrkt_div_code="V",
    fid_cond_scr_div_code="16449",
    fid_input_iscd="0000",
    fid_div_cls_code="0",
    fid_rank_sort_cls_code="0",
    fid_etc_cls_code="0"
)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 종목별 외국계 순매수추이 [국내주식-164]
##############################################################################################

result = frgnmem_pchs_trend(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_input_iscd_2="99999"
)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 외국계 매매종목 가집계 [국내주식-161]
##############################################################################################

result = frgnmem_trade_estimate(
    fid_cond_mrkt_div_code="J",
    fid_cond_scr_div_code="16441",
    fid_input_iscd="0000",
    fid_rank_sort_cls_code="0",
    fid_rank_sort_cls_code_2="0"
)
print(result)

##############################################################################################
# [국내주식] 기본시세 > 회원사 실 시간 매매동향(틱)[국내주식-163]
##############################################################################################

df1, df2 = frgnmem_trade_trend(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20432", fid_input_iscd="005930",
                               fid_input_iscd_2="99999", fid_mrkt_cls_code="A", fid_vol_cnt="1000")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 순위분석 > HTS조회상위20종목[국내주식-214]
##############################################################################################

df = hts_top_view()
print(df)

##############################################################################################
# [국내주식] 주문/계좌 > 투자계좌자산현황조회[v1_국내주식-048]
##############################################################################################

result1, result2 = inquire_account_balance(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 호가/예상체결[v1_국내주식-011]
##############################################################################################

result1, result2 = inquire_asking_price_exp_ccn(env_dv="real", fid_cond_mrkt_div_code="J",
                                                fid_input_iscd="005930")
print(result1)
print(result2)

##############################################################################################
# [국내주식] 주문/계좌 > 주식잔고조회[v1_국내주식-006]
##############################################################################################

result1, result2 = inquire_balance(
    env_dv="real",
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    afhr_flpr_yn="N",
    inqr_dvsn="01",
    unpr_dvsn="01",
    fund_sttl_icld_yn="N",
    fncg_amt_auto_rdpt_yn="N",
    prcs_dvsn="00"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 주문/계좌 > 주식잔고조회_실현손익[v1_국내주식-041]
##############################################################################################

result1, result2 = inquire_balance_rlz_pl(
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    afhr_flpr_yn="N",
    inqr_dvsn="02",
    unpr_dvsn="01",
    fund_sttl_icld_yn="N",
    fncg_amt_auto_rdpt_yn="N",
    prcs_dvsn="01",
    cost_icld_yn="N"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 체결[v1_국내주식-009]
##############################################################################################

result = inquire_ccnl(env_dv="real", fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 신용매수가능조회[v1_국내주식-042]
##############################################################################################

result = inquire_credit_psamount(
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    pdno="005930",
    ord_dvsn="00",
    crdt_type="21",
    cma_evlu_amt_icld_yn="N",
    ovrs_icld_yn="N"
)
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 주식일별주문체결조회[v1_국내주식-005]
##############################################################################################

result1, result2 = inquire_daily_ccld(
    env_dv="real",
    pd_dv="inner",
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    inqr_strt_dt="20220810",
    inqr_end_dt="20220810",
    sll_buy_dvsn_cd="00",
    inqr_dvsn="00",
    pdno="005930",
    ccld_dvsn="00",
    inqr_dvsn_3="00"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 국내주식업종기간별시세(일_주_월_년)[v1_국내주식-021]
##############################################################################################

df1, df2 = inquire_daily_indexchartprice(fid_cond_mrkt_div_code="U", fid_input_iscd="0001", fid_input_date_1="20250101",
                                         fid_input_date_2="20250131", fid_period_div_code="D", env_dv="real")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 기본시세 > 국내주식기간별시세(일/주/월/년)[v1_국내주식-016]
##############################################################################################

result1, result2 = inquire_daily_itemchartprice(
    env_dv="real",
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_input_date_1="20220101",
    fid_input_date_2="20220809",
    fid_period_div_code="D",
    fid_org_adj_prc="1"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 시간외일자별주가[v1_국내주식-026]
##############################################################################################

result1, result2 = inquire_daily_overtimeprice(env_dv="real", fid_cond_mrkt_div_code="J",
                                               fid_input_iscd="005930")
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 일자별[v1_국내주식-010]
##############################################################################################

result = inquire_daily_price(env_dv="real", fid_cond_mrkt_div_code="J", fid_input_iscd="005930",
                             fid_period_div_code="D", fid_org_adj_prc="1")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 종목별일별매수매도체결량 [v1_국내주식-056]
##############################################################################################

result1, result2 = inquire_daily_trade_volume(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_period_div_code="D"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] ELW시세 > ELW 현재가 시세 [v1_국내주식-014]
##############################################################################################

df = inquire_elw_price(fid_cond_mrkt_div_code="W", fid_input_iscd="123456", env_dv="real")
print(df)

##############################################################################################
# [국내주식] 업종/기타 > 국내업종 구분별전체시세[v1_국내주식-066]
##############################################################################################

df1, df2 = inquire_index_category_price(fid_cond_mrkt_div_code="U", fid_input_iscd="0001",
                                        fid_cond_scr_div_code="20214", fid_mrkt_cls_code="K", fid_blng_cls_code="0")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 업종/기타 > 국내업종 일자별지수 [v1_국내주식-065]
##############################################################################################

df1, df2 = inquire_index_daily_price(fid_period_div_code="D", fid_cond_mrkt_div_code="U", fid_input_iscd="0001",
                                     fid_input_date_1="20250101")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 업종/기타 > 국내업종 현재지수 [v1_국내주식-063]
##############################################################################################

df = inquire_index_price(fid_cond_mrkt_div_code="U", fid_input_iscd="0001")
print(df)

##############################################################################################
# [국내주식] 업종/기타 > 국내업종 시간별지수(초)[국내주식-064]
##############################################################################################

df = inquire_index_tickprice(fid_input_iscd="0001", fid_cond_mrkt_div_code="U")
print(df)

##############################################################################################
# [국내주식] 업종/기타 > 국내업종 시간별지수(분)[국내주식-119]
##############################################################################################

df = inquire_index_timeprice(fid_input_hour_1="60", fid_input_iscd="0001", fid_cond_mrkt_div_code="U")
print(df)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 투자자[v1_국내주식-012]
##############################################################################################

result = inquire_investor(env_dv="real", fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 시장별 투자자매매동향(일별) [국내주식-075]
##############################################################################################

result = inquire_investor_daily_by_market(
    fid_cond_mrkt_div_code="U",
    fid_input_iscd="0001",
    fid_input_date_1="20240517",
    fid_input_iscd_1="KSP",
    fid_input_date_2="20250701",
    fid_input_iscd_2="0001",
)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 시장별 투자자매매동향(시세)[v1_국내주식-074]
##############################################################################################

result = inquire_investor_time_by_market(fid_input_iscd="999", fid_input_iscd_2="S001")
print(result)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 회원사[v1_국내주식-013]
##############################################################################################

result = inquire_member(env_dv="real", fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 주식현재가 회원사 종목매매동향 [국내주식-197]
##############################################################################################

result = inquire_member_daily(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_input_iscd_2="00003",
    fid_input_date_1="20240501",
    fid_input_date_2="20240624"
)
print(result)

##############################################################################################
# [국내주식] 기본시세 > 국내주식 시간외호가[국내주식-077]
##############################################################################################

result = inquire_overtime_asking_price(fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 기본시세 > 국내주식 시간외현재가[국내주식-076]
##############################################################################################

result = inquire_overtime_price(fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 기간별손익일별합산조회[v1_국내주식-052]
##############################################################################################

result1, result2 = inquire_period_profit(
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    inqr_strt_dt="20230101",
    inqr_end_dt="20240301",
    sort_dvsn="00",
    inqr_dvsn="00",
    cblc_dvsn="00"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 주문/계좌 > 기간별매매손익현황조회[v1_국내주식-060]
##############################################################################################

result1, result2 = inquire_period_trade_profit(
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    sort_dvsn="02",
    inqr_strt_dt="20230216",
    inqr_end_dt="20240301",
    cblc_dvsn="00"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 시세[v1_국내주식-008]
##############################################################################################

result = inquire_price(env_dv="real", fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 시세2[v1_국내주식-054]
##############################################################################################

result = inquire_price_2(fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 매수가능조회[v1_국내주식-007]
##############################################################################################

result = inquire_psbl_order(env_dv="real", cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, pdno="005930",
                            ord_unpr="55000",
                            ord_dvsn="01", cma_evlu_amt_icld_yn="N", ovrs_icld_yn="N")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 주식정정취소가능주문조회[v1_국내주식-004]
##############################################################################################

result = inquire_psbl_rvsecncl(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, inqr_dvsn_1="1", inqr_dvsn_2="0")
print(result)

##############################################################################################
# [국내주식] 기본시세 > 주식당일분봉조회[v1_국내주식-022]
##############################################################################################

result1, result2 = inquire_time_dailychartprice(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_input_hour_1="130000",
    fid_input_date_1="20241023"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 업종 분봉조회[v1_국내주식-045]
##############################################################################################

df1, df2 = inquire_time_indexchartprice(fid_cond_mrkt_div_code="U", fid_etc_cls_code="0", fid_input_iscd="0001",
                                        fid_input_hour_1="60", fid_pw_data_incu_yn="Y")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 기본시세 > 시장별 투자자매매동향(일별) [국내주식-075]
##############################################################################################

output1, output2 = inquire_time_itemchartprice(env_dv="real", fid_cond_mrkt_div_code="J",
                                               fid_input_iscd="005930", fid_input_hour_1="093000",
                                               fid_pw_data_incu_yn="Y")
print(output1)
print(output2)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 당일시간대별체결[v1_국내주식-023]
##############################################################################################

result1, result2 = inquire_time_itemconclusion(
    env_dv="real",
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_input_hour_1="115959"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 주식현재가 시간외시간별체결[v1_국내주식-025]
##############################################################################################

result1, result2 = inquire_time_overtimeconclusion(env_dv="real", fid_cond_mrkt_div_code="J",
                                                   fid_input_iscd="005930", fid_hour_cls_code="1")
print(result1)
print(result2)

##############################################################################################
# [국내주식] 기본시세 > 변동성완화장치(VI) 현황[v1_국내주식-055]
##############################################################################################

df = inquire_vi_status(fid_div_cls_code="0", fid_cond_scr_div_code="20139", fid_mrkt_cls_code="0", fid_input_iscd="",
                       fid_rank_sort_cls_code="0", fid_input_date_1="20250101", fid_trgt_cls_code="",
                       fid_trgt_exls_cls_code="")
print(df)

##############################################################################################
# [국내주식] 주문/계좌 > 주식통합증거금 현황 [국내주식-191]
##############################################################################################

result = intgr_margin(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, cma_evlu_amt_icld_yn="N", wcrc_frcr_dvsn_cd="01",
                      fwex_ctrt_frcr_dvsn_cd="01")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 관심종목 그룹조회 [국내주식-204]
##############################################################################################

result = intstock_grouplist(type="1", fid_etc_cls_code="00", user_id=trenv.my_htsid)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 관심종목(멀티종목) 시세조회 [국내주식-205]
##############################################################################################

result = intstock_multprice(
    fid_cond_mrkt_div_code_1="J",
    fid_input_iscd_1="419530",
    fid_cond_mrkt_div_code_2="J",
    fid_input_iscd_2="092070"
)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 관심종목 그룹별 종목조회 [국내주식-203]
##############################################################################################

result1, result2 = intstock_stocklist_by_group(
    type="1",
    user_id=trenv.my_htsid,
    inter_grp_code="001",
    fid_etc_cls_code="4"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 증권사별 투자의견[국내주식-189]
##############################################################################################

df = invest_opbysec(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="16634", fid_input_iscd="005930",
                    fid_div_cls_code="0", fid_input_date_1="20250101", fid_input_date_2="20250131")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 국내주식 종목투자의견[국내주식-188]
##############################################################################################

df = invest_opinion(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="16633", fid_input_iscd="005930",
                    fid_input_date_1="20231113", fid_input_date_2="20240513")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 프로그램매매 투자자매매동향(당일) [국내주식-116]
##############################################################################################

result = investor_program_trade_today(mrkt_div_cls_code="1")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 종목별 외인기관 추정가집계[v1_국내주식-046]
##############################################################################################

result = investor_trend_estimate(mksc_shrn_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(무상증자일정)[국내주식-144]
##############################################################################################

df = ksdinfo_bonus_issue(cts="", f_dt="20250101", t_dt="20250131", sht_cd="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(자본감소일정) [국내주식-149]
##############################################################################################

df = ksdinfo_cap_dcrs(cts="", f_dt="20250101", t_dt="20250131", sht_cd="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(배당일정)[국내주식-145]
##############################################################################################

df = ksdinfo_dividend(cts="", gb1="0", f_dt="20250101", t_dt="20250131", sht_cd="", high_gb="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(실권주일정)[국내주식-152]
##############################################################################################

df = ksdinfo_forfeit(sht_cd="", t_dt="20250131", f_dt="20250101", cts="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(상장정보일정)[국내주식-150]
##############################################################################################

df = ksdinfo_list_info(sht_cd="", t_dt="20250131", f_dt="20250101", cts="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(의무예치일정) [국내주식-153]
##############################################################################################

df = ksdinfo_mand_deposit(t_dt="20250131", sht_cd="", f_dt="20250101", cts="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(합병_분할일정)[국내주식-147]
##############################################################################################

df = ksdinfo_merger_split(cts="", f_dt="20230101", t_dt="20231231", sht_cd="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(유상증자일정)[국내주식-143]
##############################################################################################

df = ksdinfo_paidin_capin(cts="", gb1="1", f_dt="20250101", t_dt="20250101", sht_cd="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(공모주청약일정)[국내주식-151]
##############################################################################################

df = ksdinfo_pub_offer(sht_cd="", cts="", f_dt="20250101", t_dt="20250131")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(주식매수청구일정)[국내주식-146]
##############################################################################################

df = ksdinfo_purreq(sht_cd="", t_dt="20250131", f_dt="20250101", cts="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(액면교체일정)[국내주식-148]
##############################################################################################

df = ksdinfo_rev_split(sht_cd="", cts="", f_dt="20250101", t_dt="20250131", market_gb="0")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 예탁원정보(주주총회일정)[국내주식-154]
##############################################################################################

df = ksdinfo_sharehld_meet(cts="", f_dt="20230101", t_dt="20231231", sht_cd="")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 당사 대주가능 종목 [국내주식-195]
##############################################################################################

df1, df2 = lendable_by_company(excg_dvsn_cd="00", pdno="", thco_stln_psbl_yn="Y", inqr_dvsn_1="0", ctx_area_fk200="",
                               ctx_area_nk100="")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 시가총액 상위 [v1_국내주식-091]
##############################################################################################

df = market_cap(fid_input_price_2="1000000", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20174",
                fid_div_cls_code="0", fid_input_iscd="0000", fid_trgt_cls_code="0", fid_trgt_exls_cls_code="0",
                fid_input_price_1="50000", fid_vol_cnt="1000")
print(df)

##############################################################################################
# [국내주식] 업종/기타 > 국내선물 영업일조회 [국내주식-160]
##############################################################################################

result = market_time()
print(result)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 시장가치 순위[v1_국내주식-096]
##############################################################################################

df = market_value(fid_trgt_cls_code="0", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20179",
                  fid_input_iscd="0000", fid_div_cls_code="0", fid_input_price_1="", fid_input_price_2="",
                  fid_vol_cnt="", fid_input_option_1="2023", fid_input_option_2="0", fid_rank_sort_cls_code="23",
                  fid_blng_cls_code="0", fid_trgt_exls_cls_code="0")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 국내 증시자금 종합 [국내주식-193]
##############################################################################################

result = mktfunds(fid_input_date_1="")
print(result)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 신고_신저근접종목 상위[v1_국내주식-105]
##############################################################################################

df = near_new_highlow(fid_aply_rang_vol="100", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20187",
                      fid_div_cls_code="0", fid_input_cnt_1="0", fid_input_cnt_2="10", fid_prc_cls_code="0",
                      fid_input_iscd="0000", fid_trgt_cls_code="0", fid_trgt_exls_cls_code="0",
                      fid_aply_rang_prc_1="10000", fid_aply_rang_prc_2="50000")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 종합 시황/공시(제목) [국내주식-141]
##############################################################################################

df = news_title(fid_news_ofer_entp_code="", fid_cond_mrkt_cls_code="", fid_input_iscd="", fid_titl_cntt="",
                fid_input_date_1="", fid_input_hour_1="", fid_rank_sort_cls_code="", fid_input_srno="")
print(df)

##############################################################################################
# [국내주식] 주문/계좌 > 주식주문(현금)[v1_국내주식-001]
##############################################################################################

result = order_cash(env_dv="real", ord_dv="sell", cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, pdno="005930",
                    ord_dvsn="00", ord_qty="1", ord_unpr="2000", excg_id_dvsn_cd="SOR")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 주식주문(신용)[v1_국내주식-002]
##############################################################################################

result = order_credit(
    ord_dv="buy",
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    pdno="005930",
    crdt_type="21",
    loan_dt="20220810",
    ord_dvsn="00",
    ord_qty="1",
    ord_unpr="55000"
)
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 주식예약주문[v1_국내주식-017]
##############################################################################################

result = order_resv(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, pdno="005930", ord_qty="1", ord_unpr="55000",
                    sll_buy_dvsn_cd="02", ord_dvsn_cd="00", ord_objt_cblc_dvsn_cd="10")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 주식예약주문조회[v1_국내주식-020]
##############################################################################################

result = order_resv_ccnl(
    rsvn_ord_ord_dt="20220729",
    rsvn_ord_end_dt="20220810",
    tmnl_mdia_kind_cd="00",
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    prcs_dvsn_cd="0",
    cncl_yn="Y"
)
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 주식예약주문정정취소[v1_국내주식-018,019]
##############################################################################################

result = order_resv_rvsecncl(
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    rsvn_ord_seq="88793",
    rsvn_ord_orgno="001",
    rsvn_ord_ord_dt="20250113",
    ord_type="cancel",
    pdno="005930",
    ord_qty="2",
    ord_unpr="55000",
    sll_buy_dvsn_cd="02",
    ord_dvsn_cd="00",
    ord_objt_cblc_dvsn_cd="10"
)
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 주식주문(정정취소)[v1_국내주식-003]
##############################################################################################

result = order_rvsecncl(
    env_dv="real",
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    krx_fwdg_ord_orgno="06010",
    orgn_odno="0000002101",
    ord_dvsn="00",
    rvse_cncl_dvsn_cd="02",
    ord_qty="1",
    ord_unpr="55000",
    qty_all_ord_yn="Y",
    excg_id_dvsn_cd="KRX"
)
print(result)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 시간외예상체결등락률 [국내주식-140]
##############################################################################################

result = overtime_exp_trans_fluct(
    fid_cond_mrkt_div_code="J",
    fid_cond_scr_div_code="11186",
    fid_input_iscd="0000",
    fid_rank_sort_cls_code="0",
    fid_div_cls_code="0"
)
print(result)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 시간외등락율순위[국내주식-138]
##############################################################################################

df1, df2 = overtime_fluctuation(fid_cond_mrkt_div_code="J", fid_mrkt_cls_code="", fid_cond_scr_div_code="20234",
                                fid_input_iscd="0000", fid_div_cls_code="1", fid_input_price_1="", fid_input_price_2="",
                                fid_vol_cnt="", fid_trgt_cls_code="", fid_trgt_exls_cls_code="")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 국내주식 > 국내주식 시간외거래량순위[국내주식-139]
##############################################################################################

df1, df2 = overtime_volume(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20235", fid_input_iscd="0000",
                           fid_rank_sort_cls_code="0", fid_input_price_1="", fid_input_price_2="", fid_vol_cnt="",
                           fid_trgt_cls_code="", fid_trgt_exls_cls_code="")
print(df1)
print(df2)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 매물대/거래비중 [국내주식-196]
##############################################################################################

result1, result2 = pbar_tratio(
    fid_cond_mrkt_div_code="J",
    fid_input_iscd="005930",
    fid_cond_scr_div_code="20113"
)
print(result1)
print(result2)

##############################################################################################
# [국내주식] 주문/계좌 > 퇴직연금 잔고조회[v1_국내주식-036]
##############################################################################################

result1, result2 = pension_inquire_balance(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, acca_dvsn_cd="00",
                                           inqr_dvsn="00")
print(result1)
print(result2)

##############################################################################################
# [국내주식] 주문/계좌 > 퇴직연금 미체결내역[v1_국내주식-033]
##############################################################################################

result = pension_inquire_daily_ccld(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, user_dvsn_cd="%%",
                                    sll_buy_dvsn_cd="00",
                                    ccld_nccs_dvsn="%%", inqr_dvsn_3="00")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 퇴직연금 예수금조회[v1_국내주식-035]
##############################################################################################

result = pension_inquire_deposit(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, acca_dvsn_cd="00")
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 퇴직연금 체결기준잔고[v1_국내주식-032]
##############################################################################################

result1, result2 = pension_inquire_present_balance(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod,
                                                   user_dvsn_cd="00"
                                                   )
print(result1)
print(result2)

##############################################################################################
# [국내주식] 주문/계좌 > 퇴직연금 매수가능조회[v1_국내주식-034]
##############################################################################################

result = pension_inquire_psbl_order(
    cano=trenv.my_acct,
    acnt_prdt_cd=trenv.my_prod,
    pdno="069500",
    acca_dvsn_cd="00",
    cma_evlu_amt_icld_yn="Y",
    ord_unpr="30800",
    ord_dvsn="00"
)
print(result)

##############################################################################################
# [국내주식] 주문/계좌 > 기간별계좌권리현황조회 [국내주식-211]
##############################################################################################

result = period_rights(inqr_dvsn="03", cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, inqr_strt_dt="20250101",
                       inqr_end_dt="20250103")
print(result)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 우선주_괴리율 상위[v1_국내주식-094]
##############################################################################################

df = prefer_disparate_ratio(fid_vol_cnt="1000", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20177",
                            fid_div_cls_code="0", fid_input_iscd="0000", fid_trgt_cls_code="0",
                            fid_trgt_exls_cls_code="0", fid_input_price_1="10000", fid_input_price_2="50000")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 수익자산지표 순위[v1_국내주식-090]
##############################################################################################

df = profit_asset_index(fid_cond_mrkt_div_code="J", fid_trgt_cls_code="0", fid_cond_scr_div_code="20173",
                        fid_input_iscd="0000", fid_div_cls_code="0", fid_input_price_1="", fid_input_price_2="",
                        fid_vol_cnt="", fid_input_option_1="2023", fid_input_option_2="0", fid_rank_sort_cls_code="0",
                        fid_blng_cls_code="0", fid_trgt_exls_cls_code="0")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 종목별 프로그램매매추이(체결)[v1_국내주식-044]
##############################################################################################

result = program_trade_by_stock(fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 종목별 프로그램매매추이(일별) [국내주식-113]
##############################################################################################

result = program_trade_by_stock_daily(fid_cond_mrkt_div_code="J", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 종목조건검색조회 [국내주식-039]
##############################################################################################

result = psearch_result(user_id=trenv.my_htsid, seq="0")
print(result)

##############################################################################################
# [국내주식] 시세분석 > 종목조건검색 목록조회[국내주식-038]
##############################################################################################

result = psearch_title(user_id=trenv.my_htsid)
print(result)

##############################################################################################
# [국내주식] 기본시세 > 국내주식 호가잔량 순위[국내주식-089]
##############################################################################################

df = quote_balance(fid_vol_cnt="1000", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20172", fid_input_iscd="0001",
                   fid_rank_sort_cls_code="0", fid_div_cls_code="0", fid_trgt_cls_code="0", fid_trgt_exls_cls_code="0",
                   fid_input_price_1="50000", fid_input_price_2="100000")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 상품기본조회[v1_국내주식-029]
##############################################################################################

df = search_info(pdno="000660", prdt_type_cd="300")
print(df)

##############################################################################################
# [국내주식] 종목정보 > 주식기본조회[v1_국내주식-067]
##############################################################################################

df = search_stock_info(prdt_type_cd="300", pdno="005930")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 공매도 상위종목[국내주식-133]
##############################################################################################

df = short_sale(fid_aply_rang_vol="", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20482", fid_input_iscd="0000",
                fid_period_div_code="D", fid_input_cnt_1="0", fid_trgt_exls_cls_code="", fid_trgt_cls_code="",
                fid_aply_rang_prc_1="0", fid_aply_rang_prc_2="1000000")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 관심종목등록 상위[v1_국내주식-102]
##############################################################################################

df = top_interest_stock(fid_input_iscd_2="000000", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20180",
                        fid_input_iscd="0000", fid_trgt_cls_code="0", fid_trgt_exls_cls_code="0", fid_input_price_1="0",
                        fid_input_price_2="0", fid_vol_cnt="0", fid_div_cls_code="0", fid_input_cnt_1="1")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 당사매매종목 상위[v1_국내주식-104]
##############################################################################################

df = traded_by_company(fid_trgt_exls_cls_code="0", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20186",
                       fid_div_cls_code="0", fid_rank_sort_cls_code="1", fid_input_date_1="20230101",
                       fid_input_date_2="20231231", fid_input_iscd="0000", fid_trgt_cls_code="0",
                       fid_aply_rang_vol="100", fid_aply_rang_prc_2="100000", fid_aply_rang_prc_1="50000")
print(df)

##############################################################################################
# [국내주식] 시세분석 > 국내주식 체결금액별 매매비중 [국내주식-192]
##############################################################################################

result = tradprt_byamt(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="11119", fid_input_iscd="005930")
print(result)

##############################################################################################
# [국내주식] 순위분석 > 국내주식 체결강도 상위[v1_국내주식-101]
##############################################################################################

df = volume_power(fid_trgt_exls_cls_code="0", fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20168",
                  fid_input_iscd="0000", fid_div_cls_code="0", fid_input_price_1="0", fid_input_price_2="1000000",
                  fid_vol_cnt="0", fid_trgt_cls_code="0")
print(df)

##############################################################################################
# [국내주식] 순위분석 > 거래량순위[v1_국내주식-047]
##############################################################################################

df = volume_rank(fid_cond_mrkt_div_code="J", fid_cond_scr_div_code="20171", fid_input_iscd="0000", fid_div_cls_code="0",
                 fid_blng_cls_code="0", fid_trgt_cls_code="*********", fid_trgt_exls_cls_code="0000000000",
                 fid_input_price_1="0", fid_input_price_2="1000000", fid_vol_cnt="100000", fid_input_date_1="")
print(df)

##############################################################################################
# [국내주식] 주문/계좌 > 매도가능수량조회 [국내주식-165]
##############################################################################################

df = inquire_psbl_sell(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, pdno="000660")
print(df)
