import sys
import logging

import pandas as pd

sys.path.extend(['..', '.'])
import kis_auth as ka
from domestic_bond_functions import *

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 인증
ka.auth()
trenv = ka.getTREnv()

##############################################################################################
# [장내채권] 기본시세 > 장내채권 평균단가조회 [국내채권-158]
##############################################################################################

df1, df2, df3 = avg_unit(inqr_strt_dt="20250101", inqr_end_dt="20250131", pdno="KR2033022D33", prdt_type_cd="302", vrfc_kind_cd="00")
print(df1)
print(df2)
print(df3)

##############################################################################################
# [장내채권] 주문/계좌 > 장내채권 매수주문 [국내주식-124]
##############################################################################################

result = buy(
            cano=trenv.my_acct,  # 종합계좌번호
            acnt_prdt_cd=trenv.my_prod,  # 계좌상품코드
            pdno="KR6095572D81",  # 상품번호
            ord_qty2="10",  # 주문수량
            bond_ord_unpr="9900",  # 채권주문단가
            samt_mket_ptci_yn="N",  # 소액시장참여여부
            bond_rtl_mket_yn="N",  # 채권소매시장여부
            idcr_stfno="",  # 유치자직원번호
            mgco_aptm_odno="",  # 운용사지정주문번호
            ord_svr_dvsn_cd="0",  # 주문서버구분코드
            ctac_tlno="",  # 연락전화번호
        )
print(result)

##############################################################################################
# [장내채권] 기본시세 > 장내채권현재가(호가) [국내주식-132]
##############################################################################################

df = inquire_asking_price(fid_cond_mrkt_div_code="B", fid_input_iscd="KR2033022D33")
print(df)

##############################################################################################
# [장내채권] 주문/계좌 > 장내채권 잔고조회 [국내주식-198]
##############################################################################################

df = inquire_balance(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, inqr_cndt="00", pdno="", buy_dt="")
print(df)

##############################################################################################
# [장내채권] 기본시세 > 장내채권현재가(체결) [국내주식-201]
##############################################################################################

df = inquire_ccnl(fid_cond_mrkt_div_code="B", fid_input_iscd="KR2033022D33")
print(df)

##############################################################################################
# [장내채권] 주문/계좌 > 장내채권 일별체결조회 [국내주식-127]
##############################################################################################

df1, df2 = inquire_daily_ccld(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, inqr_strt_dt="20250601", inqr_end_dt="20250630", sll_buy_dvsn_cd="%", sort_sqn_dvsn="01", pdno="", nccs_yn="N", ctx_area_nk200="", ctx_area_fk200="")
print(df1)
print(df2)

##############################################################################################
# [장내채권] 기본시세 > 장내채권 기간별시세(일) [국내주식-159]
##############################################################################################

df = inquire_daily_itemchartprice(fid_cond_mrkt_div_code="B", fid_input_iscd="KR2033022D33")
print(df)

##############################################################################################
# [장내채권] 기본시세 > 장내채권현재가(일별) [국내주식-202]
##############################################################################################

df = inquire_daily_price(fid_cond_mrkt_div_code="B", fid_input_iscd="KR2033022D33")
print(df)

##############################################################################################
# [장내채권] 기본시세 > 장내채권현재가(시세) [국내주식-200]
##############################################################################################

df = inquire_price(fid_cond_mrkt_div_code="B", fid_input_iscd="KR2033022D33")
print(df)

##############################################################################################
# [장내채권] 주문/계좌 > 장내채권 매수가능조회 [국내주식-199]
##############################################################################################

df = inquire_psbl_order(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, pdno="KR2033022D33", bond_ord_unpr="1000")
print(df)

##############################################################################################
# [장내채권] 주문/계좌 > 채권정정취소가능주문조회 [국내주식-126]
##############################################################################################

df = inquire_psbl_rvsecncl(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, ord_dt="20250601", odno="", ctx_area_fk200="", ctx_area_nk200="")
print(df)

##############################################################################################
# [장내채권] 기본시세 > 장내채권 발행정보 [국내주식-156]
##############################################################################################

df = issue_info(pdno="KR6449111CB8", prdt_type_cd="302")
print(df)

##############################################################################################
# [장내채권] 주문/계좌 > 장내채권 정정취소주문 [국내주식-125]
##############################################################################################

result = order_rvsecncl(
            cano=trenv.my_acct,
            acnt_prdt_cd=trenv.my_prod,
            pdno="KR6095572D81",
            orgn_odno="0004357900",  # 실제 테스트 시 유효한 원주문번호로 변경해야 합니다.
            ord_qty2="1",  # 정정/취소 수량
            bond_ord_unpr="10470",  # 정정 단가
            qty_all_ord_yn="Y",  # 잔량 전부 주문 여부
            rvse_cncl_dvsn_cd="01",  # 01: 정정, 02: 취소
            mgco_aptm_odno="",
            ord_svr_dvsn_cd="0",
            ctac_tlno="",
        )
print(result)

##############################################################################################
# [장내채권] 기본시세 > 장내채권 기본조회 [국내주식-129]
##############################################################################################

df = search_bond_info(pdno="KR2033022D33", prdt_type_cd="302")
print(df)

##############################################################################################
# [장내채권] 주문/계좌 > 장내채권 매도주문 [국내주식-123]
##############################################################################################

result = sell(
            cano=trenv.my_acct,  # 종합계좌번호
            acnt_prdt_cd=trenv.my_prod,  # 계좌상품코드
            ord_dvsn="01",  # 주문구분
            pdno="KR6095572D81",  # 상품번호
            ord_qty2="1",  # 주문수량
            bond_ord_unpr="10000.0",  # 채권주문단가
            sprx_yn="N",  # 분리과세여부
            buy_dt="",  # 매수일자
            buy_seq="",  # 매수순번
            samt_mket_ptci_yn="N",  # 소액시장참여여부
            sll_agco_opps_sll_yn="N",  # 매도대행사반대매도여부
            bond_rtl_mket_yn="N",  # 채권소매시장여부
            mgco_aptm_odno="",  # 운용사지정주문번호
            ord_svr_dvsn_cd="0",  # 주문서버구분코드
            ctac_tlno="",  # 연락전화번호
        )
print(result)

