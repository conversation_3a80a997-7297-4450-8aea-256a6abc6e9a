import sys
import logging

import pandas as pd

sys.path.extend(['..', '.'])
import kis_auth as ka
from overseas_futureoption_functions import *

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 인증
ka.auth()
trenv = ka.getTREnv()

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 체결추이(일간) [해외선물-018]
##############################################################################################

df1, df2 = daily_ccnl(srs_cd="6AM24", exch_cd="CME", start_date_time="", close_date_time="20240402", qry_tp="Q", qry_cnt="30", qry_gap="", index_key="")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 호가 [해외선물-031]
##############################################################################################

df1, df2 = inquire_asking_price(srs_cd="ESZ23")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 당일주문내역조회 [v1_해외선물-004]
##############################################################################################

df = inquire_ccld(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, ccld_nccs_dvsn="01", sll_buy_dvsn_cd="%%", fuop_dvsn="00", ctx_area_fk200="", ctx_area_nk200="")
print(df)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 일별체결내역[해외선물-011]
##############################################################################################

df1, df2 = inquire_daily_ccld(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, strt_dt="20250101", end_dt="20250131", fuop_dvsn_cd="00", fm_pdgr_cd="", crcy_cd="%%%", fm_item_ftng_yn="N", sll_buy_dvsn_cd="%%", ctx_area_fk200="", ctx_area_nk200="")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 일별 주문내역 [해외선물-013]
##############################################################################################

df = inquire_daily_order(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, strt_dt="20250601", end_dt="20250703", fm_pdgr_cd="", ccld_nccs_dvsn="01", sll_buy_dvsn_cd="%%", fuop_dvsn="00", ctx_area_fk200="", ctx_area_nk200="")
print(df)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 예수금현황 [해외선물-012]
##############################################################################################

df = inquire_deposit(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, crcy_cd="TUS", inqr_dt="20250630")
print(df)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 기간계좌손익 일별 [해외선물-010]
##############################################################################################

df1, df2 = inquire_period_ccld(inqr_term_from_dt="20250601", inqr_term_to_dt="20250630", cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, crcy_cd="%%%", whol_trsl_yn="N", fuop_dvsn="00", ctx_area_fk200="", ctx_area_nk200="")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 기간계좌거래내역 [해외선물-014]
##############################################################################################

df = inquire_period_trans(inqr_term_from_dt="20250101", inqr_term_to_dt="20250131", cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, acnt_tr_type_cd="1", crcy_cd="%%%", ctx_area_fk100="", ctx_area_nk100="", pwd_chk_yn="")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물종목현재가 [v1_해외선물-009]
##############################################################################################

df = inquire_price(srs_cd="BONU25")
print(df)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 주문가능조회 [v1_해외선물-006]
##############################################################################################

df = inquire_psamount(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, ovrs_futr_fx_pdno="6AU22", sll_buy_dvsn_cd="02", fm_ord_pric="", ecis_rsvn_ord_yn="")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 분봉조회[해외선물-016]
##############################################################################################

df1, df2 = inquire_time_futurechartprice(srs_cd="BONU25", exch_cd="EUREX", start_date_time="20250101", close_date_time="20250701", qry_tp="Q", qry_cnt="120", qry_gap="1", index_key="")
print(df1)
print(df2)


##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션 분봉조회 [해외선물-040]
##############################################################################################

result1, result2 = inquire_time_optchartprice(srs_cd="DXM24", exch_cd="ICE", qry_cnt="30")
print(result1)
print(result2)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 미결제내역조회(잔고) [v1_해외선물-005]
##############################################################################################

df = inquire_unpd(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, fuop_dvsn="00", ctx_area_fk100="", ctx_area_nk100="")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 미결제추이 [해외선물-029]
##############################################################################################

df1, df2 = investor_unpd_trend(prod_iscd="GE", bsop_date="20240513", upmu_gubun="0", cts_key="")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 증거금상세 [해외선물-032]
##############################################################################################

df = margin_detail(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, crcy_cd="USD", inqr_dt="20250701")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물옵션 장운영시간 [해외선물-030]
##############################################################################################

df = market_time(fm_pdgr_cd="", fm_clas_cd="", fm_excg_cd="CME", opt_yn="N", ctx_area_nk200="", ctx_area_fk200="")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 체결추이(월간)[해외선물-020]
##############################################################################################

df1, df2 = monthly_ccnl(srs_cd="6AM24", exch_cd="CME", start_date_time="", close_date_time="20240402", qry_tp="Q", qry_cnt="30", qry_gap="", index_key="")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션 호가 [해외선물-033]
##############################################################################################

df1, df2 = opt_asking_price(srs_cd="OESM24 C5340")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션 체결추이(일간) [해외선물-037]
##############################################################################################

result1, result2 = opt_daily_ccnl(srs_cd="DXM24", exch_cd="ICE", qry_cnt="30")
print(result1)
print(result2)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션종목상세 [해외선물-034]
##############################################################################################

result = opt_detail(srs_cd="C5500")
print(result)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션 체결추이(월간) [해외선물-039]
##############################################################################################

result1, result2 = opt_monthly_ccnl(srs_cd="DXM24", exch_cd="ICE", qry_cnt="30")
print(result1)
print(result2)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션종목현재가 [해외선물-035]
##############################################################################################

result = opt_price(srs_cd="DXM24")
print(result)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션 체결추이(틱) [해외선물-038]
##############################################################################################

result1, result2 = opt_tick_ccnl(srs_cd="DXM24", exch_cd="ICE", qry_cnt="30")
print(result1)
print(result2)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션 체결추이(주간) [해외선물-036]
##############################################################################################

result1, result2 = opt_weekly_ccnl(srs_cd="DXM24", exch_cd="ICE", qry_cnt="30")
print(result1)
print(result2)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 주문[v1_해외선물-001]
##############################################################################################

df = order(cano=trenv.my_acct, acnt_prdt_cd=trenv.my_prod, ovrs_futr_fx_pdno="1AALN25 C10.0", sll_buy_dvsn_cd="02", fm_lqd_ustl_ccld_dt="", fm_lqd_ustl_ccno="", pric_dvsn_cd="1", fm_limit_ord_pric="1.17", fm_stop_ord_pric="", fm_ord_qty="1", fm_lqd_lmt_ord_pric="", fm_lqd_stop_ord_pric="", ccld_cndt_cd="6", cplx_ord_dvsn_cd="0", ecis_rsvn_ord_yn="N", fm_hdge_ord_scrn_yn="N")
print(df)

##############################################################################################
# [해외선물옵션] 주문/계좌 > 해외선물옵션 정정취소주문[v1_해외선물-002, 003]
##############################################################################################

df = order_rvsecncl(cano=trenv.my_acct, ord_dv="0", acnt_prdt_cd=trenv.my_prod, orgn_ord_dt="20250630", orgn_odno="00123456", fm_limit_ord_pric="10.0", fm_stop_ord_pric="", fm_lqd_lmt_ord_pric="", fm_lqd_stop_ord_pric="", fm_hdge_ord_scrn_yn="N", fm_mkpr_cvsn_yn="")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 상품기본정보[해외선물-023]
##############################################################################################

df = search_contract_detail(qry_cnt="1", srs_cd_01="BONU25", srs_cd_02="BONU25", srs_cd_03="BONU25")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외옵션 상품기본정보 [해외선물-041]
##############################################################################################

result = search_opt_detail(qry_cnt="1", srs_cd_01="6AM24")
print(result)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물종목상세[v1_해외선물-008]
##############################################################################################

df = stock_detail(srs_cd="BONU25")
print(df)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 체결추이(틱)[해외선물-019]
##############################################################################################

df1, df2 = tick_ccnl(srs_cd="BONU25", exch_cd="EUREX", start_date_time="", close_date_time="20250630", qry_tp="Q", qry_cnt="30", qry_gap="", index_key="")
print(df1)
print(df2)

##############################################################################################
# [해외선물옵션] 기본시세 > 해외선물 체결추이(주간)[해외선물-017]
##############################################################################################

df1, df2 = weekly_ccnl(srs_cd="6AM24", exch_cd="CME", start_date_time="", close_date_time="20240402", qry_tp="Q", qry_cnt="30", qry_gap="", index_key="")
print(df1)
print(df2)

