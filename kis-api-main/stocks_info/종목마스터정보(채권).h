/*****************************************************************************
 * 채권 코드 파일 구조
 ****************************************************************************/
typedef struct
{
	char	type			[2];				// A0:장내소매채권, F9:(주식관련사채, 소액채권), C0:국고채권
	char	bond_cls_code	[2];				// A0: GA:국고채 MA:통안채 BA:금융채 SA:비금융특수채 CA:회사채
	                                            // F9: 03:장내소액채권 02:기타채권 04:장내주식관련사채 MM:매매종류별
												// C0: GA:국고채 MA:통안채 BA:금융채 SA:비금융특수채 MB:지방채 99:기타
	char	stnd_iscd		[12];
	char	sname		    [40];
	char	bond_int_cls_code[2];				// F9/C0 : 01:할인   02:복리   03:이표   04:금리연동 05:분할상환(거리복리)
												//		   06:분할(거치단리)   07:단리   08:FRN      09:복5단2
												// A0 : 고정금리형 11:할인채 12:복리채 13:이표채 14:단리채   15:복5단2 19:기타
												// A0 : 변동금리형 21:이표채 22:복리채 23:단리채 29:기타
	char    lstn_date       [8];                // 상장일
	char    pblc_date       [8];                // 발행 일자
	char    rdmp_date       [8];                // 상환 일자
	char    sale_date       [8];                // 매출 일자
	char    srfc_intrt      [8];                // 표면 이자율(사용안함)
	char    rdmt_rate       [8];                // 만기 상환 비율(사용안함)
} ST_BOND_CODE;