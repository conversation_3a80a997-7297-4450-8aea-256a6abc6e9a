#### **[당사에서 제공하는 샘플코드에 대한 유의사항]** ####
- 샘플 코드는 한국투자증권 오픈API(KIS Develpers)를 연동하는 예시입니다. 고객님의 개발부담을 줄이고자 참고용으로 제공되고 있습니다.
- 샘플 코드는 별도의 공지 없이 지속적으로 업데이트될 수 있습니다.
- 샘플 코드를 활용하여 제작한 고객님의 프로그램으로 인한 손해에 대해서는 당사에서 책임지지 않습니다.

![header](https://capsule-render.vercel.app/api?type=waving&color=gradient&height=300&section=header&text=한국투자증권%20KIS%20Developers&fontSize=50&animation=fadeIn&fontAlignY=38&desc=Open%20Trading%20API%20Postman%20Sample%20Code&descAlignY=51&descAlign=62)

## 1. Postman이란

[포스트맨(Postman)](https://www.postman.com/)은 개발자들이 API를 디자인하고 빌드하고 테스트하고 반복하기 위한 API 플랫폼입니다. 2022년 4월 기준으로 포스트맨의 등록 사용자는 20,000,000명 이상, 개방 API 수는 75,000개로 보고되었으며, 세계 최대의 공개 API 허브입니다. (출처 : [위키백과-포스트맨](https://ko.wikipedia.org/wiki/포스트맨_(소프트웨어)))


## 2. 사전 준비 사항
1.  한국투자증권 홈페이지([실전계좌 개설](https://securities.koreainvestment.com/main/customer/guide/_static/TF04aa090000.jsp)/[모의계좌 개설](https://securities.koreainvestment.com/main/research/virtual/_static/TF07da010000.jsp))에서 계좌 개설 → **계좌번호 준비**
2.  [KIS Developers 홈페이지](https://apiportal.koreainvestment.com/)에서 API신청 - 사용할 계좌번호로 API신청 → **APP KEY & APP SECRET 준비**
3.  [Postman](https://www.postman.com/downloads/) 설치


## 3. Postman을 활용한 API 호출 방법
### 3.1. Postman 실행 및 json 파일 Import
Postman 실행 후 아래 4개의 json 파일들을 다운로드 하신 후 Import 해주세요. Import하는 방법은 Postman 좌측 상단의 Import 버튼을 누르시고 File 탭에 파일을 끌어다 놓아주세요. 파일이 정상적으로 끌어와지면 아래 이미지와 같이 파일들이 식별되며, 이후 Import 버튼을 누르시면 됩니다.
* 모의계좌만을 사용하시는 경우 1,2번 파일을, 실전계좌만을 사용하시는 경우 3,4번 파일만 Import하셔도 됩니다.

|순번 |파일명 |파일 상세 |
|--|--|--|
|1|모의계좌_POSTMAN_샘플코드_v1.5.json|postman collections json file(모의계좌용)|
|2|모의계좌_POSTMAN_환경변수.json|postman environments json file(모의계좌용)|
|3|실전계좌_POSTMAN_샘플코드_v2.4.json|postman collections json file(실전계좌용)|
|4|실전계좌_POSTMAN_환경변수.json|postman environments json file(실전계좌용)|

![image](https://user-images.githubusercontent.com/87407853/200476672-c9d15708-5476-4703-89c9-29046ca00d37.png)

### 3.2. 환경변수 설정
Import가 완료되면 환경변수 설정을 해줍니다. 모의계좌를 활용하여 API테스트를 하실 경우 왼쪽 바의 Environments의 모의Env를, 실전계좌를 활용하여 API테스트를 하실 경우 실전 Env를 환경변수로 사용합니다. 따라서 사용하실 환경변수 값들을 채워주어야 합니다. 아래 설명대로 값들을 전부 채워 넣어주세요. 값을 채워 넣을 때는 Initial Value, Current Value 모두 값을 넣어주셔야 합니다. (VTS, PROD는 이미 값이 채워져 있으니 수정하지 말아주세요.)

* API 신청한 계좌번호와 해당 계좌의 APPKEY, APPSECRET 값을 채워주시면 됩니다.
* 주식거래계좌(01)의 경우 아래 각 표의 1,3,4번을 채워주시면 되고, 선물옵션계좌(03 or 08)의 경우 아래 각 표의 2,5,6번을 채워주시면 됩니다.

#### 3.2.1. 모의Env의 경우 아래의 값들을 각각 채워 넣어줍니다.
|순번 |환경변수명 |값(Initial Value, Current Value) |값 예시 |
|--|--|--|--|
|1|CANO|본인의 모의계좌 종합계좌번호 8자리(주식)|ex.50012345|
|2|CANO_T|본인의 모의계좌 종합계좌번호 8자리(선물옵션)|ex.60012345|
|3|VTS_APPKEY|홈페이지에서 발급 받은 계좌번호(주식) APP KEY|ex.PSabcmEJH4U9dfewefwJdfsa4P5qewrPdf4n|
|4|VTS_APPSECRET|홈페이지에서 발급 받은 계좌번호(주식) APP SECRET|ex.FoB6uLRLw5o0Ozxsdfkejklskjkr...uFg9Ya0=|
|5|VTT_APPKEY|홈페이지에서 발급 받은 계좌번호(선물옵션) APP KEY|ex.PSabcmEJH4U9dfewefwJdfsa4P5qewrPdf4n|
|6|VTT_APPSECRET|홈페이지에서 발급 받은 계좌번호(선물옵션) APP SECRET|ex.FoB6uLRLw5o0Ozxsdfkejklskjkr...uFg9Ya0=|

#### 3.2.2. 실전Env의 경우 아래의 값들을 각각 채워 넣어줍니다.
|순번 |환경변수명 |값(Initial Value, Current Value) |값 예시 |
|--|--|--|--|
|1|CANO_REAL|본인의 실전계좌 종합계좌번호(주식)|ex.50012345|
|2|CANO_REAL_T|본인의 실전계좌 종합계좌번호(선물옵션)|ex.60012345|
|3|PROD_APPKEY|홈페이지에서 발급 받은 계좌번호(주식) APP KEY|ex.PSabcmEJH4U9dfewefwJdfsa4P5qewrPdf4n|
|4|PROD_APPSECRET|홈페이지에서 발급 받은 계좌번호(주식) APP SECRET|ex.FoB6uLRLw5o0Ozxsdfkejklskjkr...uFg9Ya0=|
|5|PROT_APPKEY|홈페이지에서 발급 받은 계좌번호(선물옵션) APP KEY|ex.PSabcmEJH4U9dfewefwJdfsa4P5qewrPdf4n|
|6|PROT_APPSECRET|홈페이지에서 발급 받은 계좌번호(선물옵션) APP SECRET|ex.FoB6uLRLw5o0Ozxsdfkejklskjkr...uFg9Ya0=|


### 3.3. 환경변수 선택
환경변수 설정이 완료되면 다시 Collections 바로 돌아가셔서 사용할 환경변수를 선택을 해줍니다. 환경변수 선택은 Postman 화면 우측 상단에서 아래 화살표(∨)를 눌러 선택 가능합니다. 모의계좌를 활용하여 API테스트를 하실 경우 모의투자(모의Env)를, 실전계좌를 활용하여 API테스트를 하실 경우 실전투자(실전Env)를 환경변수로 선택합니다.

![image](https://user-images.githubusercontent.com/87407853/200476740-beceda63-4d7e-48e2-ba4e-f69a8a850fea.png)

### 3.4. API 호출
호출하고 싶은 API를 각 폴더에서 찾아 header값, body값을 변경하시면서 사용하시면 됩니다.
* 각 API 이름 앞에 V는 모의계좌를, J는 실전계좌를 의미합니다.
* GET 요청의 경우, 계좌번호 환경변수가 불러와져 그대로 사용하시면 되지만, POST 요청의 경우 계좌번호(CANO)를 BODY값에 직접 입력하셔야 하는 점 유의 부탁드립니다. **(중요)  따라서 POST API 호출 테스트하실 때는 반드시 Body 값의 계좌번호(CANO)를 본인의 종합계좌번호 8자리로 수정 후 호출하셔야 합니다.**

### 4. 코드 자동생성 기능
포스트맨에는 소스코드를 생성하는 기능이 있습니다. 우측의 </> 부분을 클릭하면 소스코드가 생성되며, 생성 언어 또한 선택할 수 있습니다.

![image](https://github.com/koreainvestment/open-trading-api/assets/87407853/e22da880-1ac1-490f-b483-4917cb712953)




## 5. Postman 샘플코드 목록

|구분 |API명 |모의투자 제공 여부 |실전투자 제공 여부 |
|--|--|--|--|
|OAuth인증|웹소켓접속키발급|⭕|⭕|
|OAuth인증|접근토큰발급|⭕|⭕|
|OAuth인증|접근토큰폐기|⭕|⭕|
|OAuth인증|Hashkey|⭕|⭕|
|[국내주식]주문/계좌|주식주문(현금)|⭕|⭕|
|[국내주식]주문/계좌|주식주문(신용)| |⭕|
|[국내주식]주문/계좌|주식주문(정정취소)|⭕|⭕|
|[국내주식]주문/계좌|주식정정취소가능주문조회| |⭕|
|[국내주식]주문/계좌|주식일별주문체결조회|⭕|⭕|
|[국내주식]주문/계좌|주식잔고조회|⭕|⭕|
|[국내주식]주문/계좌|매수가능조회|⭕|⭕|
|[국내주식]주문/계좌|주식예약주문| |⭕|
|[국내주식]주문/계좌|주식예약주문정정취소| |⭕|
|[국내주식]주문/계좌|주식예약주문조회| |⭕|
|[국내주식]주문/계좌|퇴직연금 체결기준잔고| |⭕|
|[국내주식]주문/계좌|퇴직연금 미체결내역| |⭕|
|[국내주식]주문/계좌|퇴직연금 매수가능조회| |⭕|
|[국내주식]주문/계좌|퇴직연금 예수금조회| |⭕|
|[국내주식]주문/계좌|퇴직연금 잔고조회| |⭕|
|[국내주식]주문/계좌|주식잔고조회_실현손익| |⭕|
|[국내주식]주문/계좌|신용매수가능조회| |⭕|
|[국내주식]주문/계좌|투자계좌자산현황조회| |⭕|
|[국내주식]주문/계좌|기간별매매손익현황조회| |⭕|
|[국내주식]주문/계좌|기간별손익일별합산조회| |⭕|
|[국내주식]주문/계좌|매도가능수량조회| |⭕|
|[국내주식]주문/계좌|주식통합증거금현황| |⭕|
|[국내주식]기본시세|주식현재가 시세|⭕|⭕|
|[국내주식]기본시세|주식현재가 체결|⭕|⭕|
|[국내주식]기본시세|주식현재가 일자별|⭕|⭕|
|[국내주식]기본시세|주식현재가 호가 예상체결|⭕|⭕|
|[국내주식]기본시세|주식현재가 투자자|⭕|⭕|
|[국내주식]기본시세|주식현재가 회원사|⭕|⭕|
|[국내주식]기본시세|국내주식기간별시세(일/주/월/년)|⭕|⭕|
|[국내주식]기본시세|주식현재가 당일시간대별체결|⭕|⭕|
|[국내주식]기본시세|주식현재가 시간외일자별주가|⭕|⭕|
|[국내주식]기본시세|주식당일분봉조회|⭕|⭕|
|[국내주식]기본시세|주식현재가 시세2| |⭕|
|[국내주식]기본시세|ETF/ETN 현재가| |⭕|
|[국내주식]기본시세|NAV 비교추이(종목)| |⭕|
|[국내주식]기본시세|NAV 비교추이(분)| |⭕|
|[국내주식]기본시세|NAV 비교추이(일)| |⭕|
|[국내주식]기본시세|국내주식 장마감 예상체결가| |⭕|
|[국내주식]기본시세|ETF 구성종목시세| |⭕|
|[국내주식]기본시세|국내주식 시간외현재가| |⭕|
|[국내주식]기본시세|국내주식 시간외호가| |⭕|
|[국내주식]ELW시세|ELW현재가 시세|⭕|⭕|
|[국내주식]ELW시세|ELW 상승률순위| |⭕|
|[국내주식]ELW시세|ELW 거래량순위| |⭕|
|[국내주식]ELW시세|ELW 지표순위| |⭕|
|[국내주식]ELW시세|ELW 민감도 순위| |⭕|
|[국내주식]ELW시세|ELW 당일급변종목| |⭕|
|[국내주식]ELW시세|ELW 신규상장종목| |⭕|
|[국내주식]ELW시세|ELW 투자지표추이(체결)| |⭕|
|[국내주식]ELW시세|ELW 투자지표추이(분별)| |⭕|
|[국내주식]ELW시세|ELW 투자지표추이(일별)| |⭕|
|[국내주식]ELW시세|ELW 변동성 추이(틱)| |⭕|
|[국내주식]ELW시세|ELW 변동성 추이(체결)| |⭕|
|[국내주식]ELW시세|ELW 변동성 추이(분별)| |⭕|
|[국내주식]ELW시세|ELW 변동성 추이(일별)| |⭕|
|[국내주식]ELW시세|ELW 민감도 추이(체결)| |⭕|
|[국내주식]ELW시세|ELW 민감도 추이(일별)| |⭕|
|[국내주식]ELW시세|ELW 기초자산별 종목시세| |⭕|
|[국내주식]ELW시세|ELW LP매매추이| |⭕|
|[국내주식]ELW시세|ELW 비교대상종목조회| |⭕|
|[국내주식]ELW시세|ELW 종목검색| |⭕|
|[국내주식]ELW시세|ELW 기초자산 목록조회| |⭕|
|[국내주식]ELW시세|ELW 만기예정/만기종목| |⭕|
|[국내주식]업종/기타|국내주식업종기간별시세(일/주/월/년)|⭕|⭕|
|[국내주식]업종/기타|국내휴장일조회| |⭕|
|[국내주식]업종/기타|업종분봉조회| |⭕|
|[국내주식]업종/기타|변동성완화장치(VI) 현황| |⭕|
|[국내주식]업종/기타|국내업종 현재지수| |⭕|
|[국내주식]업종/기타|국내업종 일자별지수| |⭕|
|[국내주식]업종/기타|국내업종 구분별전체시세| |⭕|
|[국내주식]업종/기타|국내주식 예상체결 전체지수| |⭕|
|[국내주식]업종/기타|국내업종 시간별지수(틱)| |⭕|
|[국내주식]업종/기타|국내업종 시간별지수(분)| |⭕|
|[국내주식]업종/기타|국내주식 예상체결지수 추이| |⭕|
|[국내주식]업종/기타|금리 종합(국내채권/금리)| |⭕|
|[국내주식]업종/기타|종합 시황/공시(제목)| |⭕|
|[국내주식]종목정보|상품기본조회| |⭕|
|[국내주식]종목정보|주식기본조회| |⭕|
|[국내주식]종목정보|국내주식 대차대조표| |⭕|
|[국내주식]종목정보|국내주식 손익계산서| |⭕|
|[국내주식]종목정보|국내주식 재무비율| |⭕|
|[국내주식]종목정보|국내주식 수익성비율| |⭕|
|[국내주식]종목정보|국내주식 기타주요비율| |⭕|
|[국내주식]종목정보|국내주식 안정성비율| |⭕|
|[국내주식]종목정보|국내주식 성장성비율| |⭕|
|[국내주식]종목정보|국내주식 당사 신용가능종목| |⭕|
|[국내주식]종목정보|예탁원정보(배당일정)| |⭕|
|[국내주식]종목정보|예탁원정보(주식매수청구일정)| |⭕|
|[국내주식]종목정보|예탁원정보(합병/분할일정)| |⭕|
|[국내주식]종목정보|예탁원정보(액면교체일정)| |⭕|
|[국내주식]종목정보|예탁원정보(자본감소일정)| |⭕|
|[국내주식]종목정보|예탁원정보(상장정보일정)| |⭕|
|[국내주식]종목정보|예탁원정보(공모주청약일정)| |⭕|
|[국내주식]종목정보|예탁원정보(실권주일정)| |⭕|
|[국내주식]종목정보|예탁원정보(의무예치일정)| |⭕|
|[국내주식]종목정보|예탁원정보(유상증자일정)| |⭕|
|[국내주식]종목정보|예탁원정보(무상증자일정)| |⭕|
|[국내주식]종목정보|예탁원정보(주주총회일정)| |⭕|
|[국내주식]종목정보|국내주식 종목추정실적| |⭕|
|[국내주식]종목정보|당사 대주가능 종목| |⭕|
|[국내주식]종목정보|국내주식 종목투자의견| |⭕|
|[국내주식]종목정보|국내주식 증권사별 투자의견| |⭕|
|[국내주식]시세분석|국내기관_외국인 매매종목가집계| |⭕|
|[국내주식]시세분석|종목조건검색 목록조회| |⭕|
|[국내주식]시세분석|종목조건검색조회| |⭕|
|[국내주식]시세분석|종목별 프로그램매매추이(체결)| |⭕|
|[국내주식]시세분석|종목별 프로그램매매추이(일별)| |⭕|
|[국내주식]시세분석|종목별 외인기관 추정가집계| |⭕|
|[국내주식]시세분석|종목별일별매수매도체결량| |⭕|
|[국내주식]시세분석|시장별 투자자매매동향(시세)| |⭕|
|[국내주식]시세분석|시장별 투자자매매동향(일별)| |⭕|
|[국내주식]시세분석|국내주식 신용잔고 일별추이| |⭕|
|[국내주식]시세분석|국내주식 예상체결가 추이| |⭕|
|[국내주식]시세분석|국내주식 공매도 일별추이| |⭕|
|[국내주식]시세분석|국내주식 시간외예상체결등락률| |⭕|
|[국내주식]시세분석|프로그램매매 투자자매매동향(당일)| |⭕|
|[국내주식]시세분석|프로그램매매 종합현황(시간)| |⭕|
|[국내주식]시세분석|프로그램매매 종합현황(일별)| |⭕|
|[국내주식]시세분석|외국계 매매종목 가집계| |⭕|
|[국내주식]시세분석|종목별 외국계 순매수추이| |⭕|
|[국내주식]시세분석|국내주식 체결금액별 매매비중| |⭕|
|[국내주식]시세분석|국내 증시자금 종합| |⭕|
|[국내주식]시세분석|관심종목 그룹별 종목조회| |⭕|
|[국내주식]시세분석|관심종목 그룹조회| |⭕|
|[국내주식]시세분석|관심종목(멀티종목) 시세조회| |⭕|
|[국내주식]시세분석|국내주식 상하한가 포착| |⭕|
|[국내주식]시세분석|회원사 실시간 매매동향(틱)| |⭕|
|[국내주식]시세분석|국내주식 매물대/거래비중| |⭕|
|[국내주식]시세분석|주식현재가 회원사 종목매매동향| |⭕|
|[국내주식]순위분석|거래량순위| |⭕|
|[국내주식]순위분석|국내주식 등락률 순위| |⭕|
|[국내주식]순위분석|국내주식 호가잔량 순위| |⭕|
|[국내주식]순위분석|국내주식 수익자산지표 순위| |⭕|
|[국내주식]순위분석|국내주식 시가총액 상위| |⭕|
|[국내주식]순위분석|국내주식 재무비율 순위| |⭕|
|[국내주식]순위분석|국내주식 시간외잔량 순위| |⭕|
|[국내주식]순위분석|국내주식 우선주/괴리율 상위| |⭕|
|[국내주식]순위분석|국내주식 이격도 순위| |⭕|
|[국내주식]순위분석|국내주식 시장가치 순위| |⭕|
|[국내주식]순위분석|국내주식 체결강도 상위| |⭕|
|[국내주식]순위분석|국내주식 관심종목등록 상위| |⭕|
|[국내주식]순위분석|국내주식 예상체결 상승/하락상위| |⭕|
|[국내주식]순위분석|국내주식 당사매매종목 상위| |⭕|
|[국내주식]순위분석|국내주식 신고/신저근접종목 상위| |⭕|
|[국내주식]순위분석|국내주식 대량체결건수 상위| |⭕|
|[국내주식]순위분석|국내주식 공매도 상위종목| |⭕|
|[국내주식]순위분석|국내주식 신용잔고 상위| |⭕|
|[국내주식]순위분석|국내주식 배당률 상위| |⭕|
|[국내주식]순위분석|국내주식 시간외등락율순위| |⭕|
|[국내주식]순위분석|국내주식 시간외거래량순위| |⭕|
|[국내선물옵션]주문/계좌|선물옵션 주문|⭕|⭕|
|[국내선물옵션]주문/계좌|선물옵션 정정취소주문|⭕|⭕|
|[국내선물옵션]주문/계좌|선물옵션 주문체결내역조회|⭕|⭕|
|[국내선물옵션]주문/계좌|선물옵션 잔고현황|⭕|⭕|
|[국내선물옵션]주문/계좌|선물옵션 주문가능|⭕|⭕|
|[국내선물옵션]주문/계좌|(야간)선물옵션 주문체결내역조회| |⭕|
|[국내선물옵션]주문/계좌|(야간)선물옵션 잔고현황| |⭕|
|[국내선물옵션]주문/계좌|(야간)선물옵션 주문가능| |⭕|
|[국내선물옵션]주문/계좌|선물옵션 잔고정산손익내역| |⭕|
|[국내선물옵션]주문/계좌|선물옵션 총자산현황| |⭕|
|[국내선물옵션]주문/계좌|선물옵션 잔고평가손익내역| |⭕|
|[국내선물옵션]주문/계좌|선물옵션 기준일체결내역| |⭕|
|[국내선물옵션]주문/계좌|선물옵션기간약정수수료일별| |⭕|
|[국내선물옵션]주문/계좌|(야간)선물옵션 증거금 상세| |⭕|
|[국내선물옵션]기본시세|선물옵션 시세|⭕|⭕|
|[국내선물옵션]기본시세|선물옵션 시세호가|⭕|⭕|
|[국내선물옵션]기본시세|선물옵션기간별시세(일/주/월/년)|⭕|⭕|
|[국내선물옵션]기본시세|선물옵션 분봉조회| |⭕|
|[국내선물옵션]기본시세|선물옵션 일중예상체결추이| |⭕|
|[국내선물옵션]기본시세|국내선물 기초자산 시세|⭕|⭕|
|[국내선물옵션]기본시세|국내옵션전광판_옵션월물리스트| |⭕|
|[국내선물옵션]기본시세|국내옵션전광판_콜풋| |⭕|
|[국내선물옵션]기본시세|국내옵션전광판_선물| |⭕|
|[해외주식]주문/계좌|해외주식 주문|⭕|⭕|
|[해외주식]주문/계좌|해외주식 정정취소주문|⭕|⭕|
|[해외주식]주문/계좌|해외주식 예약주문접수|⭕|⭕|
|[해외주식]주문/계좌|해외주식 예약주문접수취소|⭕|⭕|
|[해외주식]주문/계좌|해외주식 미체결내역|⭕|⭕|
|[해외주식]주문/계좌|해외주식 잔고|⭕|⭕|
|[해외주식]주문/계좌|해외주식 주문체결내역|⭕|⭕|
|[해외주식]주문/계좌|해외주식 체결기준현재잔고|⭕|⭕|
|[해외주식]주문/계좌|해외주식 예약주문조회| |⭕|
|[해외주식]주문/계좌|해외주식 매수가능금액조회| |⭕|
|[해외주식]주문/계좌|해외주식 미국주간주문| |⭕|
|[해외주식]주문/계좌|해외주식 미국주간정정취소| |⭕|
|[해외주식]주문/계좌|해외주식 기간손익| |⭕|
|[해외주식]주문/계좌|해외증거금 통화별조회| |⭕|
|[해외주식]주문/계좌|해외주식 일별거래내역| |⭕|
|[해외주식]주문/계좌|해외주식 결제기준잔고| |⭕|
|[해외주식]기본시세|해외주식 현재체결가|⭕|⭕|
|[해외주식]기본시세|해외주식 기간별시세|⭕|⭕|
|[해외주식]기본시세|해외주식 종목/지수/환율기간별시세(일/주/월/년)|⭕|⭕|
|[해외주식]기본시세|해외주식 조건검색|⭕|⭕|
|[해외주식]기본시세|해외결제일자조회| |⭕|
|[해외주식]기본시세|해외주식 현재가상세| |⭕|
|[해외주식]기본시세|해외주식분봉조회| |⭕|
|[해외주식]기본시세|해외지수분봉조회| |⭕|
|[해외주식]기본시세|해외주식 상품기본정보| |⭕|
|[해외주식]기본시세|해외주식 현재가 10호가| |⭕|
|[해외주식]시세분석|해외주식 기간별권리조회| |⭕|
|[해외주식]시세분석|해외뉴스종합(제목)| |⭕|
|[해외주식]시세분석|해외주식 권리종합| |⭕|
|[해외주식]시세분석|당사 해외주식담보대출 가능 종목| |⭕|
|[해외주식]시세분석|해외속보(제목)| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 주문| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 정정취소주문| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 당일주문내역조회| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 미결제내역조회(잔고)| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 주문가능조회| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 기간계좌손익 일별| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 일별 체결내역| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 예수금현황| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 일별 주문내역| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 기간계좌거래내역| |⭕|
|[해외선물옵션]주문/계좌|해외선물옵션 증거금상세| |⭕|
|[해외선물옵션]기본시세|해외선물종목상세|⭕|⭕|
|[해외선물옵션]기본시세|해외선물종목현재가|⭕|⭕|
|[해외선물옵션]기본시세|해외선물 분봉조회| |⭕|
|[해외선물옵션]기본시세|해외선물 체결추이(주간)| |⭕|
|[해외선물옵션]기본시세|해외선물 체결추이(일간)| |⭕|
|[해외선물옵션]기본시세|해외선물 체결추이(틱)| |⭕|
|[해외선물옵션]기본시세|해외선물 체결추이(월간)| |⭕|
|[해외선물옵션]기본시세|해외선물 호가| |⭕|
|[해외선물옵션]기본시세|해외선물 상품기본정보| |⭕|
|[해외선물옵션]기본시세|해외선물 장운영시간| |⭕|
|[해외선물옵션]기본시세|해외선물 미결제추이| |⭕|
|[해외선물옵션]기본시세|해외옵션 호가| |⭕|
|[장내채권]주문/계좌|장내채권 매수주문| |⭕|
|[장내채권]주문/계좌|장내채권 매도주문| |⭕|
|[장내채권]주문/계좌|장내채권 정정취소주문| |⭕|
|[장내채권]주문/계좌|채권정정취소가능주문조회| |⭕|
|[장내채권]주문/계좌|장내채권 주문체결내역| |⭕|
|[장내채권]주문/계좌|장내채권 잔고조회| |⭕|
|[장내채권]주문/계좌|장내채권 매수가능조회| |⭕|
|[장내채권]기본시세|장내채권 발행정보| |⭕|
|[장내채권]기본시세|장내채권 기본조회| |⭕|
|[장내채권]기본시세|장내채권현재가(호가)| |⭕|
|[장내채권]기본시세|장내채권 평균단가조회| |⭕|
|[장내채권]기본시세|장내채권 기간별시세(일)| |⭕|
|[장내채권]기본시세|장내채권현재가(시세)| |⭕|
|[장내채권]기본시세|장내채권현재가(체결)| |⭕|
|[장내채권]기본시세|장내채권현재가(일별)| |⭕|


**사용하시면서 어려운 점이 생기면 KIS Developers - Q&A 게시판에 문의 부탁드립니다 :)**
https://apiportal.koreainvestment.com/community/qna

![Footer](https://capsule-render.vercel.app/api?type=waving&color=gradient&height=200&section=footer)
