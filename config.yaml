ai:
  cache_ttl: 300
  confidence_threshold: 0.7
  decision_interval: 30
  max_cache_size: 1000
  max_tokens: 2000
  model: gpt-4o-mini
  openai_api_key: ${OPENAI_API_KEY}
  temperature: 0.2
  timeout: 30.0
  weights:
    market_condition: 0.2
    news_sentiment: 0.3
    risk_assessment: 0.1
    technical_analysis: 0.4
data_collection:
  ai_model:
    stock_check:
      enabled: true
      primary_threshold: 0.7
      server_type: fastchat
      training_data_tracking:
        auto_retrain: true
        enabled: true
        last_training_time_file: ./cache/ai/models/stock_check/last_training_time.json
        training_data_path: ./src/ai_local/stock_check/training_data.json
  api_delay: 2.0
  batch_size: 20
  dart_api_key: YOUR_DART_API_KEY
  data_retention_days: 30
  disclosure_interval: 30
  dynamic_symbol_selection:
    enabled: true
    max_spread: 0.5
    max_symbols: 20
    min_daily_volume: 100
    min_market_cap: 1000
    monitoring_duration: 60
  market_data_interval: 1
  news:
    symbol_keywords:
      '000660':
      - SK하이닉스
      - SK하이닉스
      - Hynix
      005380:
      - 현대차
      - 현대자동차
      - Hyundai
      005930:
      - 삼성전자
      - 삼성
      - Samsung
      '006400':
      - 삼성SDI
      - SDI
      '035420':
      - NAVER
      - 네이버
      '035720':
      - 카카오
      - Kakao
      051910:
      - LG화학
      - LG화학
      068270:
      - 셀트리온
      - Celltrion
      '207940':
      - 삼성바이오로직스
      - 바이오로직스
      '373220':
      - LG에너지솔루션
      - LG에너지
  news_cache_size: 500
  news_collection_interval: 5
  news_similarity:
    comparison_days: 7
    enabled: true
    max_comparison_count: 100
    strict_title_check: true
    threshold: 0.3
  news_time_filter:
    enabled: true
    max_age_minutes: 3  # 현재 시간보다 이 시간(분) 이상 오래된 기사는 수집하지 않음
  news_sources:
  - name: 네이버 증권
    type: rss
    url: https://finance.naver.com/news/news_list.nhn?mode=LSS2D&section_id=101&section_id2=258
  - name: 한국경제
    type: rss
    url: https://www.hankyung.com/feed/economy
  - name: 매일경제
    type: rss
    url: https://www.mk.co.kr/rss/30000001/
  orderbook_cache_size: 100
  orderbook_depth: 5
  price_cache_size: 1000
  price_update_interval: 1
  rss_feeds:
  - https://news.naver.com/main/rss/section.naver?sid=101
  - https://news.naver.com/main/rss/section.naver?sid=102
database:
  backup:
    enabled: true
    interval_hours: 6
    path: data/backups
    retention_days: 30
  backup_interval: 3600
  max_overflow: 20
  path: data/stockbot.db
  pool_recycle: 3600
  pool_size: 10
  pool_timeout: 30
  sqlite:
    check_same_thread: false
    path: data/stockbot.db
    timeout: 30
development:
  debug: false
  debug_mode: false
  log_to_console: true
  log_to_file: true
  profiling:
    enabled: false
    output_path: logs/profile
  simulation:
    enabled: false
    end_date: '2024-12-31'
    initial_capital: ********
    start_date: '2024-01-01'
  simulation_mode: false
  test_mode: false
kis:
  demo:
    account_number: YOUR_DEMO_ACCOUNT_NUMBER
    account_product_code: '01'
    app_key: YOUR_DEMO_APP_KEY
    app_secret: YOUR_DEMO_APP_SECRET
  endpoints:
    demo:
      api_url: https://openapivts.koreainvestment.com:29443
      websocket_url: ws://ops.koreainvestment.com:31000
    real:
      api_url: https://openapi.koreainvestment.com:9443
      websocket_url: ws://ops.koreainvestment.com:21000
  rate_limits:
    per_hour: 10000
    per_minute: 200
    per_second: 20
  real:
    account_number: YOUR_REAL_ACCOUNT_NUMBER
    account_product_code: '01'
    app_key: YOUR_REAL_APP_KEY
    app_secret: YOUR_REAL_APP_SECRET
  timeouts:
    connect: 10
    read: 30
    total: 60
  websocket:
    heartbeat_interval: 30
    max_connections: 5
    reconnect_interval: 30
logging:
  colored_output: true
  console:
    enabled: true
    show_colors: true
  date_format: '%Y-%m-%d %H:%M:%S'
  error_file:
    backup_count: 3
    enabled: true
    level: ERROR
    max_size: 5MB
    path: logs/errors.log
  file:
    backup_count: 5
    compress: true
    enabled: true
    max_size: 10MB
    path: logs/stockbot.log
    rotation: daily
  format: '%(asctime)s | %(levelname)8s | %(name)s:%(lineno)d | %(message)s'
  level: INFO
  loggers:
    aiohttp: WARNING
    httpx: WARNING
    requests: WARNING
    urllib3: WARNING
    websockets: WARNING
  performance:
    backup_count: 3
    enabled: true
    max_size: 5MB
    path: logs/performance.log
monitoring:
  alert_on_high_latency: true
  alert_on_high_slippage: true
  alert_thresholds:
    api_latency: 5000
    cpu_usage: 80.0
    daily_loss: -200000
    drawdown: 10.0
    error_rate: 5.0
    memory_usage: 85.0
  daily_report_time: '18:00'
  latency_threshold: 3.0
  metrics_retention_days: 90
  monthly_report_day: 1
  performance_update_interval: 60
  slippage_threshold: 0.005
  system_check_interval: 30
  weekly_report_day: sunday
performance:
  api_rate_limit: 100
  cache_size: 1000
  cache_ttl: 300
  max_workers: 4
risk:
  concentration_limit: 0.3
  correlation_window: 60
  market_conditions:
    bear: 0.5
    bull: 1.0
    sideways: 0.8
    volatile: 0.3
  max_correlation: 0.7
  max_portfolio_risk: 0.15
  max_volatility: 0.05
  time_risk_multipliers:
    afternoon: 1.0
    market_close: 0.7
    market_open: 0.5
    morning: 1.0
  volatility_window: 20
security:
  allowed_ips: []
  encrypt_api_keys: true
  encrypt_sensitive_data: true
  mask_sensitive_data: true
  max_login_attempts: 5
  session_timeout: 3600
telegram:
  alert_filters:
  - low
  alert_levels:
    critical: 🚨
    high: 🔴
    low: 🟡
    medium: 🟠
  bot_token: ${TELEGRAM_BOT_TOKEN}
  chat_id: ${TELEGRAM_CHAT_ID}
  custom_templates: {}
  daily_summary_time: '18:00'
  max_messages_per_minute: 20
trading:
  cancel_timeout: 5
  close_positions_on_shutdown: true
  daily_loss_limit: -500000
  daily_trade_limit: 50
  environment: demo
  initial_capital: ********
  max_daily_loss: 0.02
  max_holding_time: 3600
  max_order_age: 300
  max_position_ratio: 0.3
  max_position_size: 0.1
  max_positions: 10
  max_slippage: 0.005
  max_splits: 3
  max_spread_ratio: 0.005
  min_daily_volume: ********000
  min_market_cap: ********0000
  min_order_amount: 10000
  order_split_threshold: 1000000
  order_timeout: 60
  order_type: limit
  price_adjustment: 0.001
  split_interval: 30
  split_orders: true
  stop_loss_rate: 0.05
  stop_loss_ratio: 0.01
  take_profit_rate: 0.1
  take_profit_ratio: 0.02
  total_capital: ********
  trailing_stop_rate: 0.03
