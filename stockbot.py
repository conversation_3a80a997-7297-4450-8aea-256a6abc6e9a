#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
StockBot Python 실행 스크립트

사용법:
  python3 run_stockbot.py          # 일반 실행
  python3 run_stockbot.py --debug  # 디버그 모드
  python3 run_stockbot.py --help   # 도움말
"""

import os
import sys
import subprocess
import signal
import time
import argparse
from pathlib import Path

# 색상 코드 (ANSI)
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    MAGENTA = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color
    BOLD = '\033[1m'

def print_colored(message, color=Colors.NC):
    """색상이 적용된 메시지 출력"""
    print(f"{color}{message}{Colors.NC}")

def print_banner():
    """배너 출력"""
    print_colored("\n" + "="*50, Colors.BLUE)
    print_colored("           StockBot 실행 스크립트", Colors.BLUE + Colors.BOLD)
    print_colored("="*50 + "\n", Colors.BLUE)

def check_python_version():
    """Python 버전 확인"""
    if sys.version_info < (3, 8):
        print_colored("오류: Python 3.8 이상이 필요합니다.", Colors.RED)
        print_colored(f"현재 버전: {sys.version}", Colors.YELLOW)
        sys.exit(1)
    
    print_colored(f"✓ Python 버전: {sys.version.split()[0]}", Colors.GREEN)

def check_virtual_env():
    """가상환경 확인 및 활성화"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print_colored("✓ 가상환경 발견", Colors.GREEN)
        
        # 가상환경이 활성화되어 있는지 확인
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            print_colored("✓ 가상환경이 이미 활성화되어 있습니다", Colors.GREEN)
        else:
            print_colored("⚠️  가상환경을 수동으로 활성화해주세요: source venv/bin/activate", Colors.YELLOW)
    else:
        print_colored("⚠️  가상환경이 없습니다. 시스템 Python을 사용합니다.", Colors.YELLOW)

def check_requirements():
    """필수 패키지 확인"""
    print_colored("패키지 의존성 확인 중...", Colors.YELLOW)
    
    required_packages = [
        'colorama',
        'aiohttp', 
        'sqlalchemy',
        'dotenv',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print_colored(f"✓ {package}", Colors.GREEN)
        except ImportError:
            missing_packages.append(package)
            print_colored(f"✗ {package} (누락)", Colors.RED)
    
    if missing_packages:
        print_colored("\n필수 패키지를 설치합니다...", Colors.YELLOW)
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         check=True, capture_output=True)
            print_colored("✓ 패키지 설치 완료", Colors.GREEN)
        except subprocess.CalledProcessError as e:
            print_colored(f"패키지 설치 실패: {e}", Colors.RED)
            sys.exit(1)
    else:
        print_colored("✓ 모든 필수 패키지가 설치되어 있습니다", Colors.GREEN)

def find_stockbot_processes():
    """실행 중인 StockBot 프로세스 찾기"""
    try:
        # ps 명령어로 프로세스 찾기
        result = subprocess.run(
            ["ps", "aux"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        
        pids = []
        for line in result.stdout.split('\n'):
            if 'python' in line and 'main.py' in line and 'grep' not in line:
                parts = line.split()
                if len(parts) > 1:
                    pids.append(int(parts[1]))
        
        return pids
    except (subprocess.CalledProcessError, ValueError):
        return []

def stop_stockbot():
    """StockBot 프로세스 중지"""
    print_colored("StockBot 중지 중...", Colors.YELLOW)
    
    pids = find_stockbot_processes()
    
    if not pids:
        print_colored("실행 중인 StockBot이 없습니다.", Colors.YELLOW)
        return
    
    for pid in pids:
        try:
            print_colored(f"PID {pid} 종료 중...", Colors.YELLOW)
            os.kill(pid, signal.SIGTERM)
            time.sleep(2)
            
            # 프로세스가 여전히 실행 중인지 확인
            try:
                os.kill(pid, 0)  # 프로세스 존재 확인
                print_colored(f"강제 종료: PID {pid}", Colors.RED)
                os.kill(pid, signal.SIGKILL)
            except OSError:
                pass  # 프로세스가 이미 종료됨
                
        except OSError as e:
            print_colored(f"프로세스 {pid} 종료 실패: {e}", Colors.RED)
    
    print_colored("✓ StockBot 중지 완료", Colors.GREEN)

def check_status():
    """StockBot 상태 확인"""
    print_colored("StockBot 상태 확인", Colors.BLUE + Colors.BOLD)
    
    pids = find_stockbot_processes()
    
    if not pids:
        print_colored("StockBot이 실행되지 않고 있습니다.", Colors.YELLOW)
    else:
        print_colored("StockBot이 실행 중입니다:", Colors.GREEN)
        for pid in pids:
            print_colored(f"  PID: {pid}", Colors.GREEN)
    
    # 로그 파일 확인
    log_file = Path("logs/stockbot.log")
    if log_file.exists():
        print_colored("\n최근 로그 (마지막 5줄):", Colors.CYAN)
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[-5:]:
                    print(line.rstrip())
        except Exception as e:
            print_colored(f"로그 읽기 실패: {e}", Colors.RED)
    else:
        print_colored("로그 파일이 없습니다.", Colors.YELLOW)

def run_stockbot(debug_mode=False):
    """StockBot 실행"""
    print_colored("StockBot 시작 중...", Colors.GREEN + Colors.BOLD)
    
    # 로그 디렉토리 생성
    Path("logs").mkdir(exist_ok=True)
    
    # 환경 변수 설정
    env = os.environ.copy()
    env['PYTHONPATH'] = str(Path.cwd())
    
    # 실행 명령어 구성
    cmd = [sys.executable, "-m", "src.main"]
    
    if debug_mode:
        print_colored("디버그 모드로 실행합니다.", Colors.YELLOW)
        cmd.append("--debug")
    else:
        print_colored("일반 모드로 실행합니다.", Colors.GREEN)
    
    try:
        # StockBot 실행
        print_colored("\n" + "="*50, Colors.CYAN)
        print_colored("StockBot 로그 출력 시작", Colors.CYAN + Colors.BOLD)
        print_colored("="*50, Colors.CYAN)
        
        subprocess.run(cmd, env=env, check=True)
        
    except KeyboardInterrupt:
        print_colored("\n\n사용자에 의해 중단되었습니다.", Colors.YELLOW)
    except subprocess.CalledProcessError as e:
        print_colored(f"\nStockBot 실행 실패: {e}", Colors.RED)
        sys.exit(1)

def main():
    """메인 함수"""
    parser = argparse.ArgumentParser(
        description="StockBot 실행 스크립트",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
예시:
  python3 run_stockbot.py                # 일반 실행
  python3 run_stockbot.py --debug       # 디버그 모드 실행
  python3 run_stockbot.py --stop        # StockBot 중지
  python3 run_stockbot.py --status      # 상태 확인
        """
    )
    
    parser.add_argument('--debug', action='store_true', help='디버그 모드로 실행')
    parser.add_argument('--stop', action='store_true', help='실행 중인 StockBot 중지')
    parser.add_argument('--status', action='store_true', help='StockBot 실행 상태 확인')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 작업 디렉토리를 스크립트 위치로 변경
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    if args.stop:
        stop_stockbot()
    elif args.status:
        check_status()
    else:
        # 환경 확인
        print_colored("환경 확인 중...", Colors.YELLOW)
        check_python_version()
        check_virtual_env()
        check_requirements()
        print_colored("✓ 환경 확인 완료!\n", Colors.GREEN)
        
        # StockBot 실행
        run_stockbot(debug_mode=args.debug)

if __name__ == "__main__":
    main()