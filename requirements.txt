# StockBot 의존성 패키지

# 웹 및 HTTP 클라이언트
aiohttp>=3.9.1
aiofiles>=23.2.0
requests>=2.31.0
httpx>=0.25.2

# 웹소켓
websockets>=12.0

# 데이터베이스
aiosqlite>=0.19.0
sqlalchemy>=2.0.23
alembic>=1.13.1

# 데이터 처리
pandas>=2.1.4
numpy>=1.26.2
scipy>=1.11.4

# 설정 관리
PyYAML>=6.0.1
python-dotenv>=1.0.0
pydantic>=2.5.2

# 로깅
loguru>=0.7.2
colorama>=0.4.6

# 날짜/시간 처리
python-dateutil>=2.8.2
pytz>=2023.3
schedule>=1.2.0

# 암호화
cryptography>=41.0.8
pycryptodome>=3.19.0

# 시스템 모니터링
psutil>=5.9.6

# 뉴스 수집
feedparser>=6.0.10
BeautifulSoup4>=4.12.2
lxml>=4.9.4

# AI/ML
openai>=1.6.1
scikit-learn>=1.3.2
torch>=2.1.0
transformers>=4.36.0
tokenizers>=0.13.0
torchvision>=0.16.0
torchaudio>=2.1.0
tokenizers>=0.15.0

# 기술적 분석
# TA-Lib>=0.4.28  # C 라이브러리 의존성으로 인해 주석 처리

# 비동기 처리
asyncio-throttle>=1.0.2

# 환경 변수
python-decouple>=3.8

# 스케줄링
APScheduler>=3.10.4

# 메모리 캐시
cachetools>=5.3.2

# JSON 처리
orjson>=3.9.10

# 재시도 로직
tenacity>=8.2.3

# 색상 출력
colorama>=0.4.6

# 진행률 표시
tqdm>=4.66.1

# 설정 파일 검증
jsonschema>=4.20.0

# 타입 힌트
typing-extensions>=4.8.0

# 파일 감시
watchdog>=3.0.0

# 보안
bcrypt>=4.1.2

# URL 파싱
furl>=2.1.3

# 설정 병합
mergedeep>=1.3.4

# 문자열 처리
unidecode>=1.3.7

# 플랫폼 디렉토리
platformdirs>=4.1.0

# 앱 디렉토리
appdirs>=1.4.4

# CLI
click>=8.1.7

# 병렬 처리
joblib>=1.3.2

# 파일 잠금
filelock>=3.13.1

# 키링
keyring>=24.3.0

# 테스트 (개발용)
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0

# 개발 도구 (개발용)
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.1
isort>=5.12.0