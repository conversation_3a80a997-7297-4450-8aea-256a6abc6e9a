# StockBot - AI 기반 자동 매매 시스템

한국투자증권 API를 활용한 AI 기반 자동 주식 매매 시스템입니다.

## 주요 기능

### 🤖 AI 분석 엔진
- OpenAI GPT 모델을 활용한 시장 분석
- 기술적 분석과 뉴스 분석을 통한 종합적 의사결정
- 리스크 분석 및 포지션 관리
- 동적 프롬프트 관리 시스템

### 📊 데이터 수집 및 분석
- **MarketDataManager**: 시장 데이터 통합 관리자 (단일 진입점)
  - 실시간 시세, 주식 정보, 호가 데이터 통합 관리
  - 각 데이터 소스들의 통합 제어 및 모니터링
  - 콜백 관리 및 성능 메트릭 제공
- **market_sources/**: 기능별 분리된 데이터 소스들
  - **RealtimeSource**: 실시간 시세 데이터 수집 및 기술적 지표 계산
  - **StockInfoSource**: 주식 기본 정보 및 일별 데이터 관리
  - **OrderbookSource**: 호가 데이터 수집 및 분석
- 뉴스 데이터 수집 및 감정 분석
- 기술적 지표 계산 (RSI, Bollinger Bands 등)
- 시장 상황 모니터링

### 💰 거래 시스템
- 자동 주문 실행
- 포지션 관리
- 리스크 관리
- 분할 매매 지원

### 📈 모니터링 및 알림
- 실시간 성능 모니터링
- 텔레그램 알림
- 시스템 상태 모니터링
- 일일/주간 리포트

## 🚀 빠른 시작

### 실행 방법

StockBot을 쉽게 실행할 수 있도록 두 가지 실행 스크립트를 제공합니다:

#### 1. 셸 스크립트 (권장)
```bash
# 일반 실행
./run_stockbot.sh

# 디버그 모드 실행
./run_stockbot.sh --debug

# StockBot 중지
./run_stockbot.sh --stop

# 실행 상태 확인
./run_stockbot.sh --status

# 도움말
./run_stockbot.sh --help
```

#### 2. Python 스크립트
```bash
# 일반 실행
python3 run_stockbot.py

# 디버그 모드 실행
python3 run_stockbot.py --debug

# StockBot 중지
python3 run_stockbot.py --stop

# 실행 상태 확인
python3 run_stockbot.py --status
```

### 실행 스크립트 기능

- ✅ **자동 환경 확인**: Python 버전, 가상환경, 필수 패키지 확인
- ✅ **의존성 자동 설치**: 누락된 패키지 자동 설치
- ✅ **프로세스 관리**: 실행 중인 StockBot 확인 및 중지
- ✅ **색상 로그**: 가독성 높은 색상 로그 출력
- ✅ **상태 모니터링**: 실행 상태 및 최근 로그 확인

### 수동 실행 (고급 사용자)
```bash
# PYTHONPATH 설정 후 실행
export PYTHONPATH=/path/to/stockbot
python3 -m src.main
```

## 시스템 구조

```
stockbot/
├── src/
│   ├── api/                 # 한국투자증권 API 연동
│   │   ├── kis_api.py      # KIS API 클라이언트
│   │   └── websocket.py    # 웹소켓 연결
│   ├── data_collection/    # 데이터 수집 및 관리
│   │   ├── market_data_manager.py    # 시장 데이터 통합 관리자
│   │   ├── market_sources/           # 기능별 분리된 데이터 소스들
│   │   │   ├── realtime_source.py   # 실시간 시세 데이터 소스
│   │   │   ├── stock_info_source.py # 주식 정보 데이터 소스
│   │   │   └── orderbook_source.py  # 호가 데이터 소스
│   │   ├── news_collector.py         # 뉴스 데이터 수집
│   │   └── kis_api.py               # KIS API 클라이언트
│   ├── ai/                 # AI 분석 엔진
│   │   ├── decision_engine.py  # 의사결정 엔진
│   │   ├── prompt_manager.py   # 프롬프트 관리
│   │   └── risk_analyzer.py    # 리스크 분석
│   ├── trading/            # 거래 시스템
│   │   ├── order_manager.py    # 주문 관리
│   │   ├── position_manager.py # 포지션 관리
│   │   └── execution_engine.py # 실행 엔진
│   ├── monitoring/         # 모니터링 시스템
│   │   ├── performance_monitor.py # 성능 모니터링
│   │   ├── telegram_bot.py     # 텔레그램 봇
│   │   └── alert_manager.py    # 알림 관리
│   ├── database/           # 데이터베이스
│   │   └── db_manager.py   # 데이터베이스 관리
│   ├── utils/              # 유틸리티
│   │   ├── config.py       # 설정 관리
│   │   └── logger.py       # 로깅
│   └── main.py             # 메인 애플리케이션
├── config/
│   └── config.yaml         # 설정 파일
├── data/                   # 데이터 저장소
├── logs/                   # 로그 파일
├── requirements.txt        # 의존성 패키지
└── README.md              # 프로젝트 문서
```

## 설치 및 설정

### 1. 환경 설정

```bash
# 가상환경 생성
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 또는
venv\Scripts\activate     # Windows

# 의존성 설치
pip install -r requirements.txt
```

### 2. 환경 변수 설정

`.env` 파일을 생성하고 다음 정보를 입력하세요:

```env
# 한국투자증권 API 설정
# 실제 거래 (Real Trading)
KIS_REAL_APP_KEY=your_real_app_key_here
KIS_REAL_APP_SECRET=your_real_app_secret_here
KIS_REAL_ACCOUNT_NUMBER=your_real_account_number_here

# 모의 거래 (Demo Trading) - 권장
KIS_DEMO_APP_KEY=your_demo_app_key_here
KIS_DEMO_APP_SECRET=your_demo_app_secret_here
KIS_DEMO_ACCOUNT_NUMBER=your_demo_account_number_here
KIS_ACCOUNT_PRODUCT_CODE=01

# API 환경 설정 (demo 또는 real)
KIS_ENVIRONMENT=demo

# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# 텔레그램 봇 설정 (선택사항)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
```

### 3. 한국투자증권 API 키 발급 방법

**⚠️ 중요: 데모 환경에서도 실제 한국투자증권에서 발급받은 API 키가 필요합니다.**

#### 3.1 한국투자증권 계좌 개설
1. [한국투자증권 홈페이지](https://securities.koreainvestment.com/)에서 계좌 개설
2. 온라인 계좌 개설 또는 영업점 방문

#### 3.2 API 서비스 신청
1. 한국투자증권 홈페이지 로그인
2. **[KIS Developers](https://apiportal.koreainvestment.com/)** 접속
3. **API 서비스 신청**
   - 실제투자: 실제 거래용 API
   - 모의투자: 테스트용 API (권장)

#### 3.3 API 키 발급
1. API 서비스 승인 후 **APP KEY**와 **APP SECRET** 발급
2. 모의투자와 실제투자 각각 별도의 키 발급
3. 발급받은 키를 `.env` 파일에 입력

#### 3.4 계좌번호 확인
1. 한국투자증권 HTS/MTS에서 계좌번호 확인
2. 모의투자 계좌번호는 별도로 발급됨
3. 계좌상품코드는 일반적으로 `01` (종합계좌)

### 4. 환경 설정 확인

올바른 설정 확인:
```bash
# 환경 변수 확인
echo $KIS_ENVIRONMENT
echo $KIS_DEMO_APP_KEY

# 설정 파일 확인
cat .env | grep KIS
```

### 3. 설정 파일 수정

`config/config.yaml` 파일에서 다음 설정을 확인하고 수정하세요:

- 거래 설정 (초기 자본, 리스크 한도 등)
- AI 분석 설정 (모델, 가중치 등)
- 모니터링 설정 (알림 임계값 등)

## 실행

```bash
# 메인 애플리케이션 실행
python src/main.py
```

## 시장 데이터 수집 시스템

### 통합 구조
StockBot은 모든 한국투자증권 관련 데이터 수집을 통합 관리하는 구조를 도입했습니다:

#### MarketDataManager (통합 관리자)
- 모든 시장 데이터 소스들의 통합 관리자 역할
- 실시간 시세, 주식 정보, 호가 데이터를 단일 진입점에서 제어
- 데이터 콜백 및 성능 메트릭 제공
- 각 데이터 소스들의 생명주기 관리

#### 통합된 기능들
1. **실시간 시세 데이터**: 
   - 웹소켓 및 REST API를 통한 실시간 데이터
   - 동적 종목 선택 및 캐싱
   - 기술적 지표 계산 (RSI, Bollinger Bands)

2. **주식 기본 정보**: 
   - 일일 자동 업데이트
   - 종목 기본 정보 및 일별 주가 정보
   - 배치 처리 및 오류 복구

3. **호가 데이터**: 
   - 실시간 매수/매도 호가 정보
   - 호가 스프레드 계산
   - 데이터 캐싱 및 이력 관리

### 주식 정보 자동 업데이트

#### 주요 기능
- **일일 자동 업데이트**: 매일 정해진 시간에 자동으로 주식 정보 업데이트
- **배치 처리**: 대량의 주식 데이터를 효율적으로 처리
- **오류 복구**: 네트워크 오류나 API 한도 초과 시 자동 재시도
- **진행 상황 추적**: 업데이트 진행 상황을 실시간으로 모니터링
- **데이터 검증**: 수집된 데이터의 무결성 검증

#### 업데이트되는 정보
- 종목 기본 정보 (종목명, 시장, 섹터, 업종)
- 일별 주가 정보 (시가, 고가, 저가, 종가, 거래량)
- 변동률 및 변동금액
- 시가총액

#### 테스트 방법
```bash
# 시장 데이터 관리자 테스트
python3 -m src.data_collection.market_data_manager

# 개별 데이터 소스 테스트
python3 -m src.data_collection.market_sources.stock_info_source
python3 -m src.data_collection.market_sources.realtime_source
python3 -m src.data_collection.market_sources.orderbook_source
```

#### 설정
`config/config.yaml`에서 다음 설정을 조정할 수 있습니다:
```yaml
stock_info_updater:
  update_interval_hours: 24  # 업데이트 간격 (시간)
  batch_size: 50            # 배치 처리 크기
  max_retries: 3            # 최대 재시도 횟수
  retry_delay: 5            # 재시도 지연 시간 (초)
```



## 주요 설정

### 거래 설정
- `initial_capital`: 초기 자본금
- `max_position_size`: 최대 포지션 크기
- `daily_loss_limit`: 일일 손실 한도
- `risk_per_trade`: 거래당 리스크 비율

### AI 분석 설정
- `model`: 사용할 OpenAI 모델
- `technical_weight`: 기술적 분석 가중치
- `news_weight`: 뉴스 분석 가중치
- `risk_weight`: 리스크 분석 가중치

### 모니터링 설정
- `performance_check_interval`: 성능 체크 간격
- `alert_thresholds`: 알림 임계값
- `telegram_notifications`: 텔레그램 알림 설정

## 보안 주의사항

1. **API 키 보안**: 모든 API 키는 환경 변수로 관리하고 절대 코드에 하드코딩하지 마세요.
2. **암호화**: 민감한 데이터는 암호화하여 저장합니다.
3. **접근 제한**: 시스템 접근을 제한하고 로그를 모니터링하세요.
4. **정기 업데이트**: 의존성 패키지를 정기적으로 업데이트하세요.

## 리스크 관리

1. **포지션 크기 제한**: 단일 종목에 대한 최대 투자 비율 제한
2. **손실 한도**: 일일/월간 손실 한도 설정
3. **변동성 관리**: 시장 변동성에 따른 포지션 조정
4. **상관관계 분석**: 포트폴리오 내 종목 간 상관관계 모니터링

## 모니터링

### 성능 지표
- 총 수익률
- 샤프 비율
- 최대 낙폭 (MDD)
- 승률
- 평균 수익/손실

### 시스템 지표
- CPU/메모리 사용률
- API 응답 시간
- 에러율
- 연결 상태

### 알림
- 거래 체결 알림
- 손익 알림
- 시스템 오류 알림
- 일일 성과 리포트

## 문제 해결

### 일반적인 문제

1. **API 연결 오류**
   - API 키와 시크릿 확인
   - 네트워크 연결 상태 확인
   - API 사용량 한도 확인

2. **주문 실행 오류**
   - 계좌 잔고 확인
   - 주문 가능 시간 확인
   - 종목 거래 가능 여부 확인

3. **AI 분석 오류**
   - OpenAI API 키 확인
   - API 사용량 한도 확인
   - 입력 데이터 형식 확인

### 로그 확인

로그 파일은 `logs/` 디렉토리에 저장됩니다:
- `stockbot.log`: 일반 로그
- `trading.log`: 거래 관련 로그
- `error.log`: 에러 로그

## 개발 및 기여

### 개발 환경 설정

```bash
# 개발 의존성 설치
pip install -r requirements.txt

# 코드 포맷팅
black src/
isort src/

# 타입 체크
mypy src/

# 린팅
flake8 src/

# 테스트 실행
pytest tests/
```

### 기여 가이드라인

1. 이슈를 먼저 생성하여 논의하세요
2. 브랜치를 생성하여 작업하세요
3. 코드 스타일을 준수하세요
4. 테스트를 작성하세요
5. Pull Request를 생성하세요

## 라이선스

MIT License

## 면책 조항

이 소프트웨어는 교육 및 연구 목적으로 제공됩니다. 실제 투자에 사용할 경우 발생하는 모든 손실에 대해 개발자는 책임지지 않습니다. 투자는 본인의 판단과 책임 하에 이루어져야 합니다.

## 지원

문제가 발생하거나 질문이 있으시면 GitHub Issues를 통해 문의해 주세요.