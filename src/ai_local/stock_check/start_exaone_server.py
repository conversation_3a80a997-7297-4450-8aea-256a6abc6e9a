#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXAONE 4.0 1.2B 모델 서버 시작 스크립트

llama.cpp의 llama-server를 사용하여 EXAONE 모델을 서빙합니다.
"""

import os
import sys
import subprocess
import time
import requests
import logging
from pathlib import Path

# 로거 설정 - 모듈이 임포트될 때도 로그가 출력되도록 설정
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s | %(levelname)8s | %(name)s:%(lineno)d | %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

class ExaoneServerManager:
    """EXAONE 모델 서버 관리자"""
    
    def __init__(self, 
                 model_path: str = None,
                 server_host: str = "127.0.0.1",
                 server_port: int = 8820,
                 context_size: int = 8192,
                 gpu_layers: int = 31):
        """
        서버 관리자 초기화
        
        Args:
            model_path: EXAONE 모델 파일 경로
            server_host: 서버 호스트
            server_port: 서버 포트
            context_size: 컨텍스트 크기
            gpu_layers: GPU 레이어 수 (M1 Mac의 경우 31)
        """
        # 프로젝트 루트 디렉토리 먼저 설정
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.models_dir = self.project_root / "models"
        
        # 모델 경로 설정 (models_dir 설정 후에 호출)
        self.model_path = model_path or self._find_model_file()
        self.server_host = server_host
        self.server_port = server_port
        self.context_size = context_size
        self.gpu_layers = gpu_layers
        self.server_url = f"http://{server_host}:{server_port}"
        self.process = None
        
    def _find_model_file(self) -> str:
        """
        EXAONE 모델 파일을 찾습니다.
        
        Returns:
            모델 파일 경로
        """
        # 가능한 모델 파일 위치들
        possible_paths = [
            "./EXAONE-4.0-1.2B-Q4_K_M.gguf",
            "./models/EXAONE-4.0-1.2B-Q4_K_M.gguf",
            "../../../models/EXAONE-4.0-1.2B-Q4_K_M.gguf",
            str(self.models_dir / "EXAONE-4.0-1.2B-Q4_K_M.gguf"),
            os.path.expanduser("~/models/EXAONE-4.0-1.2B-Q4_K_M.gguf")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        
        raise FileNotFoundError(
            "EXAONE 모델 파일을 찾을 수 없습니다. "
            "다음 중 하나의 위치에 EXAONE-4.0-1.2B-Q4_K_M.gguf 파일을 배치해주세요:\n" +
            "\n".join(possible_paths)
        )
    
    def _find_llama_server(self) -> str:
        """
        llama-server 실행 파일을 찾습니다.
        
        Returns:
            llama-server 실행 파일 경로
        """
        # 가능한 llama-server 위치들
        possible_paths = [
            "llama-server",  # PATH에 있는 경우
            "/usr/local/bin/llama-server",
            "/opt/homebrew/bin/llama-server",
            os.path.expanduser("~/llama.cpp/llama-server"),
            "./llama-server"
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, "--help"], 
                                       capture_output=True, 
                                       timeout=5)
                if result.returncode == 0:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        raise FileNotFoundError(
            "llama-server를 찾을 수 없습니다. "
            "llama.cpp를 설치하고 PATH에 추가하거나, "
            "다음 명령으로 설치해주세요:\n"
            "brew install llama.cpp"
        )
    
    def _create_chat_template(self) -> str:
        """
        EXAONE용 채팅 템플릿 파일을 생성합니다.
        
        Returns:
            템플릿 파일 경로
        """
        
        template_path = self.project_root / "exaone_chat_template.jinja"
        return str(template_path)
    
    def start_server(self) -> bool:
        """
        EXAONE 모델 서버를 시작합니다.
        
        Returns:
            시작 성공 여부
        """
        try:
            logger.info('EXAONE 서버를 시작합니다.')    
            # 이미 실행 중인지 확인
            if self.is_server_running():
                logger.info(f"EXAONE 서버가 이미 실행 중입니다: {self.server_url}")
                return True
            
            # llama-server 찾기
            llama_server_path = self._find_llama_server()
            logger.info(f"llama-server 경로: {llama_server_path}")
            
            # 모델 파일 확인
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"모델 파일을 찾을 수 없습니다: {self.model_path}")
            
            logger.info(f"모델 파일: {self.model_path}")
            
            # 채팅 템플릿 생성
            template_path = self._create_chat_template()
            logger.info(f"채팅 템플릿: {template_path}")
            
            # 서버 시작 명령 구성
            cmd = [
                llama_server_path,
                "-m", self.model_path,
                "-c", str(self.context_size),
                "-fa",  # Flash Attention 활성화
                "-ngl", str(self.gpu_layers),  # GPU 레이어 수
                "--temp", "0.1",  # 더 낮은 온도로 일관성 향상
                "--top-p", "0.8",
                "--repeat-penalty", "1.1",
                "--jinja",
                "--chat-template-file", template_path,
                "--reasoning-format", "none",  # thinking 태그 비활성화
                "--reasoning-budget", "0",     # thinking 완전 비활성화
                "--json-schema", '{"type":"object","properties":{"relevant":{"type":"boolean"},"desc":{"type":"string"}},"required":["relevant","desc"]}',
                "--host", self.server_host,
                "--port", str(self.server_port),
                "-a", "EXAONE-4.0-1.2B-Q4_K_M"
            ]
            
            logger.info(f"서버 시작 명령: {' '.join(cmd)}")
            
            # 서버 프로세스 시작
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 서버 시작 대기
            logger.info("EXAONE 서버 시작 중...")
            for i in range(30):  # 최대 30초 대기
                time.sleep(1)
                if self.is_server_running():
                    logger.info(f"EXAONE 서버 시작 완료: {self.server_url}")
                    return True
                
                # 프로세스가 종료되었는지 확인
                if self.process.poll() is not None:
                    stdout, stderr = self.process.communicate()
                    logger.error(f"서버 시작 실패:\nstdout: {stdout}\nstderr: {stderr}")
                    return False
            
            logger.error("서버 시작 타임아웃")
            self.stop_server()
            return False
            
        except Exception as e:
            logger.error(f"서버 시작 오류: {e}")
            return False
    
    def stop_server(self):
        """
        EXAONE 모델 서버를 중지합니다.
        """
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
                logger.info("EXAONE 서버 중지 완료")
            except subprocess.TimeoutExpired:
                self.process.kill()
                logger.warning("EXAONE 서버 강제 종료")
            finally:
                self.process = None
    
    def is_server_running(self) -> bool:
        """
        서버가 실행 중인지 확인합니다.
        
        Returns:
            실행 중 여부
        """
        try:
            response = requests.get(f"{self.server_url}/health", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def test_server(self) -> bool:
        """
        서버 동작을 테스트합니다.
        
        Returns:
            테스트 성공 여부
        """
        try:
            test_data = {
                "model": "EXAONE-4.0-1.2B-Q4_K_M",
                "messages": [
                    {"role": "user", "content": "안녕하세요"}
                ],
                "max_tokens": 50,
                "temperature": 0.3
            }
            
            response = requests.post(
                f"{self.server_url}/v1/chat/completions",
                headers={"Content-Type": "application/json"},
                json=test_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    logger.info("서버 테스트 성공")
                    return True
            
            logger.error(f"서버 테스트 실패: {response.status_code} - {response.text}")
            return False
            
        except Exception as e:
            logger.error(f"서버 테스트 오류: {e}")
            return False

def main():
    """메인 함수"""
    logging.basicConfig(level=logging.INFO)
    
    # 서버 관리자 생성
    server_manager = ExaoneServerManager()
    
    try:
        # 서버 시작
        if server_manager.start_server():
            # 테스트
            if server_manager.test_server():
                print(f"EXAONE 서버가 성공적으로 시작되었습니다: {server_manager.server_url}")
                print("Ctrl+C를 눌러 서버를 중지할 수 있습니다.")
                
                # 서버 유지
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n서버 중지 중...")
            else:
                print("서버 테스트 실패")
        else:
            print("서버 시작 실패")
            
    except Exception as e:
        print(f"오류 발생: {e}")
    finally:
        server_manager.stop_server()

if __name__ == "__main__":
    main()