# -*- coding: utf-8 -*-
"""
LLM 기반 주식 뉴스 분류기

대화형 AI를 사용하여 뉴스의 주식 시장 관련성을 판별합니다.
"""

import asyncio
import json
import logging
import re
import time
from dataclasses import dataclass
from threading import Lock
from typing import Dict, Any, Optional

import aiohttp

logger = logging.getLogger(__name__)

# 상수 정의
DEFAULT_TIMEOUT = 30
INITIALIZE_TIMEOUT = 10
MAX_TEXT_LENGTH = 2000
RETRY_DELAY = 0.5

@dataclass
class NewsClassificationResult:
    """뉴스 분류 결과를 담는 데이터 클래스"""
    text: str
    is_market_result: bool

class LLMClassifier:
    """LLM 모델을 사용한 주식 뉴스 분류기"""
    
    def __init__(self, server_url: str = "http://localhost:1234", max_retries: int = 1):
        """
        LLM 분류기 초기화
        
        Args:
            server_url: LLM 모델 서버 URL
            max_retries: 최대 재시도 횟수
        """
        self.server_url = server_url.rstrip('/')  # URL 정규화
        self.max_retries = max(1, max_retries)
        self.model_name = "hyperclovax-seed-text-instruct-1.5b"
        self._lock = Lock()
        self._initialized = False
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 시스템 프롬프트 캐싱
        self._system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """시스템 프롬프트 생성 (캐싱용)"""
        return """당신은 한국 주식 시장 전문가이고 뉴스를 보고 주식 시장과 관련된 뉴스인지 판단하세요.

주식 관련 뉴스 기준 (result = true)
- 상장기업 관련 소식 (실적, 사업, 이슈 등)
- 업종/산업 동향 및 전망 (규제, 전망 등)
- 경제정책: 금리, 세제, 규제, 정부정책 변화
- 글로벌 경제: 환율, 원자재가격, 주요국 경제정책
- 시장 동향: 지수, 거래량, 외국인/기관 매매, 공매도 등
- 기업공시: 공시사항, IR 자료, 감사보고서

주식 무관 (result = false)
- 순수 사회이슈: 사건사고, 날씨, 연예, 스포츠
- 개인사: 일반인, 개인투자자 관련 단순 뉴스
- 학술/연구: 주식시장과 직접 연관 없는 연구
- 해외 뉴스: 한국 상장기업/시장에 미미한 영향
- 단순 부동산: 개별 아파트 거래, 지역 부동산 소식

판단 절차
1. 상장기업명/티커가 언급되고 주가에 단기(1~3일) 내 실질적 영향(3% 이상 변동 가능성)이 있으면 true
2. 특정 업종/산업에 명확한 영향(예: 반도체 수요 증가, 규제 변화)이 있으면 true
3. 경제정책(금리 변동, 세제 변화, 규제 변화, 정부정책)이 언급되면 무조건 true
4. 글로벌 경제, 시장 동향이 한국 주식 시장에 직접적 파급효과가 있으면 true
5. 루머/단기 이벤트(컨퍼런스 발표 등)/확인되지 않은 정보는 false



애매한 경우 판단 기준
상장기업이 주가에 영향을 줄 수 있으면 → true
특정 업종에 주가에 영향을 줄 수 있으면 → true
위 조건 모두 해당 없으면 → false

주의 사항
- 기업명이 단순 언급(예: 사례, 배경 설명)된 경우는 실질적 영향 없으면 false
- 뉴스가 특정 기업/업종에 미치는 영향은 기업 특성(예: 부채비율)과 업종 민감도(예: 금리 영향)를 고려
- 간접적 연관성보다는 직접적 영향력에 집중
- 확실하지 않은 경우 false로 판단

응답 절대 필수 규칙(중요함!절대 규칙 무시 금지!)
- 응답포맷: {"result": true or false,근거:최대 10글자까지 판단 근거}
- 반드시 응답포맷으로 출력, 그 외의 응답은 모두 시스템 오류

"""
    
    async def initialize(self):
        """분류기 초기화 및 모델 서버 연결 확인"""
        with self._lock:
            if self._initialized:
                return
                
            try:
                logger.info("LLM 분류기 초기화 시작...")
                
                # HTTP 세션 생성 (최적화된 설정)
                if self.session is None:
                    connector = aiohttp.TCPConnector(
                        limit=10,  # 연결 풀 크기 제한
                        limit_per_host=5,  # 호스트당 연결 수 제한
                        ttl_dns_cache=300,  # DNS 캐시 TTL
                        use_dns_cache=True
                    )
                    timeout = aiohttp.ClientTimeout(total=INITIALIZE_TIMEOUT)
                    self.session = aiohttp.ClientSession(
                        connector=connector,
                        timeout=timeout
                    )
                
                # 서버 연결 테스트
                await self._test_server_connection()
                
                self._initialized = True
                logger.info("LLM 분류기 초기화 완료")
                
            except Exception as e:
                logger.error(f"LLM 분류기 초기화 실패: {e}")
                await self._cleanup_session()
                raise
    
    async def _test_server_connection(self):
        """서버 연결 테스트"""
        try:
            async with self.session.get(
                f"{self.server_url}/v1/models", 
                timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                if response.status != 200:
                    raise ConnectionError(f"서버 응답 오류: {response.status}")
        except Exception as e:
            raise ConnectionError(f"LLM 모델 서버에 연결할 수 없습니다: {e}")
    
    async def _cleanup_session(self):
        """세션 정리 (내부용)"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def _call_model(self, text: str, timeout: int = DEFAULT_TIMEOUT) -> Dict[str, Any]:
        """
        LLM 모델 API 호출 (최적화된 버전)
        
        Args:
            text: 분석할 텍스트
            timeout: 타임아웃 (초)
            
        Returns:
            모델 응답 결과
        """
        if not self._initialized:
            await self.initialize()
            
        try:
            # 성능 측정 시작
            start_time = time.time()
            
            # 요청 데이터 구성 (캐시된 시스템 프롬프트 사용)
            request_data = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": self._system_prompt},
                    {"role": "user", "content": f"뉴스 내용: {text}"}
                ],
                "temperature": 0.2,
                "max_tokens": 200,
                "stream": False
            }
            
            # 타임아웃 설정 최적화
            client_timeout = aiohttp.ClientTimeout(
                total=timeout,
                connect=5,  # 연결 타임아웃
                sock_read=timeout - 5  # 읽기 타임아웃
            )
            
            # 비동기 HTTP 요청
            async with self.session.post(
                f"{self.server_url}/v1/chat/completions",
                headers={"Content-Type": "application/json"},
                json=request_data,
                timeout=client_timeout
            ) as response:
                
                # HTTP 요청 시간 측정
                http_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0]["message"]["content"]
                        
                        # 전체 처리 시간 측정
                        total_time = time.time() - start_time
                        logger.debug(f"HTTP 요청: {http_time:.3f}s, 전체: {total_time:.3f}s")
                        
                        return self._parse_model_response(content)
                    else:
                        logger.error(f"모델 응답 형식 오류: {result}")
                        raise ValueError("모델 응답에 choices가 없습니다")
                elif response.status == 429:  # Rate limit
                    logger.warning("API 요청 한도 초과, 잠시 대기 필요")
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=429,
                        message="Rate limit exceeded"
                    )
                else:
                    response_text = await response.text()
                    logger.error(f"API 호출 실패: {response.status} - {response_text}")
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=response_text
                    )
                    
        except asyncio.TimeoutError:
            logger.error(f"모델 API 호출 타임아웃 ({timeout}초)")
            raise
        except aiohttp.ClientConnectorError as e:
            logger.error(f"모델 서버에 연결할 수 없습니다: {e}")
            raise
        except aiohttp.ClientError as e:
            logger.error(f"HTTP 클라이언트 오류: {e}")
            raise
        except Exception as e:
            logger.error(f"모델 API 호출 중 예상치 못한 오류: {e}")
            raise
    
    def _parse_model_response(self, content: str) -> Dict[str, Any]:
        """
        모델 응답을 파싱하여 구조화된 데이터로 변환 (최적화된 버전)
        
        Args:
            content: 모델 응답 텍스트
            
        Returns:
            파싱된 응답 데이터
        """
        if not content or not content.strip():
            raise ValueError("응답 내용이 비어있습니다")
        
        content = content.strip()
        
        try:
            # JSON 블록 찾기 (여러 패턴 지원)
            json_patterns = [
                (content.find('{'), content.rfind('}') + 1),  # 기본 패턴
                (content.find('['), content.rfind(']') + 1),  # 배열 패턴
            ]
            
            json_str = None
            for start, end in json_patterns:
                if start != -1 and end > start:
                    json_str = content[start:end]
                    break
            
            # JSON 문자열이 없으면 전체 내용을 JSON으로 시도
            if not json_str:
                json_str = content

            
            # JSON 파싱
            try:
                result = json.loads(json_str)
            except json.JSONDecodeError:
                # 일반적인 JSON 오류 수정 시도
                json_str = json_str.replace("'", '"')  # 작은따옴표를 큰따옴표로
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)  # 키에 따옴표 추가
                result = json.loads(json_str)
            
            # 결과 검증 및 정규화
            if isinstance(result, dict):
                # 필수 필드 확인 및 정규화
                result_key = 'result'

                if result_key is None:
                    raise ValueError("'result' 필드를 찾을 수 없습니다")
                
                # 정규화된 결과 반환
                return {
                    'result': bool(result[result_key]),
                }
            else:
                raise ValueError("응답이 딕셔너리 형태가 아닙니다")
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON 파싱 실패: {e}\n내용: {content[:200]}...")
            raise ValueError(f"JSON 파싱 실패: {e}")
        except Exception as e:
            logger.error(f"응답 파싱 오류: {e}\n내용: {content[:200]}...")
            raise ValueError(f"응답 파싱 오류: {e}")
    

    
    async def classify_news(self, text: str) -> NewsClassificationResult:
        """뉴스 분류 수행 (최적화된 버전)"""
        # 초기화 확인
        if not self._initialized:
            logger.warning("분류기가 초기화되지 않았습니다. 기본값을 반환합니다.")
            return NewsClassificationResult(
                text=text,
                is_market_result=False
            )
        
        # 입력 텍스트 전처리 및 검증
        if not text or not text.strip():
            return NewsClassificationResult(
                text=text,
                is_market_result=False
            )
        
        # 텍스트 길이 제한 (성능 최적화)
        text = text.strip()
        if len(text) > MAX_TEXT_LENGTH:
            text = text[:MAX_TEXT_LENGTH]
            logger.debug(f"텍스트가 {MAX_TEXT_LENGTH}자로 제한되었습니다")
        
        # 성능 측정 시작
        start_time = time.time()
        
        # 재시도 로직 (지수 백오프 적용)
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                # 모델 호출
                result = await self._call_model(text)
                
                if result is not None:
                    # 성능 측정 완료
                    processing_time = time.time() - start_time
                    logger.debug(f"AI 분류 완료: {processing_time:.3f}s (시도 {attempt + 1})")
                    
                    return NewsClassificationResult(
                        text=text,
                        is_market_result=result["result"]
                    )
                else:
                    raise ValueError("모델 응답이 비어있습니다")
                    
            except aiohttp.ClientResponseError as e:
                last_exception = e
                if e.status == 429:  # Rate limit
                    wait_time = min(RETRY_DELAY * (2 ** attempt), 30)  # 지수 백오프, 최대 30초
                    logger.warning(f"Rate limit 도달, {wait_time}초 대기 후 재시도 ({attempt + 1}/{self.max_retries})")
                    await asyncio.sleep(wait_time)
                elif e.status >= 500:  # 서버 오류
                    wait_time = RETRY_DELAY * (attempt + 1)
                    logger.warning(f"서버 오류 ({e.status}), {wait_time}초 대기 후 재시도 ({attempt + 1}/{self.max_retries})")
                    await asyncio.sleep(wait_time)
                else:
                    # 클라이언트 오류는 재시도하지 않음
                    logger.error(f"클라이언트 오류 ({e.status}): {e.message}")
                    break
                    
            except (asyncio.TimeoutError, aiohttp.ClientConnectorError) as e:
                last_exception = e
                wait_time = RETRY_DELAY * (attempt + 1)
                logger.warning(f"연결 오류, {wait_time}초 대기 후 재시도 ({attempt + 1}/{self.max_retries}): {e}")
                await asyncio.sleep(wait_time)
                
            except ValueError as e:
                last_exception = e
                logger.warning(f"응답 파싱 실패 ({attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(RETRY_DELAY)
                    
            except Exception as e:
                last_exception = e
                logger.error(f"예상치 못한 오류 ({attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(RETRY_DELAY)
        
        # 모든 재시도 실패
        total_time = time.time() - start_time
        error_msg = f"AI 모델 분류 실패 (총 {total_time:.3f}s, 최종 오류: {last_exception})"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    async def is_server_available(self) -> bool:
        """서버 연결 상태 확인 (최적화된 버전)"""
        if not self.session:
            return False
            
        try:
            timeout = aiohttp.ClientTimeout(total=5, connect=2)
            async with self.session.get(
                f"{self.server_url}/v1/models", 
                timeout=timeout
            ) as response:
                return response.status == 200
        except Exception as e:
            logger.debug(f"서버 연결 확인 실패: {e}")
            return False
    
    async def close(self):
        """리소스 정리 (안전한 종료)"""
        try:
            if self.session and not self.session.closed:
                await self.session.close()
        except Exception as e:
            logger.warning(f"세션 종료 중 오류: {e}")
        finally:
            self.session = None
            self._initialized = False
            logger.info("LLM 분류기 리소스 정리 완료")
    
    def __del__(self):
        """소멸자 - 리소스 누수 방지"""
        if hasattr(self, 'session') and self.session and not self.session.closed:
            logger.warning("LLM 분류기가 명시적으로 종료되지 않았습니다. close() 메서드를 호출하세요.")