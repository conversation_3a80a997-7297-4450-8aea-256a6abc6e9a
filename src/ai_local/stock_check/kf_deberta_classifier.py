import torch
import numpy as np
from transformers import AutoModel, AutoTokenizer
from typing import List, Dict, Tuple, Optional
import logging
from dataclasses import dataclass

# 로깅 설정
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class NewsClassificationResult:
    """뉴스 분류 결과를 담는 데이터 클래스"""
    text: str
    is_market_relevant: bool
    confidence_score: float

class KFDeBERTaClassifier:
    """KF-DeBERTa 모델을 사용한 의미적 유사도 기반 뉴스 분류기
    
    한국어 금융 뉴스의 시장 관련성을 분석합니다.
    """
    
    def __init__(self, device: Optional[str] = None):
        """분류기를 초기화합니다.
        
        Args:
            device: 사용할 디바이스 (cuda/cpu)
        """
        # 디바이스 자동 설정
        if device is None:
            if torch.cuda.is_available():
                self.device = "cuda"
            elif torch.backends.mps.is_available():  # Apple Silicon Mac
                self.device = "mps"
            else:
                self.device = "cpu"
        else:
            self.device = device
            
        logger.info(f"사용 디바이스: {self.device}")
        
        # 시장 관련성 분류를 위한 프롬프트 템플릿 정의
        self.market_relevance_prompts = {
            "finance": "이 뉴스는 주식 시장, 기업 실적, 경제 상황에 직접적인 영향을 미치는 금융 관련 뉴스입니다.",
            "general": "이 뉴스는 주식 시장과 관련이 없는 일반적인 사회, 문화, 스포츠 뉴스입니다."
        }
        
        # 금융 관련 핵심 키워드 (가중치 적용용) - 중복 제거 및 최적화
        self.finance_keywords = {
            "high_impact": [
                # 핵심 재무 지표
                "실적", "매출", "영업이익", "순이익", "손실", "적자", "흑자", "배당", "수익", "손익", "공시",
                "eps", "per", "pbr", "roe", "roa", "시가총액", "거래량",
                "부채비율", "유동비율", "부채", "자산", "차입금", "현금보유액",
                # 기업 활동
                "증자", "감자", "합병", "인수", "m&a", "ipo", "상장", "상폐",
                "주식분할", "스톡옵션",
                # 주가 움직임
                "주가", "주식", "상승", "하락", "급등", "급락", "폭등", "폭락", "신고가", "신저가", 
                "상한가", "하한가", "거래",
                # 투자 관련
                "투자", "매수", "매도", "순매수", "순매도", "외국인", "기관", "대량거래", "증권"
            ],
            "medium_impact": [
                # 기업 활동
                "기업", "회사", "경영진", "ceo", "대표", "주주", "분기", "연간", "반기",
                "주주총회", "자사주매입", "경영권",
                "출시", "판매", "계약", "수주", "개발", "특허", "승인", "허가",
                "신제품", "신사업", "사업확장", "해외진출", "자회사",
                "업무협약", "mou", "전략적제휴",
                "성장", "확장", "점유율", "브랜드", "공급", "재고", "인상", "인하",
                "호조", "수출", "수입", "대박", "시장", "경쟁",
                # 산업별 키워드
                "게임", "판매량", "초대박", "흥행", "콘텐츠", "ott", "웹툰",
                "뷰티", "화장품", "k뷰티", "인기", "트렌드",
                "소비", "유통", "매장", "이커머스", "배송", "물류",
                "식품", "음료", "커피", "배달", "외식", "프랜차이즈",
                # 분석 및 전망
                "목표가", "애널리스트", "리포트", "분석", "평가", "등급", "전망", "가이던스",
                "호재", "악재", "중립", "보유", "비중",
                # 정책 및 경제
                "금리", "기준금리", "환율", "인플레이션", "gdp", "cpi", "무역수지",
                "긴축", "양적완화", "실업률", "성장률", "경기침체", "경기회복",
                "정책", "규제완화", "규제강화", "부양책", "한은", "한국은행", "fed", "연준",
                "정부", "국정감사", "국회", "법안", "대선", "공약",
                # 테마 산업
                "반도체", "전기차", "배터리", "2차전지", "ai", "인공지능", "자율주행",
                "클라우드", "5g", "메타버스", "블록체인", "가상화폐",
                "신재생에너지", "태양광", "수소", "원전",
                 # 바이오/헬스케어
                 "임상", "신약", "치료제", "백신", "의료기기", "바이오", "제약",
                 "허가", "승인", "식약처", "fda", "임상시험",
                 # 글로벌 이슈
                 "중국", "미국", "유럽", "러시아", "무역전쟁", "관세", "제재",
                 "원자재", "유가", "달러", "엔화", "위안화", "달러강세", "달러약세",
                 # 시장 관련
                 "코스피", "코스닥", "나스닥", "s&p500", "다우",
                 "etf", "공모주", "거래정지",
                 # ESG
                 "esg", "탄소중립", "친환경", "그린뉴딜"
             ],
            "negative_impact": [
                # 기업 리스크
                "리콜", "결함", "사고", "화재", "폭발", "해킹", "사이버공격",
                "유출", "소송", "검찰", "수사", "압수수색", "기소", "판결",
                "제재", "과징금", "벌금", "처벌", "조사", "감사",
                "회계부정", "분식회계", "영업정지", "허가취소", "손해배상",
                "불매", "노조", "파업", "임금협상", "분규", "구조조정", "희망퇴직",
                "악재",
                # 시장 리스크
                "금융위기", "경기침체"
            ]
        }
        
        # 모델과 토크나이저 로드
        self._load_model()
        
        # 라벨 임베딩 미리 계산 (성능 최적화)
        self._precompute_label_embeddings()
    
    def _load_model(self):
        """KF-DeBERTa 모델과 토크나이저를 로드합니다."""
        try:
            logger.info("KF-DeBERTa 모델 로딩 중...")
            self.tokenizer = AutoTokenizer.from_pretrained("kakaobank/kf-deberta-base")
            self.model = AutoModel.from_pretrained("kakaobank/kf-deberta-base")
            self.model.to(self.device)
            self.model.eval()
            logger.info("KF-DeBERTa 모델 로딩 완료")
        except Exception as e:
            logger.error(f"모델 로딩 실패: {e}")
            raise
    
    def _precompute_label_embeddings(self):
        """프롬프트 임베딩을 미리 계산하여 캐싱합니다. (성능 최적화)"""
        logger.info("프롬프트 임베딩 미리 계산 중...")
        self.label_embeddings = {}
        for label, prompt in self.market_relevance_prompts.items():
            self.label_embeddings[label] = self._get_embedding(prompt)
        logger.info("프롬프트 임베딩 계산 완료")
    
    def _semantic_similarity_classify(self, text: str) -> Dict[str, float]:
        """개선된 의미적 유사도 기반 분류 수행
        
        Args:
            text: 분석할 텍스트
            
        Returns:
            각 카테고리에 대한 확률 점수
        """
        # 텍스트 임베딩 생성
        text_embedding = self._get_embedding(text)
        
        # 각 프롬프트에 대한 유사도 계산
        scores = {}
        for label in self.market_relevance_prompts.keys():
            label_embedding = self.label_embeddings[label]  # 캐싱된 임베딩 사용
            # 코사인 유사도 계산
            similarity = self._cosine_similarity(text_embedding, label_embedding)
            scores[label] = float(similarity)
        
        # 키워드 가중치 적용
        scores = self._apply_keyword_weights(text, scores)
        
        # 점수를 확률로 변환 (softmax)
        scores = self._softmax_normalize(scores)
        return scores
    
    def _apply_keyword_weights(self, text: str, scores: Dict[str, float]) -> Dict[str, float]:
        """키워드 기반 가중치를 적용하여 분류 정확도를 향상시킵니다.
        
        Args:
            text: 분석할 텍스트
            scores: 기본 유사도 점수
            
        Returns:
            가중치가 적용된 점수
        """
        text_lower = text.lower()
        finance_boost = 0.0
        
        # (부정적 가중치) - 핵심 비금융 키워드만 유지
        non_finance_keywords = [
            # 스포츠 관련
            '축구', '야구', '농구', '골프', '테니스', '올림픽', '월드컵',
            '프리미어리그', 'nba', 'mlb', '손흥민', '이강인',
            # 연예 관련
            '연예인', '가수', '배우', '드라마', '영화', '예능',
            'bts', '블랙핑크', '결혼', '이혼', '열애',
            # 범죄/사건사고 관련
            '살해', '살인', '절단', '폭행', '강도', '절도', '사기',
            '구속', '체포', '재판', '판결', '범죄', '사건', '사고',
            '성폭행', '마약', '도박', '때린', '기절', '중요부위',
            # 정치 관련
            '대통령', '국회의원', '장관', '비서실장', '정무실장', '대변인',
            '여당', '야당', '선거', '투표', '탄핵', '계엄', '정치인',
            '청와대', '국회', '옹호', '망언', '내조', '정부'
            '국정감사', '국정조사', '공약', '정책', '법안', '논란', '반박'
            # 기타 비금융
            '요리', '맛집', '여행', '날씨', '다이어트', '건강',
            '음악', '콘서트', '앨범'
        ]
        
        # 비금융 키워드 체크 (부정적 가중치) - 더 관대한 접근법
        non_finance_penalty = 0.0
        
        # 금융 관련 키워드가 함께 있는지 확인
        has_finance_context = any(keyword in text_lower for category_keywords in self.finance_keywords.values() for keyword in category_keywords)
        
        # 금융 맥락이 있으면 비금융 키워드 페널티를 크게 완화
        penalty_multiplier = 0.3 if has_finance_context else 1.0
        
        for keyword in non_finance_keywords:
            if keyword in text_lower:
                # 범죄/사건사고 키워드는 강한 페널티 (하지만 금융 맥락이 있으면 완화)
                if keyword in ['살해', '살인', '절단', '폭행', '강도', '절도', '사기', '횡령', '뇌물',
                              '구속', '체포', '수사', '기소', '재판', '판결', '형량', '징역',
                              '피의자', '피고인', '용의자', '범인', '범죄', '사건', '사고',
                              '화재', '교통사고', '추락', '익사', '중독', '폭발',
                              '성폭행', '성추행', '성범죄', '마약', '도박', '불법',
                              '때린', '기절', '신고', '중요부위']:
                    non_finance_penalty += 0.35 * penalty_multiplier  # 완화된 페널티
                # 정치 관련 키워드 페널티 (강화)
                elif keyword in ['대통령', '국회의원', '장관', '비서실장', '정무실장', '대변인',
                                '여당', '야당', '정당', '선거', '투표', '공약', '정책', '법안',
                                '국정감사', '국정조사', '탄핵', '계엄', '정치인', '정치권',
                                '청와대', '국회', '정부', '행정부', '입법부', '사법부',
                                '옹호', '논란', '반박', '망언', '내조', '與대표', '野대표',
                                '당대표']:
                    non_finance_penalty += 0.35 * penalty_multiplier  # 정치 키워드 페널티 강화
                else:
                    non_finance_penalty += 0.15 * penalty_multiplier  # 일반 키워드는 매우 약한 페널티
        
        # 최대 부정적 가중치 제한 (더 관대하게)
        non_finance_penalty = min(non_finance_penalty, 0.6)
        
        # 금융 관련 키워드 가중치 계산
        for category, keywords in self.finance_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    if category == "high_impact":
                        finance_boost += 0.15  # 높은 영향도 키워드
                    elif category == "medium_impact":
                        finance_boost += 0.08  # 중간 영향도 키워드
                    elif category == "negative_impact":
                        finance_boost += 0.12  # 부정적 영향 키워드
        
        # 가중치 적용 (최대 0.3까지)
        finance_boost = min(finance_boost, 0.3)
        
        # finance 점수에 가중치 추가 및 부정적 가중치 적용
        if "finance" in scores:
            scores["finance"] = scores["finance"] + finance_boost - non_finance_penalty
            # 최소값 보장 (음수가 되지 않도록)
            scores["finance"] = max(scores["finance"], 0.0)
            
        return scores
    
    def _get_embedding(self, text: str) -> torch.Tensor:
        """텍스트의 임베딩을 생성합니다.
        
        Args:
            text: 임베딩을 생성할 텍스트
            
        Returns:
            텍스트 임베딩 벡터
        """
        # 텍스트 토크나이징
        inputs = self.tokenizer(text, return_tensors="pt", truncation=True, 
                               padding=True, max_length=512)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # 모델을 통해 임베딩 생성
        with torch.no_grad():
            outputs = self.model(**inputs)
            # [CLS] 토큰의 임베딩을 사용 (첫 번째 토큰)
            embedding = outputs.last_hidden_state[:, 0, :].squeeze()
        
        return embedding
    
    def _cosine_similarity(self, vec1: torch.Tensor, vec2: torch.Tensor) -> float:
        """두 벡터 간의 코사인 유사도를 계산합니다.
        
        Args:
            vec1: 첫 번째 벡터
            vec2: 두 번째 벡터
            
        Returns:
            코사인 유사도 값 (-1 ~ 1)
        """
        # 벡터 정규화
        vec1_norm = torch.nn.functional.normalize(vec1, dim=0)
        vec2_norm = torch.nn.functional.normalize(vec2, dim=0)
        
        # 코사인 유사도 계산
        similarity = torch.dot(vec1_norm, vec2_norm)
        return similarity.item()
    
    def _softmax_normalize(self, scores: Dict[str, float]) -> Dict[str, float]:
        """점수를 softmax로 정규화하여 확률로 변환합니다.
        
        Args:
            scores: 정규화할 점수 딕셔너리
            
        Returns:
            정규화된 확률 딕셔너리
        """
        # 점수를 numpy 배열로 변환
        values = np.array(list(scores.values()))
        
        # softmax 적용
        exp_values = np.exp(values - np.max(values))  # 수치 안정성을 위해 최대값 빼기
        probabilities = exp_values / np.sum(exp_values)
        
        # 딕셔너리로 다시 변환
        normalized_scores = {}
        for i, key in enumerate(scores.keys()):
            normalized_scores[key] = float(probabilities[i])
        
        return normalized_scores
    
    def _calculate_market_relevance(self, text: str) -> Tuple[bool, float]:
        """개선된 방식으로 텍스트의 주식 시장 관련성을 판별합니다.
        
        Args:
            text: 분석할 텍스트
            
        Returns:
            (관련성 여부, 신뢰도 점수)
        """
        # 개선된 의미적 유사도 기반 시장 관련성 분석
        scores = self._semantic_similarity_classify(text)
        
        # 금융 관련 vs 일반 뉴스 점수 비교
        finance_score = scores.get("finance", 0)
        general_score = scores.get("general", 0)
        
        # 적응적 임계값 기반 판별
        # 의미적 유사도를 더 중시하고, 키워드 페널티의 영향을 줄임
        base_threshold = 0.48  # 기본 임계값을 낮춤 (더 포괄적 분류)
        
        # 금융 키워드가 있으면 임계값을 더 낮춤
        has_finance_keywords = any(keyword in text.lower() for category_keywords in self.finance_keywords.values() for keyword in category_keywords)
        threshold = base_threshold - (0.05 if has_finance_keywords else 0.0)
        
        # 금융 관련성 판별: finance_score가 threshold 이상이면 관련
        is_relevant = finance_score >= threshold
        confidence = finance_score
        
        return is_relevant, confidence
    
    def classify_news(self, text: str) -> NewsClassificationResult:
        """뉴스 텍스트의 시장 관련성을 분류합니다.
        
        Args:
            text: 분석할 뉴스 텍스트
            
        Returns:술 안 마셔?…초등생 밤새 때린 여중생 3명, 기절
            분류 결과
        """
        try:
            # 시장 관련성 판별만 수행
            is_relevant, relevance_confidence = self._calculate_market_relevance(text)
            
            return NewsClassificationResult(
                text=text,
                is_market_relevant=is_relevant,
                confidence_score=relevance_confidence
            )
            
        except Exception as e:
            logger.error(f"뉴스 분류 중 오류 발생: {e}")
            # 기본값 반환
            return NewsClassificationResult(
                text=text,
                is_market_relevant=False,
                confidence_score=0.0
            )
    
    def classify_batch(self, texts: List[str]) -> List[NewsClassificationResult]:
        """여러 뉴스 텍스트를 일괄 분류합니다.
        
        Args:
            texts: 분류할 뉴스 텍스트 리스트
            
        Returns:
            분류 결과 리스트
        """
        results = []
        
        for text in texts:
            result = self.classify_news(text)
            results.append(result)
            
        return results
    
    def get_market_impact_summary(self, results: List[NewsClassificationResult]) -> Dict:
        """분류 결과들의 시장 영향 요약을 생성합니다.
        
        Args:
            results: 분류 결과 리스트
            
        Returns:
            시장 영향 요약 딕셔너리
        """
        if not results:
            return {
                'total_news': 0,
                'market_relevant_count': 0,
                'market_relevant_ratio': 0.0,
                'average_confidence': 0.0
            }
        
        total_news = len(results)
        market_relevant_count = sum(1 for r in results if r.is_market_relevant)
        market_relevant_ratio = market_relevant_count / total_news
        
        # 신뢰도 평균 계산
        confidence_scores = [r.confidence_score for r in results]
        average_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        
        return {
            'total_news': total_news,
            'market_relevant_count': market_relevant_count,
            'market_relevant_ratio': market_relevant_ratio,
            'average_confidence': average_confidence
        }