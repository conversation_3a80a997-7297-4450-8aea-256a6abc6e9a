#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXAONE 4.0 1.2B 모델 설정 스크립트

Hugging Face에서 EXAONE 모델을 다운로드하고 설정합니다.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import requests
from tqdm import tqdm

logger = logging.getLogger(__name__)

def download_file(url: str, filepath: str) -> bool:
    """
    파일을 다운로드합니다.
    
    Args:
        url: 다운로드 URL
        filepath: 저장할 파일 경로
        
    Returns:
        다운로드 성공 여부
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filepath, 'wb') as f, tqdm(
            desc=os.path.basename(filepath),
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
        
        return True
        
    except Exception as e:
        logger.error(f"파일 다운로드 실패: {e}")
        return False

def check_huggingface_cli() -> bool:
    """
    Hugging Face CLI가 설치되어 있는지 확인합니다.
    
    Returns:
        설치 여부
    """
    try:
        result = subprocess.run(['huggingface-cli', '--version'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_huggingface_hub():
    """
    Hugging Face Hub를 설치합니다.
    """
    try:
        print("Hugging Face Hub 설치 중...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'huggingface_hub[cli]'], 
                      check=True)
        print("Hugging Face Hub 설치 완료")
    except subprocess.CalledProcessError as e:
        print(f"Hugging Face Hub 설치 실패: {e}")
        raise

def download_exaone_model(models_dir: Path) -> bool:
    """
    EXAONE 모델을 다운로드합니다.
    
    Args:
        models_dir: 모델 저장 디렉토리
        
    Returns:
        다운로드 성공 여부
    """
    try:
        # 모델 디렉토리 생성
        models_dir.mkdir(parents=True, exist_ok=True)
        
        # 모델 파일 경로
        model_file = models_dir / "EXAONE-4.0-1.2B-Q4_K_M.gguf"
        
        # 이미 존재하는지 확인
        if model_file.exists():
            print(f"모델 파일이 이미 존재합니다: {model_file}")
            return True
        
        # Python huggingface_hub를 사용하여 다운로드
        print("EXAONE 4.0 1.2B 모델 다운로드 중...")
        print("이 작업은 시간이 오래 걸릴 수 있습니다 (약 1-2GB).")
        
        try:
            from huggingface_hub import hf_hub_download
            
            # 모델 파일 다운로드
            downloaded_file = hf_hub_download(
                repo_id="LGAI-EXAONE/EXAONE-4.0-1.2B-GGUF",
                filename="EXAONE-4.0-1.2B-Q4_K_M.gguf",
                local_dir=str(models_dir),
                local_dir_use_symlinks=False
            )
            
            if model_file.exists():
                print(f"모델 다운로드 완료: {model_file}")
                return True
            else:
                print("모델 다운로드 실패: 파일이 생성되지 않았습니다.")
                return False
                
        except ImportError:
            print("huggingface_hub 라이브러리를 찾을 수 없습니다.")
            install_huggingface_hub()
            
            # 재시도
            from huggingface_hub import hf_hub_download
            
            downloaded_file = hf_hub_download(
                repo_id="LGAI-EXAONE/EXAONE-4.0-1.2B-GGUF",
                filename="EXAONE-4.0-1.2B-Q4_K_M.gguf",
                local_dir=str(models_dir),
                local_dir_use_symlinks=False
            )
            
            if model_file.exists():
                print(f"모델 다운로드 완료: {model_file}")
                return True
            else:
                print("모델 다운로드 실패: 파일이 생성되지 않았습니다.")
                return False
            
    except Exception as e:
        print(f"예상치 못한 오류: {e}")
        return False

def check_llama_cpp() -> bool:
    """
    llama.cpp가 설치되어 있는지 확인합니다.
    
    Returns:
        설치 여부
    """
    try:
        result = subprocess.run(['llama-server', '--help'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (FileNotFoundError, subprocess.TimeoutExpired):
        return False

def install_llama_cpp():
    """
    llama.cpp를 설치합니다 (macOS Homebrew 사용).
    """
    try:
        print("llama.cpp 설치 중...")
        subprocess.run(['brew', 'install', 'llama.cpp'], check=True)
        print("llama.cpp 설치 완료")
    except subprocess.CalledProcessError as e:
        print(f"llama.cpp 설치 실패: {e}")
        print("수동으로 설치해주세요: brew install llama.cpp")
        raise
    except FileNotFoundError:
        print("Homebrew가 설치되지 않았습니다.")
        print("Homebrew를 먼저 설치하거나, llama.cpp를 수동으로 설치해주세요.")
        raise

def main():
    """
    메인 설정 함수
    """
    logging.basicConfig(level=logging.INFO)
    
    # 프로젝트 루트 디렉토리
    project_root = Path(__file__).parent.parent.parent.parent
    models_dir = project_root / "models"
    
    print("🤖 EXAONE 4.0 1.2B 모델 설정을 시작합니다...")
    print(f"프로젝트 루트: {project_root}")
    print(f"모델 저장 경로: {models_dir}")
    
    try:
        # 1. llama.cpp 확인 및 설치
        print("\n1. llama.cpp 확인 중...")
        if check_llama_cpp():
            print("✅ llama.cpp가 이미 설치되어 있습니다.")
        else:
            print("❌ llama.cpp가 설치되지 않았습니다.")
            install_llama_cpp()
        
        # 2. EXAONE 모델 다운로드
        print("\n2. EXAONE 모델 다운로드 중...")
        if download_exaone_model(models_dir):
            print("✅ EXAONE 모델 다운로드 완료")
        else:
            print("❌ EXAONE 모델 다운로드 실패")
            return False
        
        print("\n🎉 EXAONE 4.0 1.2B 모델 설정이 완료되었습니다!")
        print("\n다음 단계:")
        print("1. StockBot을 재시작하여 EXAONE AI 분류기를 사용하세요.")
        print("2. 뉴스 수집 시 EXAONE AI가 자동으로 시장 관련성을 분석합니다.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 설정 중 오류 발생: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)