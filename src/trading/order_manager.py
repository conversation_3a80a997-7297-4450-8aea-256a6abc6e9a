# -*- coding: utf-8 -*-
"""
주문 관리자

주식 주문 생성, 관리, 추적
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import uuid

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager
from ..data_collection.kis_api import KISApi
from ..ai_analysis.decision_engine import TradingDecision, DecisionType
from ..ai_analysis.risk_analyzer import RiskAnalyzer

class OrderType(Enum):
    """주문 타입"""
    MARKET = "market"  # 시장가
    LIMIT = "limit"    # 지정가
    STOP_LOSS = "stop_loss"  # 손절
    TAKE_PROFIT = "take_profit"  # 익절

class OrderSide(Enum):
    """주문 방향"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    """주문 상태"""
    PENDING = "pending"      # 대기
    SUBMITTED = "submitted"  # 제출됨
    FILLED = "filled"        # 체결됨
    PARTIALLY_FILLED = "partially_filled"  # 부분체결
    CANCELLED = "cancelled"  # 취소됨
    REJECTED = "rejected"    # 거부됨
    EXPIRED = "expired"      # 만료됨

@dataclass
class Order:
    """주문 데이터 클래스"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float]
    stop_price: Optional[float]
    status: OrderStatus
    created_at: datetime
    updated_at: datetime
    filled_quantity: int = 0
    filled_price: Optional[float] = None
    commission: float = 0.0
    kis_order_id: Optional[str] = None
    parent_decision_id: Optional[str] = None
    notes: str = ""

class OrderManager:
    """
    주문 관리자 클래스
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, 
                 kis_api: KISApi, risk_analyzer: RiskAnalyzer):
        """주문 관리자 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
            kis_api: 한국투자증권 API
            risk_analyzer: 리스크 분석기
        """
        self.config = config_manager
        self.db = db_manager
        self.kis_api = kis_api
        self.risk_analyzer = risk_analyzer
        self.logger = get_logger()
        
        # 주문 설정
        self.max_orders_per_minute = self.config.get('trading.max_orders_per_minute', 10)
        self.order_timeout = self.config.get('trading.order_timeout_seconds', 300)  # 5분
        self.slippage_tolerance = self.config.get('trading.slippage_tolerance', 0.002)  # 0.2%
        self.commission_rate = self.config.get('trading.commission_rate', 0.00015)  # 0.015%
        
        # 주문 추적
        self.active_orders: Dict[str, Order] = {}
        self.order_history: List[Order] = []
        self.order_count_tracker = {}  # 분당 주문 수 추적
        
        # 자동 주문 관리
        self.auto_orders: Dict[str, List[Order]] = {}  # 심볼별 자동 주문
        self.stop_loss_orders: Dict[str, Order] = {}  # 손절 주문
        self.take_profit_orders: Dict[str, Order] = {}  # 익절 주문
        
        # 주문 모니터링 태스크
        self.monitoring_task = None
        self.is_monitoring = False
        
        # 체결 콜백 함수들
        self.fill_callbacks = []
    
    def add_fill_callback(self, callback):
        """체결 콜백 함수 추가
        
        Args:
            callback: 체결 시 호출할 콜백 함수 (order: Order) -> None
        """
        self.fill_callbacks.append(callback)
        self.logger.debug(f"체결 콜백 함수 추가됨: {callback.__name__}")
    
    def remove_fill_callback(self, callback):
        """체결 콜백 함수 제거
        
        Args:
            callback: 제거할 콜백 함수
        """
        if callback in self.fill_callbacks:
            self.fill_callbacks.remove(callback)
            self.logger.debug(f"체결 콜백 함수 제거됨: {callback.__name__}")
    
    async def _notify_fill_callbacks(self, order: Order):
        """체결 콜백 함수들에게 알림
        
        Args:
            order: 체결된 주문
        """
        for callback in self.fill_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(order)
                else:
                    callback(order)
            except Exception as e:
                self.logger.error(f"체결 콜백 실행 오류 ({callback.__name__}): {e}")
    
    async def initialize(self):
        """주문 관리자 초기화"""
        try:
            # 기존 활성 주문 복구
            await self._restore_active_orders()
            
            # 주문 모니터링 시작
            await self.start_monitoring()
            
            self.logger.info("주문 관리자 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"주문 관리자 초기화 오류: {e}")
    
    async def create_order_from_decision(self, decision: TradingDecision) -> Optional[Order]:
        """AI 의사결정으로부터 주문 생성
        
        Args:
            decision: AI 매매 의사결정
        
        Returns:
            생성된 주문 (실패시 None)
        """
        try:
            if decision.decision == DecisionType.HOLD:
                return None
            
            # 주문 방향 결정
            if decision.decision in [DecisionType.BUY]:
                side = OrderSide.BUY
            elif decision.decision in [DecisionType.SELL, DecisionType.CLOSE, DecisionType.EMERGENCY_EXIT]:
                side = OrderSide.SELL
            else:
                return None
            
            # 주문 타입 결정
            if decision.decision == DecisionType.EMERGENCY_EXIT:
                order_type = OrderType.MARKET
                price = None
            else:
                order_type = OrderType.LIMIT
                price = decision.target_price
            
            # 주문 생성
            order = await self.create_order(
                symbol=decision.symbol,
                side=side,
                order_type=order_type,
                quantity=decision.quantity,
                price=price,
                parent_decision_id=str(decision.timestamp)
            )
            
            if order:
                # 손절/익절 주문 생성
                if decision.stop_loss and side == OrderSide.BUY:
                    await self._create_stop_loss_order(order, decision.stop_loss)
                
                if decision.take_profit and side == OrderSide.BUY:
                    await self._create_take_profit_order(order, decision.take_profit)
            
            return order
        
        except Exception as e:
            self.logger.error(f"의사결정 기반 주문 생성 오류: {e}")
            return None
    
    async def create_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                          quantity: int, price: Optional[float] = None,
                          stop_price: Optional[float] = None,
                          parent_decision_id: Optional[str] = None) -> Optional[Order]:
        """주문 생성
        
        Args:
            symbol: 종목코드
            side: 주문 방향
            order_type: 주문 타입
            quantity: 수량
            price: 가격 (지정가 주문시)
            stop_price: 스톱 가격 (스톱 주문시)
            parent_decision_id: 부모 의사결정 ID
        
        Returns:
            생성된 주문
        """
        try:
            # 주문 제한 체크
            if not await self._check_order_limits():
                self.logger.warning("주문 제한 초과")
                return None
            
            # 주문 검증
            validation_result = await self._validate_order(symbol, side, quantity, price)
            if not validation_result['valid']:
                self.logger.warning(f"주문 검증 실패: {validation_result['reason']}")
                return None
            
            # 주문 ID 생성
            order_id = str(uuid.uuid4())
            
            # 주문 객체 생성
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                stop_price=stop_price,
                status=OrderStatus.PENDING,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                parent_decision_id=parent_decision_id
            )
            
            # 주문 제출
            success = await self._submit_order(order)
            
            if success:
                # 활성 주문에 추가
                self.active_orders[order_id] = order
                
                # 데이터베이스에 저장
                await self._save_order_to_db(order)
                
                # 주문 수 추적 업데이트
                self._update_order_count()
                
                self.logger.info(f"주문 생성 완료: {symbol} {side.value} {quantity}주 @ {price}")
                return order
            else:
                self.logger.error(f"주문 제출 실패: {symbol}")
                return None
        
        except Exception as e:
            self.logger.error(f"주문 생성 오류: {e}")
            return None
    
    async def cancel_order(self, order_id: str) -> bool:
        """주문 취소
        
        Args:
            order_id: 주문 ID
        
        Returns:
            취소 성공 여부
        """
        try:
            if order_id not in self.active_orders:
                self.logger.warning(f"활성 주문에서 찾을 수 없음: {order_id}")
                return False
            
            order = self.active_orders[order_id]
            
            if order.status not in [OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]:
                self.logger.warning(f"취소 불가능한 주문 상태: {order.status}")
                return False
            
            # KIS API를 통한 주문 취소
            if order.kis_order_id:
                cancel_result = await self.kis_api.cancel_order(
                    order.symbol,
                    order.kis_order_id,
                    order.side.value
                )
                
                if cancel_result.get('success', False):
                    order.status = OrderStatus.CANCELLED
                    order.updated_at = datetime.now()
                    
                    # 활성 주문에서 제거
                    del self.active_orders[order_id]
                    
                    # 히스토리에 추가
                    self.order_history.append(order)
                    
                    # 데이터베이스 업데이트
                    await self._update_order_in_db(order)
                    
                    self.logger.info(f"주문 취소 완료: {order_id}")
                    return True
                else:
                    self.logger.error(f"주문 취소 실패: {cancel_result.get('message', '')}")
                    return False
            else:
                # KIS 주문 ID가 없는 경우 (아직 제출되지 않음)
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                del self.active_orders[order_id]
                self.order_history.append(order)
                await self._update_order_in_db(order)
                return True
        
        except Exception as e:
            self.logger.error(f"주문 취소 오류: {e}")
            return False
    
    async def cancel_all_orders(self, symbol: Optional[str] = None) -> int:
        """모든 주문 취소 (또는 특정 종목)
        
        Args:
            symbol: 종목코드 (None이면 모든 종목)
        
        Returns:
            취소된 주문 수
        """
        try:
            cancelled_count = 0
            orders_to_cancel = []
            
            for order_id, order in self.active_orders.items():
                if symbol is None or order.symbol == symbol:
                    if order.status in [OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]:
                        orders_to_cancel.append(order_id)
            
            # 병렬로 취소 처리
            cancel_tasks = [self.cancel_order(order_id) for order_id in orders_to_cancel]
            results = await asyncio.gather(*cancel_tasks, return_exceptions=True)
            
            cancelled_count = sum(1 for result in results if result is True)
            
            self.logger.info(f"주문 일괄 취소 완료: {cancelled_count}/{len(orders_to_cancel)}")
            return cancelled_count
        
        except Exception as e:
            self.logger.error(f"일괄 주문 취소 오류: {e}")
            return 0
    
    async def _submit_order(self, order: Order) -> bool:
        """주문 제출
        
        Args:
            order: 주문 객체
        
        Returns:
            제출 성공 여부
        """
        try:
            # 주문 구분 코드 결정
            if order.order_type == OrderType.MARKET:
                ord_dvsn = "01"  # 시장가
            else:
                ord_dvsn = "00"  # 지정가
            
            # KIS API 주문 제출
            result = await self.kis_api.place_order(
                symbol=order.symbol,
                side=order.side.value,
                quantity=str(order.quantity),
                price=str(int(order.price)) if order.price else "0",
                ord_dvsn=ord_dvsn
            )
            
            if result.get('success', False):
                order.status = OrderStatus.SUBMITTED
                order.kis_order_id = result.get('order_id')
                order.updated_at = datetime.now()
                
                self.logger.info(f"주문 제출 성공: {order.symbol} KIS주문번호: {order.kis_order_id}")
                return True
            else:
                order.status = OrderStatus.REJECTED
                order.notes = result.get('message', '주문 거부')
                order.updated_at = datetime.now()
                
                self.logger.error(f"주문 제출 실패: {result.get('message', '')}")
                return False
        
        except Exception as e:
            self.logger.error(f"주문 제출 오류: {e}")
            order.status = OrderStatus.REJECTED
            order.notes = f"제출 오류: {str(e)}"
            order.updated_at = datetime.now()
            return False
    
    async def _validate_order(self, symbol: str, side: OrderSide, quantity: int, 
                            price: Optional[float]) -> Dict[str, Any]:
        """주문 검증
        
        Args:
            symbol: 종목코드
            side: 주문 방향
            quantity: 수량
            price: 가격
        
        Returns:
            검증 결과
        """
        try:
            # 기본 검증
            if quantity <= 0:
                return {'valid': False, 'reason': '수량이 0 이하'}
            
            if price is not None and price <= 0:
                return {'valid': False, 'reason': '가격이 0 이하'}
            
            # 계좌 잔고 확인
            balance = await self.kis_api.get_balance()
            if not balance.get('success', False):
                return {'valid': False, 'reason': '계좌 정보 조회 실패'}
            
            # 매수 주문 검증
            if side == OrderSide.BUY:
                available_cash = float(balance.get('data', {}).get('dnca_tot_amt', 0))
                required_cash = quantity * (price or 0)
                
                if required_cash > available_cash:
                    return {'valid': False, 'reason': f'자금 부족 (필요: {required_cash:,.0f}, 보유: {available_cash:,.0f})'}
            
            # 매도 주문 검증
            elif side == OrderSide.SELL:
                positions = balance.get('data', {}).get('output1', [])
                position = None
                
                for pos in positions:
                    if pos.get('pdno') == symbol:
                        position = pos
                        break
                
                if not position:
                    return {'valid': False, 'reason': '보유 포지션 없음'}
                
                available_quantity = int(position.get('ord_psbl_qty', 0))
                if quantity > available_quantity:
                    return {'valid': False, 'reason': f'수량 부족 (필요: {quantity}, 보유: {available_quantity})'}
            
            # 시장 시간 확인
            now = datetime.now()
            if now.weekday() >= 5:  # 주말
                return {'valid': False, 'reason': '주말 거래 불가'}
            
            if not (9 <= now.hour < 16):  # 장시간 외
                return {'valid': False, 'reason': '장시간 외 거래 불가'}
            
            return {'valid': True, 'reason': '검증 통과'}
        
        except Exception as e:
            self.logger.error(f"주문 검증 오류: {e}")
            return {'valid': False, 'reason': f'검증 오류: {str(e)}'}
    
    async def _check_order_limits(self) -> bool:
        """주문 제한 확인
        
        Returns:
            주문 가능 여부
        """
        try:
            current_minute = datetime.now().strftime('%Y%m%d%H%M')
            
            if current_minute not in self.order_count_tracker:
                return True
            
            current_count = self.order_count_tracker[current_minute]
            return current_count < self.max_orders_per_minute
        
        except Exception as e:
            self.logger.error(f"주문 제한 확인 오류: {e}")
            return False
    
    def _update_order_count(self):
        """주문 수 추적 업데이트"""
        try:
            current_minute = datetime.now().strftime('%Y%m%d%H%M')
            
            if current_minute not in self.order_count_tracker:
                self.order_count_tracker[current_minute] = 0
            
            self.order_count_tracker[current_minute] += 1
            
            # 오래된 데이터 정리 (1시간 이상)
            cutoff_time = (datetime.now() - timedelta(hours=1)).strftime('%Y%m%d%H%M')
            keys_to_remove = [k for k in self.order_count_tracker.keys() if k < cutoff_time]
            
            for key in keys_to_remove:
                del self.order_count_tracker[key]
        
        except Exception as e:
            self.logger.error(f"주문 수 추적 업데이트 오류: {e}")
    
    async def start_monitoring(self):
        """주문 모니터링 시작"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("주문 모니터링 시작")
    
    async def stop_monitoring(self):
        """주문 모니터링 중지"""
        self.is_monitoring = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("주문 모니터링 중지")
    
    async def _monitoring_loop(self):
        """주문 모니터링 루프"""
        while self.is_monitoring:
            try:
                # 활성 주문 상태 업데이트
                await self._update_order_status()
                
                # 만료된 주문 처리
                await self._handle_expired_orders()
                
                # 자동 주문 처리
                await self._process_auto_orders()
                
                # 5초 대기
                await asyncio.sleep(5)
            
            except Exception as e:
                self.logger.error(f"주문 모니터링 오류: {e}")
                await asyncio.sleep(10)
    
    async def _update_order_status(self):
        """활성 주문 상태 업데이트"""
        try:
            if not self.active_orders:
                return
            
            # 주문 상태 조회 (배치 처리)
            order_ids = list(self.active_orders.keys())
            
            for order_id in order_ids:
                order = self.active_orders.get(order_id)
                if not order or not order.kis_order_id:
                    continue
                
                # KIS API로 주문 상태 조회
                status_result = await self.kis_api.get_order_status(
                    order.symbol, order.kis_order_id
                )
                
                if status_result.get('success', False):
                    await self._process_order_status_update(order, status_result.get('data', {}))
        
        except Exception as e:
            self.logger.error(f"주문 상태 업데이트 오류: {e}")
    
    async def _process_order_status_update(self, order: Order, status_data: Dict[str, Any]):
        """주문 상태 업데이트 처리
        
        Args:
            order: 주문 객체
            status_data: 상태 데이터
        """
        try:
            # 체결 수량 및 가격 업데이트
            filled_qty = int(status_data.get('tot_ccld_qty', 0))
            filled_price = float(status_data.get('avg_prvs', 0))
            
            if filled_qty > order.filled_quantity:
                order.filled_quantity = filled_qty
                order.filled_price = filled_price
                order.updated_at = datetime.now()
                
                # 상태 업데이트
                if filled_qty >= order.quantity:
                    order.status = OrderStatus.FILLED
                    
                    # 활성 주문에서 제거
                    if order.order_id in self.active_orders:
                        del self.active_orders[order.order_id]
                    
                    # 히스토리에 추가
                    self.order_history.append(order)
                    
                    # 수수료 계산
                    order.commission = filled_qty * filled_price * self.commission_rate
                    
                    self.logger.info(f"주문 체결 완료: {order.symbol} {filled_qty}주 @ {filled_price}")
                    
                    # 체결 후 처리
                    await self._handle_order_filled(order)
                
                elif filled_qty > 0:
                    order.status = OrderStatus.PARTIALLY_FILLED
                    self.logger.info(f"주문 부분체결: {order.symbol} {filled_qty}/{order.quantity}주")
                
                # 데이터베이스 업데이트
                await self._update_order_in_db(order)
        
        except Exception as e:
            self.logger.error(f"주문 상태 업데이트 처리 오류: {e}")
    
    async def _handle_order_filled(self, order: Order):
        """주문 체결 후 처리
        
        Args:
            order: 체결된 주문
        """
        try:
            # 체결 콜백 함수들에게 알림
            await self._notify_fill_callbacks(order)
            
            # 포지션 업데이트는 PositionManager에서 처리
            
            # 자동 주문 생성 (손절/익절)
            if order.side == OrderSide.BUY:
                # 매수 체결시 손절/익절 주문 생성
                await self._create_auto_exit_orders(order)
            
            # 성과 추적
            await self._track_order_performance(order)
        
        except Exception as e:
            self.logger.error(f"주문 체결 후 처리 오류: {e}")
    
    async def _create_auto_exit_orders(self, buy_order: Order):
        """자동 청산 주문 생성
        
        Args:
            buy_order: 매수 주문
        """
        try:
            if not buy_order.filled_price:
                return
            
            # 손절가 계산 (2% 손실)
            stop_loss_price = buy_order.filled_price * 0.98
            
            # 익절가 계산 (3% 수익)
            take_profit_price = buy_order.filled_price * 1.03
            
            # 손절 주문 생성
            stop_order = await self.create_order(
                symbol=buy_order.symbol,
                side=OrderSide.SELL,
                order_type=OrderType.STOP_LOSS,
                quantity=buy_order.filled_quantity,
                price=stop_loss_price,
                stop_price=stop_loss_price
            )
            
            if stop_order:
                self.stop_loss_orders[buy_order.symbol] = stop_order
            
            # 익절 주문 생성
            profit_order = await self.create_order(
                symbol=buy_order.symbol,
                side=OrderSide.SELL,
                order_type=OrderType.TAKE_PROFIT,
                quantity=buy_order.filled_quantity,
                price=take_profit_price
            )
            
            if profit_order:
                self.take_profit_orders[buy_order.symbol] = profit_order
        
        except Exception as e:
            self.logger.error(f"자동 청산 주문 생성 오류: {e}")
    
    async def _handle_expired_orders(self):
        """만료된 주문 처리"""
        try:
            current_time = datetime.now()
            expired_orders = []
            
            for order_id, order in self.active_orders.items():
                if order.status == OrderStatus.SUBMITTED:
                    elapsed = (current_time - order.created_at).total_seconds()
                    if elapsed > self.order_timeout:
                        expired_orders.append(order_id)
            
            # 만료된 주문 취소
            for order_id in expired_orders:
                await self.cancel_order(order_id)
                self.logger.info(f"만료된 주문 취소: {order_id}")
        
        except Exception as e:
            self.logger.error(f"만료 주문 처리 오류: {e}")
    
    async def _process_auto_orders(self):
        """자동 주문 처리"""
        try:
            # 손절/익절 주문 모니터링
            for symbol in list(self.stop_loss_orders.keys()):
                await self._check_stop_loss_trigger(symbol)
            
            for symbol in list(self.take_profit_orders.keys()):
                await self._check_take_profit_trigger(symbol)
        
        except Exception as e:
            self.logger.error(f"자동 주문 처리 오류: {e}")
    
    async def _check_stop_loss_trigger(self, symbol: str):
        """손절 주문 트리거 확인
        
        Args:
            symbol: 종목코드
        """
        try:
            if symbol not in self.stop_loss_orders:
                return
            
            stop_order = self.stop_loss_orders[symbol]
            
            # 현재가 조회
            market_data = await self.db.get_latest_market_data(symbol)
            if not market_data:
                return
            
            current_price = market_data.get('price', 0)
            
            # 손절 조건 확인
            if current_price <= stop_order.stop_price:
                # 시장가 매도 주문 생성
                market_sell_order = await self.create_order(
                    symbol=symbol,
                    side=OrderSide.SELL,
                    order_type=OrderType.MARKET,
                    quantity=stop_order.quantity
                )
                
                if market_sell_order:
                    # 기존 손절 주문 취소
                    await self.cancel_order(stop_order.order_id)
                    del self.stop_loss_orders[symbol]
                    
                    self.logger.info(f"손절 주문 실행: {symbol} @ {current_price}")
        
        except Exception as e:
            self.logger.error(f"손절 주문 트리거 확인 오류: {e}")
    
    async def _check_take_profit_trigger(self, symbol: str):
        """익절 주문 트리거 확인
        
        Args:
            symbol: 종목코드
        """
        try:
            if symbol not in self.take_profit_orders:
                return
            
            profit_order = self.take_profit_orders[symbol]
            
            # 현재가 조회
            market_data = await self.db.get_latest_market_data(symbol)
            if not market_data:
                return
            
            current_price = market_data.get('price', 0)
            
            # 익절 조건 확인
            if current_price >= profit_order.price:
                # 지정가 매도 주문 실행 (이미 생성되어 있음)
                self.logger.info(f"익절 조건 달성: {symbol} @ {current_price}")
        
        except Exception as e:
            self.logger.error(f"익절 주문 트리거 확인 오류: {e}")
    
    async def _restore_active_orders(self):
        """기존 활성 주문 복구"""
        try:
            # 데이터베이스에서 활성 주문 조회
            active_orders_data = await self.db.get_active_orders()
            
            for order_data in active_orders_data:
                order = Order(
                    order_id=order_data['order_id'],
                    symbol=order_data['symbol'],
                    side=OrderSide(order_data['side']),
                    order_type=OrderType(order_data['order_type']),
                    quantity=order_data['quantity'],
                    price=order_data['price'],
                    stop_price=order_data['stop_price'],
                    status=OrderStatus(order_data['status']),
                    created_at=order_data['created_at'],
                    updated_at=order_data['updated_at'],
                    filled_quantity=order_data['filled_quantity'],
                    filled_price=order_data['filled_price'],
                    commission=order_data['commission'],
                    kis_order_id=order_data['kis_order_id'],
                    parent_decision_id=order_data['parent_decision_id'],
                    notes=order_data['notes']
                )
                
                self.active_orders[order.order_id] = order
            
            self.logger.info(f"활성 주문 복구 완료: {len(self.active_orders)}개")
        
        except Exception as e:
            self.logger.error(f"활성 주문 복구 오류: {e}")
    
    async def _save_order_to_db(self, order: Order):
        """주문을 데이터베이스에 저장
        
        Args:
            order: 주문 객체
        """
        try:
            await self.db.insert_order({
                'order_id': order.order_id,
                'symbol': order.symbol,
                'side': order.side.value,
                'order_type': order.order_type.value,
                'quantity': order.quantity,
                'price': order.price,
                'stop_price': order.stop_price,
                'status': order.status.value,
                'created_at': order.created_at,
                'updated_at': order.updated_at,
                'filled_quantity': order.filled_quantity,
                'filled_price': order.filled_price,
                'commission': order.commission,
                'kis_order_id': order.kis_order_id,
                'parent_decision_id': order.parent_decision_id,
                'notes': order.notes
            })
        
        except Exception as e:
            self.logger.error(f"주문 DB 저장 오류: {e}")
    
    async def _update_order_in_db(self, order: Order):
        """데이터베이스의 주문 정보 업데이트
        
        Args:
            order: 주문 객체
        """
        try:
            await self.db.update_order(order.order_id, {
                'status': order.status.value,
                'updated_at': order.updated_at,
                'filled_quantity': order.filled_quantity,
                'filled_price': order.filled_price,
                'commission': order.commission,
                'notes': order.notes
            })
        
        except Exception as e:
            self.logger.error(f"주문 DB 업데이트 오류: {e}")
    
    async def _track_order_performance(self, order: Order):
        """주문 성과 추적
        
        Args:
            order: 체결된 주문
        """
        try:
            # 성과 메트릭 계산 및 저장
            # 실제 구현에서는 더 상세한 성과 분석 필요
            pass
        
        except Exception as e:
            self.logger.error(f"주문 성과 추적 오류: {e}")
    
    def get_order_summary(self) -> Dict[str, Any]:
        """주문 요약 정보
        
        Returns:
            주문 요약
        """
        try:
            active_count = len(self.active_orders)
            total_count = len(self.order_history) + active_count
            
            # 오늘 주문 통계
            today = datetime.now().date()
            today_orders = [
                order for order in self.order_history
                if order.created_at.date() == today
            ]
            
            return {
                'active_orders': active_count,
                'total_orders': total_count,
                'today_orders': len(today_orders),
                'filled_orders': len([o for o in self.order_history if o.status == OrderStatus.FILLED]),
                'cancelled_orders': len([o for o in self.order_history if o.status == OrderStatus.CANCELLED]),
                'order_success_rate': self._calculate_success_rate(),
                'avg_fill_time': self._calculate_avg_fill_time()
            }
        
        except Exception as e:
            self.logger.error(f"주문 요약 정보 생성 오류: {e}")
            return {}
    
    def _calculate_success_rate(self) -> float:
        """주문 성공률 계산"""
        try:
            if not self.order_history:
                return 0.0
            
            filled_count = len([o for o in self.order_history if o.status == OrderStatus.FILLED])
            return (filled_count / len(self.order_history)) * 100
        
        except Exception:
            return 0.0
    
    def _calculate_avg_fill_time(self) -> float:
        """평균 체결 시간 계산 (초)"""
        try:
            filled_orders = [o for o in self.order_history if o.status == OrderStatus.FILLED]
            
            if not filled_orders:
                return 0.0
            
            total_time = sum(
                (order.updated_at - order.created_at).total_seconds()
                for order in filled_orders
            )
            
            return total_time / len(filled_orders)
        
        except Exception:
            return 0.0
    
    async def close(self):
        """주문 관리자 종료"""
        try:
            # 모니터링 중지
            await self.stop_monitoring()
            
            # 모든 활성 주문 취소
            await self.cancel_all_orders()
            
            self.logger.info("주문 관리자 종료 완료")
        
        except Exception as e:
            self.logger.error(f"주문 관리자 종료 오류: {e}")