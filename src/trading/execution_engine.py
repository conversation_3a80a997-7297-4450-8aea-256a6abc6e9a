# -*- coding: utf-8 -*-
"""
실행 엔진

AI 의사결정을 실제 주문으로 변환하고 실행
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager
from ..data_collection.kis_api import KISApi
from ..ai_analysis.decision_engine import DecisionEngine, TradingDecision, DecisionType
from .order_manager import OrderManager, Order, OrderType, OrderSide
from .position_manager import PositionManager

class ExecutionStatus(Enum):
    """실행 상태"""
    PENDING = "pending"      # 대기중
    EXECUTING = "executing"  # 실행중
    COMPLETED = "completed"  # 완료
    FAILED = "failed"        # 실패
    CANCELLED = "cancelled"  # 취소

@dataclass
class ExecutionPlan:
    """실행 계획 데이터 클래스"""
    decision: TradingDecision
    symbol: str
    action: str  # buy, sell, hold
    quantity: int
    price: Optional[float]
    order_type: OrderType
    priority: int  # 실행 우선순위 (1=최고)
    max_slippage: float  # 최대 슬리피지 (%)
    timeout: int  # 실행 타임아웃 (초)
    status: ExecutionStatus
    created_time: datetime
    executed_time: Optional[datetime] = None
    order_id: Optional[str] = None
    execution_price: Optional[float] = None
    execution_quantity: int = 0
    error_message: Optional[str] = None

class ExecutionEngine:
    """
    실행 엔진 클래스
    
    AI 의사결정을 실제 주문으로 변환하고 실행
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, 
                 kis_api: KISApi, order_manager: OrderManager, position_manager: PositionManager):
        """실행 엔진 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
            kis_api: 한국투자증권 API
            order_manager: 주문 관리자
            position_manager: 포지션 관리자
        """
        self.config = config_manager
        self.db = db_manager
        self.kis_api = kis_api
        self.order_manager = order_manager
        self.position_manager = position_manager
        self.logger = get_logger()
        
        # 실행 계획 큐
        self.execution_queue: List[ExecutionPlan] = []
        self.executing_plans: Dict[str, ExecutionPlan] = {}  # order_id -> plan
        
        # 거래 설정
        trading_config = self.config.get('trading', {})
        self.max_concurrent_executions = trading_config.get('max_concurrent_executions', 5)
        self.default_timeout = trading_config.get('order_timeout', 60)  # 1분
        self.max_slippage = trading_config.get('max_slippage', 0.005)  # 0.5%
        self.position_size_limit = trading_config.get('max_position_ratio', 0.3)  # 30%
        
        # 분할 주문 설정
        self.split_orders = trading_config.get('split_orders', True)
        self.max_splits = trading_config.get('max_splits', 3)
        self.split_interval = trading_config.get('split_interval', 30)  # 30초
        self.order_split_threshold = trading_config.get('order_split_threshold', 1000000)  # 100만원
        
        # 주문 설정
        self.order_type = trading_config.get('order_type', 'limit')
        self.price_adjustment = trading_config.get('price_adjustment', 0.001)  # 0.1%
        self.max_order_age = trading_config.get('max_order_age', 300)  # 5분
        self.cancel_timeout = trading_config.get('cancel_timeout', 5)  # 5초
        
        # 실행 제어
        self.is_executing = False
        self.execution_task = None
        
        # 성과 추적
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_slippage = 0.0
        self.avg_execution_time = 0.0
    
    async def initialize(self):
        """실행 엔진 초기화"""
        try:
            # 미완료 실행 계획 복구
            await self._restore_pending_executions()
            
            # 실행 루프 시작
            await self.start_execution_loop()
            
            self.logger.info("실행 엔진 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"실행 엔진 초기화 오류: {e}")
    
    async def execute_decision(self, decision: TradingDecision) -> bool:
        """AI 의사결정 실행
        
        Args:
            decision: AI 의사결정
        
        Returns:
            실행 계획 생성 성공 여부
        """
        try:
            # 의사결정 검증
            if not await self._validate_decision(decision):
                return False
            
            # 실행 계획 생성
            execution_plans = await self._create_execution_plans(decision)
            
            if not execution_plans:
                self.logger.warning(f"실행 계획 생성 실패: {decision.symbol}")
                return False
            
            # 실행 큐에 추가
            for plan in execution_plans:
                await self._add_to_execution_queue(plan)
            
            self.logger.info(f"실행 계획 생성 완료: {decision.symbol} {len(execution_plans)}개")
            return True
        
        except Exception as e:
            self.logger.error(f"의사결정 실행 오류: {e}")
            return False
    
    async def _validate_decision(self, decision: TradingDecision) -> bool:
        """의사결정 검증
        
        Args:
            decision: AI 의사결정
        
        Returns:
            검증 통과 여부
        """
        try:
            # 기본 검증
            if not decision.symbol or not decision.action:
                self.logger.warning("의사결정 기본 정보 누락")
                return False
            
            # 신뢰도 검증
            min_confidence = self.config.get('trading.min_confidence_threshold', 0.7)
            if decision.confidence < min_confidence:
                self.logger.warning(f"신뢰도 부족: {decision.confidence} < {min_confidence}")
                return False
            
            # 시장 시간 검증
            if not await self._is_market_open():
                self.logger.warning("시장 시간 외 거래 시도")
                return False
            
            # 리스크 검증
            if decision.risk_score > 0.8:
                self.logger.warning(f"리스크 점수 과다: {decision.risk_score}")
                return False
            
            # 포지션 한도 검증
            if decision.action in ['buy', 'add']:
                if not await self._check_position_limits(decision.symbol, decision.target_quantity):
                    return False
            
            # 자금 검증
            if decision.action in ['buy', 'add']:
                if not await self._check_available_funds(decision.symbol, decision.target_quantity, decision.target_price):
                    return False
            
            return True
        
        except Exception as e:
            self.logger.error(f"의사결정 검증 오류: {e}")
            return False
    
    async def _create_execution_plans(self, decision: TradingDecision) -> List[ExecutionPlan]:
        """실행 계획 생성
        
        Args:
            decision: AI 의사결정
        
        Returns:
            실행 계획 리스트
        """
        try:
            plans = []
            
            if decision.action == 'buy':
                plans.extend(await self._create_buy_plans(decision))
            elif decision.action == 'sell':
                plans.extend(await self._create_sell_plans(decision))
            elif decision.action == 'add':
                plans.extend(await self._create_add_plans(decision))
            elif decision.action == 'reduce':
                plans.extend(await self._create_reduce_plans(decision))
            elif decision.action == 'close':
                plans.extend(await self._create_close_plans(decision))
            
            return plans
        
        except Exception as e:
            self.logger.error(f"실행 계획 생성 오류: {e}")
            return []
    
    async def _create_buy_plans(self, decision: TradingDecision) -> List[ExecutionPlan]:
        """매수 실행 계획 생성
        
        Args:
            decision: AI 의사결정
        
        Returns:
            매수 실행 계획 리스트
        """
        try:
            plans = []
            
            # 분할 매수 여부 결정
            total_quantity = decision.target_quantity
            total_value = total_quantity * decision.target_price if decision.target_price else 0
            
            if self.split_orders and total_value > self.order_split_threshold:
                # 분할 매수
                split_count = min(self.max_splits, (total_value + self.order_split_threshold - 1) // self.order_split_threshold)
                quantity_per_order = total_quantity // split_count
                remaining_quantity = total_quantity % split_count
                
                for i in range(split_count):
                    quantity = quantity_per_order
                    if i == split_count - 1:  # 마지막 주문에 나머지 수량 추가
                        quantity += remaining_quantity
                    
                    # 주문 타입 결정
                    order_type = self._determine_order_type(decision, i)
                    
                    # 가격 결정
                    price = await self._determine_execution_price(decision, order_type, 'buy')
                    
                    plan = ExecutionPlan(
                        decision=decision,
                        symbol=decision.symbol,
                        action='buy',
                        quantity=quantity,
                        price=price,
                        order_type=order_type,
                        priority=decision.urgency,
                        max_slippage=self.max_slippage,
                        timeout=self.default_timeout,
                        status=ExecutionStatus.PENDING,
                        created_time=datetime.now()
                    )
                    
                    plans.append(plan)
            else:
                # 단일 매수
                order_type = self._determine_order_type(decision, 0)
                price = await self._determine_execution_price(decision, order_type, 'buy')
                
                plan = ExecutionPlan(
                    decision=decision,
                    symbol=decision.symbol,
                    action='buy',
                    quantity=total_quantity,
                    price=price,
                    order_type=order_type,
                    priority=decision.urgency,
                    max_slippage=self.max_slippage,
                    timeout=self.default_timeout,
                    status=ExecutionStatus.PENDING,
                    created_time=datetime.now()
                )
                
                plans.append(plan)
            
            return plans
        
        except Exception as e:
            self.logger.error(f"매수 계획 생성 오류: {e}")
            return []
    
    async def _create_sell_plans(self, decision: TradingDecision) -> List[ExecutionPlan]:
        """매도 실행 계획 생성
        
        Args:
            decision: AI 의사결정
        
        Returns:
            매도 실행 계획 리스트
        """
        try:
            plans = []
            
            # 현재 포지션 확인
            position = self.position_manager.get_position(decision.symbol)
            if not position:
                self.logger.warning(f"매도할 포지션이 없음: {decision.symbol}")
                return []
            
            # 매도 수량 결정
            sell_quantity = min(decision.target_quantity, position.quantity)
            
            if sell_quantity <= 0:
                return []
            
            # 긴급 매도 여부 확인
            is_urgent = decision.urgency >= 8 or decision.decision_type == DecisionType.EMERGENCY_EXIT
            
            if is_urgent:
                # 긴급 시장가 매도
                plan = ExecutionPlan(
                    decision=decision,
                    symbol=decision.symbol,
                    action='sell',
                    quantity=sell_quantity,
                    price=None,  # 시장가
                    order_type=OrderType.MARKET,
                    priority=1,  # 최고 우선순위
                    max_slippage=5.0,  # 긴급시 슬리피지 허용
                    timeout=60,  # 1분 타임아웃
                    status=ExecutionStatus.PENDING,
                    created_time=datetime.now()
                )
                
                plans.append(plan)
            else:
                # 일반 매도
                order_type = self._determine_order_type(decision, 0)
                price = await self._determine_execution_price(decision, order_type, 'sell')
                
                plan = ExecutionPlan(
                    decision=decision,
                    symbol=decision.symbol,
                    action='sell',
                    quantity=sell_quantity,
                    price=price,
                    order_type=order_type,
                    priority=decision.urgency,
                    max_slippage=self.max_slippage,
                    timeout=self.default_timeout,
                    status=ExecutionStatus.PENDING,
                    created_time=datetime.now()
                )
                
                plans.append(plan)
            
            return plans
        
        except Exception as e:
            self.logger.error(f"매도 계획 생성 오류: {e}")
            return []
    
    async def _create_add_plans(self, decision: TradingDecision) -> List[ExecutionPlan]:
        """추가 매수 실행 계획 생성"""
        # 매수 계획과 동일하지만 기존 포지션 고려
        return await self._create_buy_plans(decision)
    
    async def _create_reduce_plans(self, decision: TradingDecision) -> List[ExecutionPlan]:
        """포지션 축소 실행 계획 생성"""
        # 매도 계획과 동일하지만 부분 매도
        return await self._create_sell_plans(decision)
    
    async def _create_close_plans(self, decision: TradingDecision) -> List[ExecutionPlan]:
        """포지션 청산 실행 계획 생성"""
        try:
            position = self.position_manager.get_position(decision.symbol)
            if not position:
                return []
            
            # 전량 매도로 설정
            decision.target_quantity = position.quantity
            return await self._create_sell_plans(decision)
        
        except Exception as e:
            self.logger.error(f"청산 계획 생성 오류: {e}")
            return []
    
    def _determine_order_type(self, decision: TradingDecision, order_index: int) -> OrderType:
        """주문 타입 결정
        
        Args:
            decision: AI 의사결정
            order_index: 주문 인덱스 (분할 주문시)
        
        Returns:
            주문 타입
        """
        try:
            # 긴급도에 따른 주문 타입 결정
            if decision.urgency >= 9:
                return OrderType.MARKET
            elif decision.urgency >= 7:
                return OrderType.LIMIT
            else:
                # 첫 번째 주문은 지정가, 나머지는 시장가
                return OrderType.LIMIT if order_index == 0 else OrderType.MARKET
        
        except Exception as e:
            self.logger.error(f"주문 타입 결정 오류: {e}")
            return OrderType.LIMIT
    
    async def _determine_execution_price(self, decision: TradingDecision, 
                                       order_type: OrderType, side: str) -> Optional[float]:
        """실행 가격 결정
        
        Args:
            decision: AI 의사결정
            order_type: 주문 타입
            side: 매매 구분 (buy/sell)
        
        Returns:
            실행 가격
        """
        try:
            if order_type == OrderType.MARKET:
                return None  # 시장가
            
            # 현재가 조회
            market_data = await self.db.get_latest_market_data(decision.symbol)
            if not market_data:
                return decision.target_price
            
            current_price = market_data.get('price', decision.target_price)
            
            # 호가창 정보 조회
            orderbook = await self.db.get_latest_orderbook(decision.symbol)
            
            if side == 'buy':
                if orderbook and 'ask_price1' in orderbook:
                    # 매도 1호가 기준
                    base_price = orderbook['ask_price1']
                else:
                    base_price = current_price
                
                # 약간 높은 가격으로 설정 (체결 확률 증가)
                return base_price * (1 + 0.001)  # 0.1% 프리미엄
            
            else:  # sell
                if orderbook and 'bid_price1' in orderbook:
                    # 매수 1호가 기준
                    base_price = orderbook['bid_price1']
                else:
                    base_price = current_price
                
                # 약간 낮은 가격으로 설정 (체결 확률 증가)
                return base_price * (1 - 0.001)  # 0.1% 디스카운트
        
        except Exception as e:
            self.logger.error(f"실행 가격 결정 오류: {e}")
            return decision.target_price
    
    async def _add_to_execution_queue(self, plan: ExecutionPlan):
        """실행 큐에 계획 추가
        
        Args:
            plan: 실행 계획
        """
        try:
            # 우선순위에 따라 정렬하여 삽입
            inserted = False
            for i, existing_plan in enumerate(self.execution_queue):
                if plan.priority < existing_plan.priority:  # 낮은 숫자가 높은 우선순위
                    self.execution_queue.insert(i, plan)
                    inserted = True
                    break
            
            if not inserted:
                self.execution_queue.append(plan)
            
            self.logger.info(f"실행 큐 추가: {plan.symbol} {plan.action} {plan.quantity}주 (우선순위: {plan.priority})")
        
        except Exception as e:
            self.logger.error(f"실행 큐 추가 오류: {e}")
    
    async def start_execution_loop(self):
        """실행 루프 시작"""
        if self.is_executing:
            return
        
        self.is_executing = True
        self.execution_task = asyncio.create_task(self._execution_loop())
        self.logger.info("실행 루프 시작")
    
    async def stop_execution_loop(self):
        """실행 루프 중지"""
        self.is_executing = False
        
        if self.execution_task:
            self.execution_task.cancel()
            try:
                await self.execution_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("실행 루프 중지")
    
    async def _execution_loop(self):
        """실행 루프"""
        while self.is_executing:
            try:
                # 실행 가능한 계획 확인
                if not self.execution_queue:
                    await asyncio.sleep(1)
                    continue
                
                # 동시 실행 제한 확인
                if len(self.executing_plans) >= self.max_concurrent_executions:
                    await asyncio.sleep(1)
                    continue
                
                # 다음 실행 계획 가져오기
                plan = self.execution_queue.pop(0)
                
                # 실행 시작
                await self._execute_plan(plan)
                
            except Exception as e:
                self.logger.error(f"실행 루프 오류: {e}")
                await asyncio.sleep(5)
    
    async def _execute_plan(self, plan: ExecutionPlan):
        """실행 계획 실행
        
        Args:
            plan: 실행 계획
        """
        try:
            plan.status = ExecutionStatus.EXECUTING
            
            # 주문 생성
            order = Order(
                symbol=plan.symbol,
                side=OrderSide.BUY if plan.action == 'buy' else OrderSide.SELL,
                order_type=plan.order_type,
                quantity=plan.quantity,
                price=plan.price,
                timeout=plan.timeout
            )
            
            # 주문 제출
            order_result = await self.order_manager.submit_order(order)
            
            if order_result:
                plan.order_id = order.order_id
                self.executing_plans[order.order_id] = plan
                
                # 주문 모니터링 시작
                asyncio.create_task(self._monitor_execution(plan))
                
                self.logger.info(f"주문 제출 완료: {plan.symbol} {plan.action} {plan.quantity}주 @ {plan.price}")
            else:
                plan.status = ExecutionStatus.FAILED
                plan.error_message = "주문 제출 실패"
                self.failed_executions += 1
                
                self.logger.error(f"주문 제출 실패: {plan.symbol} {plan.action}")
        
        except Exception as e:
            plan.status = ExecutionStatus.FAILED
            plan.error_message = str(e)
            self.failed_executions += 1
            
            self.logger.error(f"실행 계획 실행 오류: {e}")
    
    async def _monitor_execution(self, plan: ExecutionPlan):
        """실행 모니터링
        
        Args:
            plan: 실행 계획
        """
        try:
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < plan.timeout:
                # 주문 상태 확인
                order = self.order_manager.get_order(plan.order_id)
                
                if not order:
                    break
                
                if order.status == 'filled':
                    # 체결 완료
                    plan.status = ExecutionStatus.COMPLETED
                    plan.executed_time = datetime.now()
                    plan.execution_price = order.filled_price
                    plan.execution_quantity = order.filled_quantity
                    
                    # 슬리피지 계산
                    if plan.price and plan.execution_price:
                        slippage = abs(plan.execution_price - plan.price) / plan.price * 100
                        self.total_slippage += slippage
                    
                    # 실행 시간 계산
                    execution_time = (plan.executed_time - plan.created_time).total_seconds()
                    self.avg_execution_time = (self.avg_execution_time * self.total_executions + execution_time) / (self.total_executions + 1)
                    
                    self.successful_executions += 1
                    self.total_executions += 1
                    
                    # 포지션 업데이트
                    await self.position_manager.update_position_from_order(
                        plan.symbol, plan.action, plan.execution_quantity, 
                        plan.execution_price, plan.order_id
                    )
                    
                    self.logger.info(f"주문 체결 완료: {plan.symbol} {plan.execution_quantity}주 @ {plan.execution_price}")
                    break
                
                elif order.status in ['cancelled', 'rejected']:
                    # 주문 취소/거부
                    plan.status = ExecutionStatus.FAILED
                    plan.error_message = f"주문 {order.status}"
                    self.failed_executions += 1
                    
                    self.logger.warning(f"주문 {order.status}: {plan.symbol}")
                    break
                
                # 슬리피지 확인
                if await self._check_slippage_limit(plan):
                    # 슬리피지 초과시 주문 취소
                    await self.order_manager.cancel_order(plan.order_id)
                    plan.status = ExecutionStatus.CANCELLED
                    plan.error_message = "슬리피지 초과"
                    
                    self.logger.warning(f"슬리피지 초과로 주문 취소: {plan.symbol}")
                    break
                
                await asyncio.sleep(1)
            
            else:
                # 타임아웃
                await self.order_manager.cancel_order(plan.order_id)
                plan.status = ExecutionStatus.FAILED
                plan.error_message = "실행 타임아웃"
                self.failed_executions += 1
                
                self.logger.warning(f"실행 타임아웃: {plan.symbol}")
            
            # 실행 중 목록에서 제거
            if plan.order_id in self.executing_plans:
                del self.executing_plans[plan.order_id]
            
            # 실행 결과 저장
            await self._save_execution_result(plan)
        
        except Exception as e:
            self.logger.error(f"실행 모니터링 오류: {e}")
    
    async def _check_slippage_limit(self, plan: ExecutionPlan) -> bool:
        """슬리피지 한도 확인
        
        Args:
            plan: 실행 계획
        
        Returns:
            슬리피지 초과 여부
        """
        try:
            if not plan.price:
                return False  # 시장가 주문은 슬리피지 체크 안함
            
            # 현재가 조회
            market_data = await self.db.get_latest_market_data(plan.symbol)
            if not market_data:
                return False
            
            current_price = market_data.get('price', plan.price)
            
            # 슬리피지 계산
            slippage = abs(current_price - plan.price) / plan.price * 100
            
            return slippage > plan.max_slippage
        
        except Exception as e:
            self.logger.error(f"슬리피지 확인 오류: {e}")
            return False
    
    async def _is_market_open(self) -> bool:
        """시장 개장 여부 확인
        
        Returns:
            시장 개장 여부
        """
        try:
            now = datetime.now()
            
            # 주말 확인
            if now.weekday() >= 5:  # 토요일(5), 일요일(6)
                return False
            
            # 거래 시간 확인 (9:00 ~ 15:30)
            market_open = now.replace(hour=9, minute=0, second=0).replace(microsecond=0)
            market_close = now.replace(hour=15, minute=30, second=0).replace(microsecond=0)
            
            return market_open <= now <= market_close
        
        except Exception as e:
            self.logger.error(f"시장 시간 확인 오류: {e}")
            return False
    
    async def _check_position_limits(self, symbol: str, quantity: int) -> bool:
        """포지션 한도 확인
        
        Args:
            symbol: 종목코드
            quantity: 수량
        
        Returns:
            한도 내 여부
        """
        try:
            # 현재 포지션 확인
            position = self.position_manager.get_position(symbol)
            current_quantity = position.quantity if position else 0
            
            # 총 포지션 크기 계산
            total_quantity = current_quantity + quantity
            
            # 시장가 기준 포지션 가치 계산
            market_data = await self.db.get_latest_market_data(symbol)
            if not market_data:
                return False
            
            current_price = market_data.get('price', 0)
            position_value = total_quantity * current_price
            
            # 전체 포트폴리오 가치 대비 비율 확인
            portfolio_summary = self.position_manager.get_portfolio_summary()
            total_portfolio_value = portfolio_summary.get('total_market_value', 0)
            
            if total_portfolio_value > 0:
                position_ratio = position_value / total_portfolio_value
                if position_ratio > self.position_size_limit:
                    self.logger.warning(f"포지션 한도 초과: {symbol} {position_ratio:.2%} > {self.position_size_limit:.2%}")
                    return False
            
            return True
        
        except Exception as e:
            self.logger.error(f"포지션 한도 확인 오류: {e}")
            return False
    
    async def _check_available_funds(self, symbol: str, quantity: int, price: float) -> bool:
        """가용 자금 확인
        
        Args:
            symbol: 종목코드
            quantity: 수량
            price: 가격
        
        Returns:
            자금 충분 여부
        """
        try:
            # 필요 자금 계산
            required_amount = quantity * price
            
            # 계좌 잔고 조회
            balance_result = await self.kis_api.get_balance()
            
            if not balance_result.get('success', False):
                return False
            
            # 가용 현금 확인
            balance_data = balance_result.get('data', {})
            available_cash = float(balance_data.get('output2', [{}])[0].get('dnca_tot_amt', 0))
            
            if available_cash < required_amount:
                self.logger.warning(f"자금 부족: 필요 {required_amount:,.0f}원, 가용 {available_cash:,.0f}원")
                return False
            
            return True
        
        except Exception as e:
            self.logger.error(f"가용 자금 확인 오류: {e}")
            return False
    
    async def _restore_pending_executions(self):
        """미완료 실행 계획 복구"""
        try:
            # 데이터베이스에서 미완료 실행 계획 조회
            # 실제 구현에서는 DB 스키마에 따라 조정 필요
            self.logger.info("미완료 실행 계획 복구 완료")
        
        except Exception as e:
            self.logger.error(f"실행 계획 복구 오류: {e}")
    
    async def _save_execution_result(self, plan: ExecutionPlan):
        """실행 결과 저장
        
        Args:
            plan: 실행 계획
        """
        try:
            await self.db.insert_execution_log({
                'symbol': plan.symbol,
                'action': plan.action,
                'quantity': plan.quantity,
                'target_price': plan.price,
                'execution_price': plan.execution_price,
                'execution_quantity': plan.execution_quantity,
                'status': plan.status.value,
                'order_id': plan.order_id,
                'created_time': plan.created_time,
                'executed_time': plan.executed_time,
                'error_message': plan.error_message,
                'decision_id': plan.decision.decision_id if hasattr(plan.decision, 'decision_id') else None
            })
        
        except Exception as e:
            self.logger.error(f"실행 결과 저장 오류: {e}")
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """실행 통계 조회
        
        Returns:
            실행 통계
        """
        try:
            success_rate = (self.successful_executions / self.total_executions * 100) if self.total_executions > 0 else 0
            avg_slippage = (self.total_slippage / self.successful_executions) if self.successful_executions > 0 else 0
            
            return {
                'total_executions': self.total_executions,
                'successful_executions': self.successful_executions,
                'failed_executions': self.failed_executions,
                'success_rate': success_rate,
                'avg_execution_time': self.avg_execution_time,
                'avg_slippage': avg_slippage,
                'pending_executions': len(self.execution_queue),
                'executing_plans': len(self.executing_plans)
            }
        
        except Exception as e:
            self.logger.error(f"실행 통계 조회 오류: {e}")
            return {}
    
    def get_pending_executions(self) -> List[ExecutionPlan]:
        """대기 중인 실행 계획 조회
        
        Returns:
            대기 중인 실행 계획 리스트
        """
        return self.execution_queue.copy()
    
    def get_executing_plans(self) -> List[ExecutionPlan]:
        """실행 중인 계획 조회
        
        Returns:
            실행 중인 계획 리스트
        """
        return list(self.executing_plans.values())
    
    async def cancel_execution(self, order_id: str) -> bool:
        """실행 취소
        
        Args:
            order_id: 주문 ID
        
        Returns:
            취소 성공 여부
        """
        try:
            # 실행 중인 계획에서 찾기
            if order_id in self.executing_plans:
                plan = self.executing_plans[order_id]
                
                # 주문 취소
                cancel_result = await self.order_manager.cancel_order(order_id)
                
                if cancel_result:
                    plan.status = ExecutionStatus.CANCELLED
                    plan.error_message = "사용자 취소"
                    
                    del self.executing_plans[order_id]
                    
                    self.logger.info(f"실행 취소 완료: {order_id}")
                    return True
            
            # 대기 중인 계획에서 찾기
            for i, plan in enumerate(self.execution_queue):
                if plan.order_id == order_id:
                    plan.status = ExecutionStatus.CANCELLED
                    self.execution_queue.pop(i)
                    
                    self.logger.info(f"대기 중인 실행 취소: {order_id}")
                    return True
            
            return False
        
        except Exception as e:
            self.logger.error(f"실행 취소 오류: {e}")
            return False
    
    async def cancel_all_executions(self) -> int:
        """모든 실행 취소
        
        Returns:
            취소된 실행 수
        """
        try:
            cancelled_count = 0
            
            # 실행 중인 계획 취소
            for order_id in list(self.executing_plans.keys()):
                if await self.cancel_execution(order_id):
                    cancelled_count += 1
            
            # 대기 중인 계획 취소
            for plan in self.execution_queue:
                plan.status = ExecutionStatus.CANCELLED
                cancelled_count += 1
            
            self.execution_queue.clear()
            
            self.logger.info(f"전체 실행 취소 완료: {cancelled_count}개")
            return cancelled_count
        
        except Exception as e:
            self.logger.error(f"전체 실행 취소 오류: {e}")
            return 0
    
    async def close(self):
        """실행 엔진 종료"""
        try:
            # 실행 루프 중지
            await self.stop_execution_loop()
            
            # 모든 실행 취소
            await self.cancel_all_executions()
            
            self.logger.info("실행 엔진 종료 완료")
        
        except Exception as e:
            self.logger.error(f"실행 엔진 종료 오류: {e}")