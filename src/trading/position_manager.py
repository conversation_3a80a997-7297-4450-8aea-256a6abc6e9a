# -*- coding: utf-8 -*-
"""
포지션 관리자

보유 포지션 추적 및 관리
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager
from ..data_collection.kis_api import KISApi

class PositionStatus(Enum):
    """포지션 상태"""
    OPEN = "open"      # 보유중
    CLOSED = "closed"  # 청산됨
    PARTIAL = "partial"  # 부분청산

@dataclass
class Position:
    """포지션 데이터 클래스"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_rate: float
    realized_pnl: float
    total_cost: float
    status: PositionStatus
    entry_time: datetime
    last_updated: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    max_profit: float = 0.0
    max_loss: float = 0.0
    holding_period: timedelta = timedelta()

class PositionManager:
    """
    포지션 관리자 클래스
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, kis_api: KISApi):
        """포지션 관리자 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
            kis_api: 한국투자증권 API
        """
        self.config = config_manager
        self.db = db_manager
        self.kis_api = kis_api
        self.logger = get_logger()
        
        # 포지션 추적
        self.positions: Dict[str, Position] = {}
        self.closed_positions: List[Position] = []
        
        # 설정
        self.update_interval = self.config.get('trading.position_update_interval', 10)  # 10초
        self.auto_stop_loss = self.config.get('trading.auto_stop_loss', True)
        self.default_stop_loss_rate = self.config.get('trading.default_stop_loss_rate', 0.02)  # 2%
        self.default_take_profit_rate = self.config.get('trading.default_take_profit_rate', 0.03)  # 3%
        
        # 모니터링
        self.monitoring_task = None
        self.is_monitoring = False
        
        # 성과 추적
        self.daily_pnl = 0.0
        self.total_realized_pnl = 0.0
        self.total_unrealized_pnl = 0.0
        self.win_count = 0
        self.loss_count = 0
        
        # 콜백 함수들
        self.position_callbacks = []
    
    async def initialize(self):
        """포지션 관리자 초기화"""
        try:
            # 기존 포지션 복구
            await self._restore_positions()
            
            # 모니터링 시작
            await self.start_monitoring()
            
            self.logger.info(f"포지션 관리자 초기화 완료 - 보유 포지션: {len(self.positions)}개")
        
        except Exception as e:
            self.logger.error(f"포지션 관리자 초기화 오류: {e}")
    
    async def update_position_from_order(self, symbol: str, side: str, quantity: int, 
                                       price: float, order_id: str):
        """주문 체결로부터 포지션 업데이트
        
        Args:
            symbol: 종목코드
            side: 매매구분 (buy/sell)
            quantity: 수량
            price: 체결가격
            order_id: 주문 ID
        """
        try:
            if side.lower() == 'buy':
                await self._handle_buy_order(symbol, quantity, price, order_id)
            elif side.lower() == 'sell':
                await self._handle_sell_order(symbol, quantity, price, order_id)
            
            # 포지션 정보 업데이트
            await self._update_position_data(symbol)
            
            # 데이터베이스 저장
            await self._save_position_to_db(symbol)
        
        except Exception as e:
            self.logger.error(f"주문 기반 포지션 업데이트 오류: {e}")
    
    async def _handle_buy_order(self, symbol: str, quantity: int, price: float, order_id: str):
        """매수 주문 처리
        
        Args:
            symbol: 종목코드
            quantity: 수량
            price: 체결가격
            order_id: 주문 ID
        """
        try:
            if symbol in self.positions:
                # 기존 포지션에 추가 매수
                position = self.positions[symbol]
                
                # 평균 단가 계산
                total_cost = (position.quantity * position.avg_price) + (quantity * price)
                total_quantity = position.quantity + quantity
                new_avg_price = total_cost / total_quantity
                
                # 포지션 업데이트
                position.quantity = total_quantity
                position.avg_price = new_avg_price
                position.total_cost = total_cost
                position.last_updated = datetime.now()
                
                self.logger.info(f"포지션 추가 매수: {symbol} {quantity}주 @ {price} (평균단가: {new_avg_price:.0f})")
            else:
                # 새 포지션 생성
                position = Position(
                    symbol=symbol,
                    quantity=quantity,
                    avg_price=price,
                    current_price=price,
                    market_value=quantity * price,
                    unrealized_pnl=0.0,
                    unrealized_pnl_rate=0.0,
                    realized_pnl=0.0,
                    total_cost=quantity * price,
                    status=PositionStatus.OPEN,
                    entry_time=datetime.now(),
                    last_updated=datetime.now()
                )
                
                # 자동 손절/익절 설정
                if self.auto_stop_loss:
                    position.stop_loss = price * (1 - self.default_stop_loss_rate)
                    position.take_profit = price * (1 + self.default_take_profit_rate)
                
                self.positions[symbol] = position
                
                self.logger.info(f"새 포지션 생성: {symbol} {quantity}주 @ {price}")
        
        except Exception as e:
            self.logger.error(f"매수 주문 처리 오류: {e}")
    
    async def _handle_sell_order(self, symbol: str, quantity: int, price: float, order_id: str):
        """매도 주문 처리
        
        Args:
            symbol: 종목코드
            quantity: 수량
            price: 체결가격
            order_id: 주문 ID
        """
        try:
            if symbol not in self.positions:
                self.logger.warning(f"매도할 포지션이 없음: {symbol}")
                return
            
            position = self.positions[symbol]
            
            if quantity > position.quantity:
                self.logger.warning(f"매도 수량이 보유 수량 초과: {symbol} (매도: {quantity}, 보유: {position.quantity})")
                quantity = position.quantity
            
            # 실현 손익 계산
            realized_pnl = (price - position.avg_price) * quantity
            
            # 포지션 업데이트
            position.quantity -= quantity
            position.realized_pnl += realized_pnl
            position.last_updated = datetime.now()
            
            # 전체 청산 여부 확인
            if position.quantity == 0:
                position.status = PositionStatus.CLOSED
                
                # 성과 추적
                if realized_pnl > 0:
                    self.win_count += 1
                else:
                    self.loss_count += 1
                
                # 보유 기간 계산
                position.holding_period = datetime.now() - position.entry_time
                
                # 닫힌 포지션으로 이동
                self.closed_positions.append(position)
                del self.positions[symbol]
                
                self.logger.info(f"포지션 전체 청산: {symbol} 실현손익: {realized_pnl:,.0f}원")
            else:
                position.status = PositionStatus.PARTIAL
                self.logger.info(f"포지션 부분 청산: {symbol} {quantity}주 @ {price} 실현손익: {realized_pnl:,.0f}원")
            
            # 총 실현 손익 업데이트
            self.total_realized_pnl += realized_pnl
            self.daily_pnl += realized_pnl
        
        except Exception as e:
            self.logger.error(f"매도 주문 처리 오류: {e}")
    
    async def _update_position_data(self, symbol: str):
        """포지션 데이터 업데이트
        
        Args:
            symbol: 종목코드
        """
        try:
            if symbol not in self.positions:
                return
            
            position = self.positions[symbol]
            
            # 현재가 조회
            market_data = await self.db.get_latest_market_data(symbol)
            if not market_data:
                return
            
            current_price = market_data.get('price', position.current_price)
            
            # 포지션 정보 업데이트
            position.current_price = current_price
            position.market_value = position.quantity * current_price
            position.unrealized_pnl = (current_price - position.avg_price) * position.quantity
            position.unrealized_pnl_rate = (current_price - position.avg_price) / position.avg_price * 100
            position.last_updated = datetime.now()
            
            # 최대 수익/손실 추적
            if position.unrealized_pnl > position.max_profit:
                position.max_profit = position.unrealized_pnl
            
            if position.unrealized_pnl < position.max_loss:
                position.max_loss = position.unrealized_pnl
        
        except Exception as e:
            self.logger.error(f"포지션 데이터 업데이트 오류: {e}")
    
    async def update_all_positions(self):
        """모든 포지션 데이터 업데이트"""
        try:
            if not self.positions:
                return
            
            # 병렬로 업데이트
            update_tasks = [
                self._update_position_data(symbol)
                for symbol in self.positions.keys()
            ]
            
            await asyncio.gather(*update_tasks, return_exceptions=True)
            
            # 총 미실현 손익 계산
            self.total_unrealized_pnl = sum(
                position.unrealized_pnl for position in self.positions.values()
            )
        
        except Exception as e:
            self.logger.error(f"전체 포지션 업데이트 오류: {e}")
    
    async def start_monitoring(self):
        """포지션 모니터링 시작"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("포지션 모니터링 시작")
    
    async def stop_monitoring(self):
        """포지션 모니터링 중지"""
        self.is_monitoring = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("포지션 모니터링 중지")
    
    async def _monitoring_loop(self):
        """포지션 모니터링 루프"""
        while self.is_monitoring:
            try:
                # 모든 포지션 업데이트
                await self.update_all_positions()
                
                # 손절/익절 조건 확인
                await self._check_exit_conditions()
                
                # 포지션 데이터 저장
                await self._save_all_positions()
                
                # 대기
                await asyncio.sleep(self.update_interval)
            
            except Exception as e:
                self.logger.error(f"포지션 모니터링 오류: {e}")
                await asyncio.sleep(30)
    
    async def _check_exit_conditions(self):
        """손절/익절 조건 확인"""
        try:
            for symbol, position in self.positions.items():
                # 손절 조건 확인
                if position.stop_loss and position.current_price <= position.stop_loss:
                    await self._trigger_stop_loss(symbol, position)
                
                # 익절 조건 확인
                if position.take_profit and position.current_price >= position.take_profit:
                    await self._trigger_take_profit(symbol, position)
                
                # 트레일링 스톱 업데이트
                await self._update_trailing_stop(symbol, position)
        
        except Exception as e:
            self.logger.error(f"청산 조건 확인 오류: {e}")
    
    async def _trigger_stop_loss(self, symbol: str, position: Position):
        """손절 실행
        
        Args:
            symbol: 종목코드
            position: 포지션
        """
        try:
            self.logger.warning(f"손절 조건 달성: {symbol} 현재가: {position.current_price}, 손절가: {position.stop_loss}")
            
            # 시장가 매도 주문 생성 (OrderManager를 통해)
            # 여기서는 로그만 기록
            await self.db.insert_trading_log({
                'symbol': symbol,
                'action': 'stop_loss_triggered',
                'price': position.current_price,
                'quantity': position.quantity,
                'pnl': position.unrealized_pnl,
                'timestamp': datetime.now()
            })
        
        except Exception as e:
            self.logger.error(f"손절 실행 오류: {e}")
    
    async def _trigger_take_profit(self, symbol: str, position: Position):
        """익절 실행
        
        Args:
            symbol: 종목코드
            position: 포지션
        """
        try:
            self.logger.info(f"익절 조건 달성: {symbol} 현재가: {position.current_price}, 익절가: {position.take_profit}")
            
            # 지정가 매도 주문 생성 (OrderManager를 통해)
            # 여기서는 로그만 기록
            await self.db.insert_trading_log({
                'symbol': symbol,
                'action': 'take_profit_triggered',
                'price': position.current_price,
                'quantity': position.quantity,
                'pnl': position.unrealized_pnl,
                'timestamp': datetime.now()
            })
        
        except Exception as e:
            self.logger.error(f"익절 실행 오류: {e}")
    
    async def _update_trailing_stop(self, symbol: str, position: Position):
        """트레일링 스톱 업데이트
        
        Args:
            symbol: 종목코드
            position: 포지션
        """
        try:
            if not position.stop_loss:
                return
            
            # 수익이 나고 있을 때만 트레일링 스톱 적용
            if position.unrealized_pnl > 0:
                # 현재가의 2% 아래로 손절가 조정
                new_stop_loss = position.current_price * (1 - self.default_stop_loss_rate)
                
                # 손절가가 상승하는 경우만 업데이트
                if new_stop_loss > position.stop_loss:
                    old_stop_loss = position.stop_loss
                    position.stop_loss = new_stop_loss
                    
                    self.logger.info(f"트레일링 스톱 업데이트: {symbol} {old_stop_loss:.0f} -> {new_stop_loss:.0f}")
        
        except Exception as e:
            self.logger.error(f"트레일링 스톱 업데이트 오류: {e}")
    
    async def _restore_positions(self):
        """기존 포지션 복구"""
        try:
            # KIS API로 실제 계좌 잔고 조회
            balance_result = await self.kis_api.get_balance()
            
            if balance_result is None:
                self.logger.error("계좌 잔고 조회 실패")
                return
            
            positions_data = balance_result.get('positions', [])
            
            for pos_data in positions_data:
                symbol = pos_data.get('symbol', '')
                quantity = int(pos_data.get('quantity', 0))
                
                if quantity <= 0:
                    continue
                
                avg_price = float(pos_data.get('avg_price', 0))
                current_price = float(pos_data.get('current_price', 0))
                
                if avg_price <= 0 or current_price <= 0:
                    continue
                
                # 포지션 생성
                position = Position(
                    symbol=symbol,
                    quantity=quantity,
                    avg_price=avg_price,
                    current_price=current_price,
                    market_value=quantity * current_price,
                    unrealized_pnl=(current_price - avg_price) * quantity,
                    unrealized_pnl_rate=(current_price - avg_price) / avg_price * 100,
                    realized_pnl=0.0,
                    total_cost=quantity * avg_price,
                    status=PositionStatus.OPEN,
                    entry_time=datetime.now(),  # 실제로는 DB에서 조회해야 함
                    last_updated=datetime.now()
                )
                
                # 자동 손절/익절 설정
                if self.auto_stop_loss:
                    position.stop_loss = avg_price * (1 - self.default_stop_loss_rate)
                    position.take_profit = avg_price * (1 + self.default_take_profit_rate)
                
                self.positions[symbol] = position
                
                self.logger.info(f"포지션 복구: {symbol} {quantity}주 @ {avg_price:.0f}")
            
            # 총 미실현 손익 계산
            self.total_unrealized_pnl = sum(
                position.unrealized_pnl for position in self.positions.values()
            )
        
        except Exception as e:
            self.logger.error(f"포지션 복구 오류: {e}")
    
    async def _save_position_to_db(self, symbol: str):
        """포지션을 데이터베이스에 저장
        
        Args:
            symbol: 종목코드
        """
        try:
            if symbol not in self.positions:
                return
            
            position = self.positions[symbol]
            
            await self.db.insert_position({
                'symbol': symbol,
                'quantity': position.quantity,
                'avg_price': position.avg_price,
                'current_price': position.current_price,
                'market_value': position.market_value,
                'unrealized_pnl': position.unrealized_pnl,
                'unrealized_pnl_rate': position.unrealized_pnl_rate,
                'realized_pnl': position.realized_pnl,
                'total_cost': position.total_cost,
                'status': position.status.value,
                'entry_time': position.entry_time,
                'last_updated': position.last_updated,
                'stop_loss': position.stop_loss,
                'take_profit': position.take_profit,
                'max_profit': position.max_profit,
                'max_loss': position.max_loss
            })
        
        except Exception as e:
            self.logger.error(f"포지션 DB 저장 오류: {e}")
    
    async def _save_all_positions(self):
        """모든 포지션 데이터베이스 저장"""
        try:
            save_tasks = [
                self._save_position_to_db(symbol)
                for symbol in self.positions.keys()
            ]
            
            await asyncio.gather(*save_tasks, return_exceptions=True)
        
        except Exception as e:
            self.logger.error(f"전체 포지션 저장 오류: {e}")
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """특정 종목의 포지션 조회
        
        Args:
            symbol: 종목코드
        
        Returns:
            포지션 정보
        """
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, Position]:
        """모든 포지션 조회
        
        Returns:
            전체 포지션 딕셔너리
        """
        return self.positions.copy()
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """포트폴리오 요약 정보
        
        Returns:
            포트폴리오 요약
        """
        try:
            total_market_value = sum(pos.market_value for pos in self.positions.values())
            total_cost = sum(pos.total_cost for pos in self.positions.values())
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            
            # 수익률 계산
            total_return_rate = (total_unrealized_pnl / total_cost * 100) if total_cost > 0 else 0
            
            # 승률 계산
            total_trades = self.win_count + self.loss_count
            win_rate = (self.win_count / total_trades * 100) if total_trades > 0 else 0
            
            # 최대 수익/손실 포지션
            max_profit_position = None
            max_loss_position = None
            
            if self.positions:
                max_profit_position = max(self.positions.values(), key=lambda p: p.unrealized_pnl_rate)
                max_loss_position = min(self.positions.values(), key=lambda p: p.unrealized_pnl_rate)
            
            return {
                'total_positions': len(self.positions),
                'total_market_value': total_market_value,
                'total_cost': total_cost,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_realized_pnl': self.total_realized_pnl,
                'total_return_rate': total_return_rate,
                'daily_pnl': self.daily_pnl,
                'win_count': self.win_count,
                'loss_count': self.loss_count,
                'win_rate': win_rate,
                'max_profit_symbol': max_profit_position.symbol if max_profit_position else None,
                'max_profit_rate': max_profit_position.unrealized_pnl_rate if max_profit_position else 0,
                'max_loss_symbol': max_loss_position.symbol if max_loss_position else None,
                'max_loss_rate': max_loss_position.unrealized_pnl_rate if max_loss_position else 0
            }
        
        except Exception as e:
            self.logger.error(f"포트폴리오 요약 생성 오류: {e}")
            return {}
    
    def get_position_by_pnl(self, ascending: bool = False) -> List[Position]:
        """손익률 순으로 포지션 정렬
        
        Args:
            ascending: 오름차순 여부
        
        Returns:
            정렬된 포지션 리스트
        """
        try:
            return sorted(
                self.positions.values(),
                key=lambda p: p.unrealized_pnl_rate,
                reverse=not ascending
            )
        except Exception as e:
            self.logger.error(f"포지션 정렬 오류: {e}")
            return []
    
    def get_risk_positions(self, loss_threshold: float = -5.0) -> List[Position]:
        """리스크 포지션 조회 (손실률 기준)
        
        Args:
            loss_threshold: 손실률 임계값 (%)
        
        Returns:
            리스크 포지션 리스트
        """
        try:
            return [
                position for position in self.positions.values()
                if position.unrealized_pnl_rate < loss_threshold
            ]
        except Exception as e:
            self.logger.error(f"리스크 포지션 조회 오류: {e}")
            return []
    
    async def close_position(self, symbol: str, quantity: Optional[int] = None) -> bool:
        """포지션 청산
        
        Args:
            symbol: 종목코드
            quantity: 청산 수량 (None이면 전량)
        
        Returns:
            청산 성공 여부
        """
        try:
            if symbol not in self.positions:
                self.logger.warning(f"청산할 포지션이 없음: {symbol}")
                return False
            
            position = self.positions[symbol]
            close_quantity = quantity or position.quantity
            
            if close_quantity > position.quantity:
                close_quantity = position.quantity
            
            # 실제 매도 주문은 OrderManager를 통해 처리
            # 여기서는 로그만 기록
            self.logger.info(f"포지션 청산 요청: {symbol} {close_quantity}주")
            
            return True
        
        except Exception as e:
            self.logger.error(f"포지션 청산 오류: {e}")
            return False
    
    async def close_all_positions(self) -> int:
        """모든 포지션 청산
        
        Returns:
            청산 요청한 포지션 수
        """
        try:
            closed_count = 0
            
            for symbol in list(self.positions.keys()):
                if await self.close_position(symbol):
                    closed_count += 1
            
            self.logger.info(f"전체 포지션 청산 요청: {closed_count}개")
            return closed_count
        
        except Exception as e:
            self.logger.error(f"전체 포지션 청산 오류: {e}")
            return 0
    
    def reset_daily_metrics(self):
        """일일 메트릭 초기화"""
        try:
            self.daily_pnl = 0.0
            self.logger.info("일일 메트릭 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"일일 메트릭 초기화 오류: {e}")
    
    async def get_position_analytics(self, symbol: str) -> Dict[str, Any]:
        """포지션 분석 정보
        
        Args:
            symbol: 종목코드
        
        Returns:
            포지션 분석 정보
        """
        try:
            if symbol not in self.positions:
                return {}
            
            position = self.positions[symbol]
            
            # 보유 기간
            holding_period = datetime.now() - position.entry_time
            holding_hours = holding_period.total_seconds() / 3600
            
            # 최대 낙폭 (MDD)
            max_drawdown = position.max_loss / position.total_cost * 100 if position.total_cost > 0 else 0
            
            # 최대 수익률
            max_return = position.max_profit / position.total_cost * 100 if position.total_cost > 0 else 0
            
            return {
                'symbol': symbol,
                'holding_hours': holding_hours,
                'max_drawdown': max_drawdown,
                'max_return': max_return,
                'current_return': position.unrealized_pnl_rate,
                'risk_reward_ratio': abs(max_return / max_drawdown) if max_drawdown != 0 else 0,
                'stop_loss_distance': ((position.current_price - position.stop_loss) / position.current_price * 100) if position.stop_loss else 0,
                'take_profit_distance': ((position.take_profit - position.current_price) / position.current_price * 100) if position.take_profit else 0
            }
        
        except Exception as e:
            self.logger.error(f"포지션 분석 정보 생성 오류: {e}")
            return {}
    
    def add_position_callback(self, callback):
        """포지션 변경 콜백 함수 추가
        
        Args:
            callback: 포지션 변경 시 호출될 콜백 함수
        """
        if callback not in self.position_callbacks:
            self.position_callbacks.append(callback)
            self.logger.info("포지션 콜백 함수 추가됨")
    
    def remove_position_callback(self, callback):
        """포지션 변경 콜백 함수 제거
        
        Args:
            callback: 제거할 콜백 함수
        """
        if callback in self.position_callbacks:
            self.position_callbacks.remove(callback)
            self.logger.info("포지션 콜백 함수 제거됨")
    
    async def _notify_position_callbacks(self, symbol: str, position: Position, event_type: str):
        """포지션 콜백 함수들에게 알림
        
        Args:
            symbol: 종목코드
            position: 포지션 정보
            event_type: 이벤트 타입 (created, updated, closed)
        """
        try:
            for callback in self.position_callbacks:
                await callback(symbol, position, event_type)
        except Exception as e:
            self.logger.error(f"포지션 콜백 알림 오류: {e}")
    
    async def close(self):
        """포지션 관리자 종료"""
        try:
            # 모니터링 중지
            await self.stop_monitoring()
            
            # 최종 포지션 저장
            await self._save_all_positions()
            
            self.logger.info("포지션 관리자 종료 완료")
        
        except Exception as e:
            self.logger.error(f"포지션 관리자 종료 오류: {e}")