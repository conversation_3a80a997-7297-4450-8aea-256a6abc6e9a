# -*- coding: utf-8 -*-
"""
프롬프트 관리자

OpenAI GPT용 초단타 매매 최적화 프롬프트 관리
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager

class PromptManager:
    """
    프롬프트 관리자 클래스
    """
    
    def __init__(self, config_manager: ConfigManager):
        """프롬프트 관리자 초기화
        
        Args:
            config_manager: 설정 관리자
        """
        self.config = config_manager
        self.logger = get_logger()
        
        # 기본 시스템 프롬프트
        self.system_prompt = self._build_system_prompt()
        
        # 분석 유형별 프롬프트
        self.analysis_prompts = {
            'scalping': self._build_scalping_prompt(),
            'day_trading': self._build_day_trading_prompt(),
            'risk_assessment': self._build_risk_assessment_prompt(),
            'exit_strategy': self._build_exit_strategy_prompt()
        }
    
    def _build_system_prompt(self) -> str:
        """시스템 프롬프트 구축
        
        Returns:
            시스템 프롬프트
        """
        return """
당신은 한국 주식시장의 초단타/단타 전문 AI 트레이더입니다.

**핵심 역할:**
- 실시간 시장 데이터 분석을 통한 초고속 매매 의사결정
- 슬리피지 최소화와 수익 극대화에 특화된 전략 수립
- 리스크 관리와 손실 제한을 최우선으로 하는 보수적 접근

**운영 원칙:**
1. **속도 우선**: 모든 판단은 3초 이내 완료
2. **손실 제한**: 단일 거래 손실률 2% 이하 엄수
3. **당일 청산**: 모든 포지션은 당일 청산 원칙
4. **슬리피지 관리**: 호가창 분석을 통한 최적 진입점 선택
5. **감정 배제**: 데이터 기반 객관적 판단만 수행

**분석 요소:**
- 실시간 가격 움직임 및 거래량 패턴
- 호가창 분석 (매수/매도 물량 균형)
- 기술적 지표 (이동평균, RSI, 볼린저밴드)
- 뉴스 및 시장 심리 분석
- 시간대별 변동성 패턴

**응답 형식:**
모든 응답은 JSON 형태로 제공하며, 다음 필드를 포함해야 합니다:
- decision: "buy", "sell", "hold" 중 하나
- confidence: 0.0-1.0 사이의 신뢰도
- target_price: 목표가격 (매수/매도시)
- quantity: 거래수량
- stop_loss: 손절가
- take_profit: 익절가
- reasoning: 판단 근거 (간결하게)
- risk_level: "low", "medium", "high"
- time_horizon: 예상 보유시간 (분 단위)
"""
    
    def _build_scalping_prompt(self) -> str:
        """스캘핑 분석 프롬프트
        
        Returns:
            스캘핑 프롬프트
        """
        return """
**초단타 스캘핑 분석 요청**

다음 데이터를 바탕으로 초단타 스캘핑 매매 결정을 내려주세요:

**분석 기준:**
- 목표 수익률: 0.3-1.0%
- 최대 보유시간: 1-5분
- 손절 기준: -0.5% 이하
- 진입 조건: 명확한 모멘텀 확인시에만

**중점 분석 사항:**
1. 즉시 실행 가능한 호가창 상황
2. 1분/5분 차트의 단기 모멘텀
3. 거래량 급증 여부
4. 지지/저항선 근접도
5. 시장 전체 분위기

현재 시장 상황에서 즉시 실행 가능한 스캘핑 전략을 제시해주세요.
"""
    
    def _build_day_trading_prompt(self) -> str:
        """데이트레이딩 분석 프롬프트
        
        Returns:
            데이트레이딩 프롬프트
        """
        return """
**단타 데이트레이딩 분석 요청**

다음 데이터를 바탕으로 단타 데이트레이딩 매매 결정을 내려주세요:

**분석 기준:**
- 목표 수익률: 1.0-3.0%
- 최대 보유시간: 30분-2시간
- 손절 기준: -1.5% 이하
- 진입 조건: 기술적 패턴 확인 후

**중점 분석 사항:**
1. 15분/30분 차트의 추세 분석
2. 주요 기술적 지표 신호
3. 뉴스 및 재료 영향도
4. 섹터 전체 흐름
5. 일중 변동성 패턴

현재 시장 상황에서 최적의 단타 전략을 제시해주세요.
"""
    
    def _build_risk_assessment_prompt(self) -> str:
        """리스크 평가 프롬프트
        
        Returns:
            리스크 평가 프롬프트
        """
        return """
**리스크 평가 분석 요청**

현재 포지션 및 시장 상황에 대한 리스크 평가를 수행해주세요:

**평가 항목:**
1. **시장 리스크**: 전체 시장 변동성 및 불안 요소
2. **종목 리스크**: 개별 종목의 특수 위험 요소
3. **유동성 리스크**: 거래량 및 호가 스프레드 상황
4. **시간 리스크**: 장 마감 시간 고려
5. **포트폴리오 리스크**: 전체 포지션 집중도

**리스크 등급:**
- LOW: 안전한 거래 환경
- MEDIUM: 주의 깊은 모니터링 필요
- HIGH: 즉시 포지션 축소 권장
- CRITICAL: 긴급 청산 필요

각 리스크 요소별 점수와 종합 리스크 등급을 제시해주세요.
"""
    
    def _build_exit_strategy_prompt(self) -> str:
        """청산 전략 프롬프트
        
        Returns:
            청산 전략 프롬프트
        """
        return """
**청산 전략 분석 요청**

현재 보유 포지션에 대한 최적 청산 전략을 수립해주세요:

**청산 고려사항:**
1. **수익 실현**: 목표 수익률 달성 여부
2. **손실 제한**: 손절선 근접 여부
3. **시간 제약**: 장 마감 시간 고려
4. **시장 상황**: 전체적인 시장 흐름 변화
5. **기술적 신호**: 반전 신호 출현 여부

**청산 방식:**
- IMMEDIATE: 즉시 전량 청산
- GRADUAL: 분할 청산 (3-5회)
- PARTIAL: 일부만 청산 후 관망
- HOLD: 현재 포지션 유지
- SCALE_OUT: 수익 구간별 단계적 청산

최적의 청산 타이밍과 방식을 제시해주세요.
"""
    
    def build_analysis_prompt(self, analysis_type: str, market_data: Dict[str, Any], 
                            news_data: List[Dict[str, Any]], 
                            position_data: Optional[Dict[str, Any]] = None) -> str:
        """분석 프롬프트 구축
        
        Args:
            analysis_type: 분석 유형
            market_data: 시장 데이터
            news_data: 뉴스 데이터
            position_data: 포지션 데이터
        
        Returns:
            완성된 프롬프트
        """
        try:
            # 기본 프롬프트 선택
            base_prompt = self.analysis_prompts.get(analysis_type, self.analysis_prompts['scalping'])
            
            # 데이터 섹션 구축
            data_section = self._build_data_section(market_data, news_data, position_data)
            
            # 시장 상황 요약
            market_summary = self._build_market_summary(market_data)
            
            # 최종 프롬프트 조합
            full_prompt = f"""{base_prompt}

**현재 시장 데이터:**
{data_section}

**시장 상황 요약:**
{market_summary}

**분석 요청:**
위 데이터를 종합하여 {analysis_type} 관점에서 최적의 매매 결정을 JSON 형태로 제시해주세요.
현재 시각: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            return full_prompt
        
        except Exception as e:
            self.logger.error(f"프롬프트 구축 오류: {e}")
            return self.analysis_prompts['scalping']
    
    def _build_data_section(self, market_data: Dict[str, Any], 
                          news_data: List[Dict[str, Any]], 
                          position_data: Optional[Dict[str, Any]]) -> str:
        """데이터 섹션 구축
        
        Args:
            market_data: 시장 데이터
            news_data: 뉴스 데이터
            position_data: 포지션 데이터
        
        Returns:
            데이터 섹션 문자열
        """
        sections = []
        
        # 시장 데이터 섹션
        if market_data:
            market_section = f"""
**시장 데이터:**
- 종목: {market_data.get('symbol', 'N/A')}
- 현재가: {market_data.get('price', 0):,}원
- 변동률: {market_data.get('change_rate', 0):.2f}%
- 거래량: {market_data.get('volume', 0):,}주
- RSI: {market_data.get('rsi', 0):.1f}
- 이동평균(5): {market_data.get('ma_5', 0):,.0f}원
- 이동평균(20): {market_data.get('ma_20', 0):,.0f}원
- 볼린저밴드 상단: {market_data.get('bb_upper', 0):,.0f}원
- 볼린저밴드 하단: {market_data.get('bb_lower', 0):,.0f}원
"""
            sections.append(market_section)
        
        # 호가 데이터 섹션
        if market_data and 'orderbook' in market_data:
            orderbook = market_data['orderbook']
            orderbook_section = f"""
**호가 정보:**
매도호가: {', '.join([f"{ask['price']:,}({ask['volume']:,})" for ask in orderbook.get('asks', [])[:5]])}
매수호가: {', '.join([f"{bid['price']:,}({bid['volume']:,})" for bid in orderbook.get('bids', [])[:5]])}
"""
            sections.append(orderbook_section)
        
        # 뉴스 데이터 섹션
        if news_data:
            news_section = "**관련 뉴스:**\n"
            for i, news in enumerate(news_data[:3]):
                sentiment_text = "긍정" if news.get('sentiment', 0) > 0 else "부정" if news.get('sentiment', 0) < 0 else "중립"
                news_section += f"- [{news.get('source', 'Unknown')}] {news.get('title', '')[:100]}... (감정: {sentiment_text})\n"
            sections.append(news_section)
        
        # 포지션 데이터 섹션
        if position_data:
            position_section = f"""
**현재 포지션:**
- 보유수량: {position_data.get('quantity', 0):,}주
- 평균단가: {position_data.get('avg_price', 0):,}원
- 현재손익: {position_data.get('unrealized_pnl', 0):,}원 ({position_data.get('unrealized_pnl_rate', 0):.2f}%)
- 보유시간: {position_data.get('holding_time', 0)}분
"""
            sections.append(position_section)
        
        return "\n".join(sections)
    
    def _build_market_summary(self, market_data: Dict[str, Any]) -> str:
        """시장 상황 요약 구축
        
        Args:
            market_data: 시장 데이터
        
        Returns:
            시장 상황 요약
        """
        try:
            price = market_data.get('price', 0)
            change_rate = market_data.get('change_rate', 0)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            rsi = market_data.get('rsi', 50)
            
            # 추세 분석
            if change_rate > 2:
                trend = "강한 상승세"
            elif change_rate > 0.5:
                trend = "상승세"
            elif change_rate > -0.5:
                trend = "횡보"
            elif change_rate > -2:
                trend = "하락세"
            else:
                trend = "강한 하락세"
            
            # 거래량 분석
            if volume_ratio > 2:
                volume_status = "거래량 급증"
            elif volume_ratio > 1.5:
                volume_status = "거래량 증가"
            elif volume_ratio < 0.5:
                volume_status = "거래량 감소"
            else:
                volume_status = "평균 거래량"
            
            # RSI 분석
            if rsi > 70:
                rsi_status = "과매수 구간"
            elif rsi > 50:
                rsi_status = "매수 우세"
            elif rsi > 30:
                rsi_status = "매도 우세"
            else:
                rsi_status = "과매도 구간"
            
            # 시간대 분석
            current_hour = datetime.now().hour
            if 9 <= current_hour < 10:
                time_status = "장 초반 (변동성 높음)"
            elif 10 <= current_hour < 14:
                time_status = "장 중반 (안정적 거래)"
            elif 14 <= current_hour < 15:
                time_status = "장 후반 (마감 준비)"
            else:
                time_status = "장외 시간"
            
            return f"""
- 가격 추세: {trend} ({change_rate:+.2f}%)
- 거래량 상태: {volume_status} (평균 대비 {volume_ratio:.1f}배)
- 기술적 상태: {rsi_status} (RSI {rsi:.1f})
- 시간대 특성: {time_status}
- 변동성: {'높음' if abs(change_rate) > 1 else '보통' if abs(change_rate) > 0.3 else '낮음'}
"""
        
        except Exception as e:
            self.logger.error(f"시장 요약 구축 오류: {e}")
            return "시장 상황 분석 중 오류 발생"
    
    def build_risk_prompt(self, portfolio_data: Dict[str, Any], 
                         market_conditions: Dict[str, Any]) -> str:
        """리스크 분석 프롬프트 구축
        
        Args:
            portfolio_data: 포트폴리오 데이터
            market_conditions: 시장 상황 데이터
        
        Returns:
            리스크 분석 프롬프트
        """
        base_prompt = self.analysis_prompts['risk_assessment']
        
        risk_data = f"""
**포트폴리오 현황:**
- 총 자산: {portfolio_data.get('total_balance', 0):,}원
- 현금 비중: {portfolio_data.get('cash_ratio', 0):.1f}%
- 포지션 수: {portfolio_data.get('position_count', 0)}개
- 일일 손익: {portfolio_data.get('daily_pnl', 0):,}원
- 최대 손실: {portfolio_data.get('max_loss', 0):,}원

**시장 상황:**
- 코스피 변동률: {market_conditions.get('kospi_change', 0):.2f}%
- 변동성 지수: {market_conditions.get('volatility', 0):.1f}
- 거래대금: {market_conditions.get('trading_value', 0):,}억원
- 외국인 순매수: {market_conditions.get('foreign_net', 0):,}억원
"""
        
        return f"{base_prompt}\n\n{risk_data}\n\n위 데이터를 바탕으로 종합적인 리스크 평가를 수행해주세요."
    
    def build_exit_prompt(self, position: Dict[str, Any], 
                         market_data: Dict[str, Any]) -> str:
        """청산 전략 프롬프트 구축
        
        Args:
            position: 포지션 정보
            market_data: 시장 데이터
        
        Returns:
            청산 전략 프롬프트
        """
        base_prompt = self.analysis_prompts['exit_strategy']
        
        position_info = f"""
**포지션 정보:**
- 종목: {position.get('symbol', 'N/A')}
- 보유수량: {position.get('quantity', 0):,}주
- 평균단가: {position.get('avg_price', 0):,}원
- 현재가: {market_data.get('price', 0):,}원
- 수익률: {position.get('return_rate', 0):.2f}%
- 보유시간: {position.get('holding_minutes', 0)}분
- 목표수익률: {position.get('target_return', 0):.2f}%
- 손절선: {position.get('stop_loss', 0):,}원

**시장 상황:**
- 현재 추세: {market_data.get('trend', 'Unknown')}
- 거래량: {market_data.get('volume_status', 'Normal')}
- 장 마감까지: {self._get_time_to_close()}분
"""
        
        return f"{base_prompt}\n\n{position_info}\n\n최적의 청산 전략을 제시해주세요."
    
    def _get_time_to_close(self) -> int:
        """장 마감까지 남은 시간 계산
        
        Returns:
            남은 시간 (분)
        """
        now = datetime.now()
        
        # 평일 15:30 장 마감
        if now.weekday() < 5:  # 월-금
            close_time = now.replace(hour=15, minute=30, second=0).replace(microsecond=0)
            if now < close_time:
                return int((close_time - now).total_seconds() / 60)
        
        return 0
    
    def get_system_prompt(self) -> str:
        """시스템 프롬프트 반환
        
        Returns:
            시스템 프롬프트
        """
        return self.system_prompt
    
    def build_news_analysis_prompt(self, news_data: Dict[str, Any]) -> str:
        """뉴스 분석용 프롬프트 생성
        
        Args:
            news_data: 뉴스 데이터 (title, content, url, source, importance 포함)
        
        Returns:
            뉴스 분석용 프롬프트
        """
        title = news_data.get('title', '')
        content = news_data.get('content', '')
        source = news_data.get('source', '')
        
        # 뉴스 텍스트 준비
        news_text = f"제목: {title}\n내용: {content}"
        
        prompt = f"""
당신은 한국 주식 시장 전문 분석가입니다. 다음 뉴스를 분석하여 시장에 미칠 영향을 평가해주세요.

뉴스 정보:
- 출처: {source}
- 뉴스 내용:
{news_text}

다음 형식으로 JSON 응답해주세요:
{{
    "sentiment_score": 감정점수(-1.0~1.0, -1.0=매우부정적, 0=중립, 1.0=매우긍정적),
    "market_impact": "시장영향도(낮음/보통/높음)",
    "confidence": 분석신뢰도(0.0~1.0),
    "decision": "투자판단(상승/하락/중립)",
    "reasoning": "분석근거 설명",
    "affected_sector": "뉴스로 인해 가장 높은 영향을 받는 업종 하나",
    "affected_stock": "뉴스가 특정 종목에 대해 구체적으로 언급하는 경우에만 해당 종목명, 그렇지 않으면 관련된 가장 강한 주도주 하나 (예: 삼성전자, SK하이닉스, NAVER 등)",
    "time_horizon": "영향지속시간(짧음/보통/장기)"
}}}}

분석 시 고려사항:
- 한국 주식 시장의 특성
- 뉴스의 신뢰성과 출처
- 시장 참여자들의 반응 예상
- 단기/중기 영향 구분
- 섹터별 차별적 영향도

종목 선택 기준:
- affected_stock은 뉴스에서 회사명이나 종목명을 직접 언급한 경우에만 해당 종목 반환
- 단순히 업종이나 테마만 언급된 경우에는 해당 업종의 대표 주도주 하나만 반환
- 여러 종목이 언급되어도 가장 핵심적인 종목 하나만 선택
"""
        return prompt
    
    def get_news_analysis_system_prompt(self) -> str:
        """뉴스 분석용 시스템 프롬프트 반환
        
        Returns:
            뉴스 분석용 시스템 프롬프트
        """
        return "당신은 한국 주식 시장 전문 분석가입니다. 정확하고 객관적인 분석을 제공합니다."
    
    def update_prompt_template(self, analysis_type: str, template: str):
        """프롬프트 템플릿 업데이트
        
        Args:
            analysis_type: 분석 유형
            template: 새로운 템플릿
        """
        if analysis_type in self.analysis_prompts:
            self.analysis_prompts[analysis_type] = template
            self.logger.info(f"프롬프트 템플릿 업데이트: {analysis_type}")
        else:
            self.logger.warning(f"알 수 없는 분석 유형: {analysis_type}")