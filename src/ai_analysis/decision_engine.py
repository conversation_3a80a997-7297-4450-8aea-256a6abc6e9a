# -*- coding: utf-8 -*-
"""
AI 의사결정 엔진

OpenAI GPT를 활용한 초단타/단타 매매 의사결정
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import openai
from openai import AsyncOpenAI

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager
from .prompt_manager import PromptManager
from .risk_analyzer import RiskAnalyzer, RiskLevel

class DecisionType(Enum):
    """의사결정 타입"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE = "close"
    EMERGENCY_EXIT = "emergency_exit"

class ConfidenceLevel(Enum):
    """신뢰도 레벨"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"

@dataclass
class TradingDecision:
    """매매 의사결정 데이터 클래스"""
    symbol: str
    decision: DecisionType
    confidence: ConfidenceLevel
    confidence_score: float  # 0-100
    target_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    quantity: int
    reasoning: str
    risk_level: RiskLevel
    urgency: int  # 1-10 (10이 가장 긴급)
    expected_return: float
    max_loss: float
    holding_period: str  # "scalping", "day_trading", "swing"
    technical_signals: List[str]
    fundamental_factors: List[str]
    market_conditions: Dict[str, Any]
    timestamp: datetime

class DecisionEngine:
    """
    AI 의사결정 엔진 클래스
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, 
                 risk_analyzer: RiskAnalyzer):
        """의사결정 엔진 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
            risk_analyzer: 리스크 분석기
        """
        self.config = config_manager
        self.db = db_manager
        self.risk_analyzer = risk_analyzer
        self.logger = get_logger()
        
        # OpenAI 클라이언트 초기화
        self.openai_client = AsyncOpenAI(
            api_key=self.config.get_openai_api_key()
        )
        
        # 프롬프트 관리자
        self.prompt_manager = PromptManager(self.config)
        
        # AI 설정
        self.model = self.config.get('ai.model', 'gpt-4o-mini')
        self.temperature = self.config.get('ai.temperature', 0.2)
        self.max_tokens = self.config.get('ai.max_tokens', 2000)
        
        # 의사결정 설정
        self.min_confidence_threshold = self.config.get('ai.confidence_threshold', 0.7) * 100
        self.scalping_mode = self.config.get('trading.scalping_mode', True)
        self.day_trading_mode = self.config.get('trading.day_trading_mode', True)
        
        # 분석 가중치 설정
        weights = self.config.get('ai.weights', {})
        self.analysis_weights = {
            'technical_analysis': weights.get('technical_analysis', 0.4),
            'news_sentiment': weights.get('news_sentiment', 0.3),
            'market_condition': weights.get('market_condition', 0.2),
            'risk_assessment': weights.get('risk_assessment', 0.1)
        }
        
        # 캐시 설정
        self.decision_cache = {}
        self.cache_ttl = self.config.get('ai.cache_ttl', 300)  # 5분
        self.max_cache_size = self.config.get('ai.max_cache_size', 1000)
        
        # 성능 추적
        self.decision_history = []
        self.performance_metrics = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'failed_decisions': 0,
            'avg_confidence': 0.0,
            'avg_return': 0.0
        }
    
    async def initialize(self):
        """의사결정 엔진 초기화"""
        try:
            self.logger.info("의사결정 엔진 초기화 시작")
            
            # OpenAI 클라이언트 초기화
            api_key = self.config.get_openai_api_key()
            if api_key and api_key != 'YOUR_OPENAI_API_KEY':
                self.openai_client = AsyncOpenAI(api_key=api_key)
                self.logger.info("OpenAI 클라이언트 초기화 완료")
            else:
                self.logger.warning("OpenAI API 키가 설정되지 않았습니다. AI 분석이 제한됩니다.")
            
            self.logger.info("의사결정 엔진 초기화 완료")
            
        except Exception as e:
            self.logger.error(f"의사결정 엔진 초기화 오류: {e}")
            raise
    
    async def close(self):
        """의사결정 엔진 종료"""
        try:
            self.logger.info("의사결정 엔진 종료")
            # 필요한 정리 작업이 있다면 여기에 추가
        except Exception as e:
            self.logger.error(f"의사결정 엔진 종료 오류: {e}")
    
    async def analyze_and_decide(self, symbol: str, analysis_type: str = "scalping") -> TradingDecision:
        """종목 분석 및 매매 의사결정
        
        Args:
            symbol: 종목코드
            analysis_type: 분석 타입 (scalping, day_trading, swing)
        
        Returns:
            매매 의사결정
        """
        try:
            # 캐시 확인
            cache_key = f"{symbol}_{analysis_type}"
            if self._is_cache_valid(cache_key):
                self.logger.debug(f"캐시된 의사결정 반환: {symbol}")
                return self.decision_cache[cache_key]['decision']
            
            # 1. 데이터 수집
            market_data = await self._collect_market_data(symbol)
            news_data = await self._collect_news_data(symbol)
            portfolio_data = await self._collect_portfolio_data()
            
            # 2. 리스크 분석
            risk_metrics = await self.risk_analyzer.analyze_portfolio_risk(portfolio_data)
            
            # 3. AI 분석 프롬프트 생성
            prompt = self.prompt_manager.build_analysis_prompt(
                analysis_type=analysis_type,
                symbol=symbol,
                market_data=market_data,
                news_data=news_data,
                portfolio_data=portfolio_data,
                risk_metrics=risk_metrics.__dict__
            )
            
            # 4. OpenAI API 호출
            ai_response = await self._call_openai_api(prompt)
            
            # 5. AI 응답 파싱
            decision = self._parse_ai_response(ai_response, symbol, risk_metrics.overall_risk)
            
            # 6. 의사결정 검증 및 조정
            validated_decision = await self._validate_and_adjust_decision(decision, portfolio_data)
            
            # 7. 캐시 저장 (크기 제한 적용)
            self._manage_cache_size()
            self.decision_cache[cache_key] = {
                'decision': validated_decision,
                'timestamp': datetime.now()
            }
            
            # 8. 의사결정 기록
            await self._record_decision(validated_decision)
            
            # 9. 성능 메트릭 업데이트
            self._update_performance_metrics(validated_decision)
            
            self.logger.info(f"의사결정 완료: {symbol} - {validated_decision.decision.value} "
                           f"(신뢰도: {validated_decision.confidence_score:.1f}%)")
            
            return validated_decision
        
        except Exception as e:
            self.logger.error(f"의사결정 분석 오류: {e}")
            # 안전한 기본 의사결정 반환
            return self._get_safe_default_decision(symbol)
    
    async def _collect_market_data(self, symbol: str) -> Dict[str, Any]:
        """시장 데이터 수집
        
        Args:
            symbol: 종목코드
        
        Returns:
            시장 데이터
        """
        try:
            # 현재가 정보
            current_data = await self.db.get_latest_market_data(symbol)
            
            # 가격 히스토리 (1시간)
            price_history = await self.db.get_price_history(symbol, hours=1)
            
            # 호가 정보
            orderbook = await self.db.get_latest_orderbook(symbol)
            
            # 기술적 지표 계산
            technical_indicators = self._calculate_technical_indicators(price_history)
            
            return {
                'current_price': current_data.get('price', 0) if current_data else 0,
                'change_rate': current_data.get('change_rate', 0) if current_data else 0,
                'volume': current_data.get('volume', 0) if current_data else 0,
                'volume_ratio': current_data.get('volume_ratio', 1.0) if current_data else 1.0,
                'price_history': price_history,
                'orderbook': orderbook,
                'technical_indicators': technical_indicators,
                'market_cap': current_data.get('market_cap', 0) if current_data else 0,
                'trading_value': current_data.get('trading_value', 0) if current_data else 0
            }
        
        except Exception as e:
            self.logger.error(f"시장 데이터 수집 오류: {e}")
            return {}
    
    async def _collect_news_data(self, symbol: str) -> List[Dict[str, Any]]:
        """뉴스 데이터 수집
        
        Args:
            symbol: 종목코드
        
        Returns:
            뉴스 데이터 리스트
        """
        try:
            # 최근 1시간 뉴스
            news_list = await self.db.get_recent_news(symbol, hours=1)
            
            # 뉴스 요약 및 감정 분석
            processed_news = []
            for news in news_list[:10]:  # 최대 10개
                processed_news.append({
                    'title': news.get('title', ''),
                    'content': news.get('content', '')[:500],  # 500자 제한
                    'sentiment': news.get('sentiment', 0),
                    'importance': news.get('importance', 0),
                    'published_at': news.get('published_at', ''),
                    'source': news.get('source', '')
                })
            
            return processed_news
        
        except Exception as e:
            self.logger.error(f"뉴스 데이터 수집 오류: {e}")
            return []
    
    async def _collect_portfolio_data(self) -> Dict[str, Any]:
        """포트폴리오 데이터 수집
        
        Returns:
            포트폴리오 데이터
        """
        try:
            # 계좌 잔고
            balance = await self.db.get_account_balance()
            
            # 보유 포지션
            positions = await self.db.get_current_positions()
            
            # 오늘 거래 내역
            today_trades = await self.db.get_today_trades()
            
            # 성과 지표
            performance = await self.db.get_performance_metrics(days=1)
            
            return {
                'total_balance': balance.get('total_balance', 0),
                'available_cash': balance.get('available_cash', 0),
                'positions': positions,
                'today_trades': today_trades,
                'today_pnl': performance.get('daily_pnl', 0),
                'win_rate': performance.get('win_rate', 0),
                'total_trades_today': len(today_trades)
            }
        
        except Exception as e:
            self.logger.error(f"포트폴리오 데이터 수집 오류: {e}")
            return {}
    
    def _calculate_technical_indicators(self, price_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """기술적 지표 계산
        
        Args:
            price_history: 가격 히스토리
        
        Returns:
            기술적 지표
        """
        try:
            if not price_history or len(price_history) < 20:
                return {}
            
            prices = [float(p.get('price', 0)) for p in price_history]
            volumes = [float(p.get('volume', 0)) for p in price_history]
            
            # 이동평균
            ma5 = sum(prices[-5:]) / 5 if len(prices) >= 5 else prices[-1]
            ma20 = sum(prices[-20:]) / 20 if len(prices) >= 20 else prices[-1]
            
            # RSI 계산
            rsi = self._calculate_rsi(prices)
            
            # 볼린저 밴드
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(prices)
            
            # MACD
            macd_line, signal_line, histogram = self._calculate_macd(prices)
            
            # 거래량 지표
            volume_ma = sum(volumes[-20:]) / 20 if len(volumes) >= 20 else volumes[-1]
            volume_ratio = volumes[-1] / volume_ma if volume_ma > 0 else 1.0
            
            return {
                'ma5': ma5,
                'ma20': ma20,
                'rsi': rsi,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram,
                'volume_ratio': volume_ratio,
                'price_position': self._get_price_position(prices[-1], bb_upper, bb_lower)
            }
        
        except Exception as e:
            self.logger.error(f"기술적 지표 계산 오류: {e}")
            return {}
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """RSI 계산"""
        if len(prices) < period + 1:
            return 50.0
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        if len(gains) < period:
            return 50.0
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_bollinger_bands(self, prices: List[float], period: int = 20, std_dev: float = 2.0) -> Tuple[float, float, float]:
        """볼린저 밴드 계산"""
        if len(prices) < period:
            price = prices[-1] if prices else 0
            return price, price, price
        
        recent_prices = prices[-period:]
        middle = sum(recent_prices) / period
        
        variance = sum((p - middle) ** 2 for p in recent_prices) / period
        std = variance ** 0.5
        
        upper = middle + (std_dev * std)
        lower = middle - (std_dev * std)
        
        return upper, middle, lower
    
    def _calculate_macd(self, prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[float, float, float]:
        """MACD 계산"""
        if len(prices) < slow:
            return 0.0, 0.0, 0.0
        
        # EMA 계산
        def calculate_ema(data: List[float], period: int) -> float:
            if len(data) < period:
                return sum(data) / len(data)
            
            multiplier = 2 / (period + 1)
            ema = data[0]
            
            for price in data[1:]:
                ema = (price * multiplier) + (ema * (1 - multiplier))
            
            return ema
        
        ema_fast = calculate_ema(prices, fast)
        ema_slow = calculate_ema(prices, slow)
        
        macd_line = ema_fast - ema_slow
        
        # Signal line은 MACD의 EMA (여기서는 단순화)
        signal_line = macd_line * 0.9  # 단순화된 계산
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def _get_price_position(self, current_price: float, bb_upper: float, bb_lower: float) -> str:
        """볼린저 밴드 내 가격 위치"""
        if current_price > bb_upper:
            return "above_upper"
        elif current_price < bb_lower:
            return "below_lower"
        else:
            band_width = bb_upper - bb_lower
            if band_width > 0:
                position = (current_price - bb_lower) / band_width
                if position > 0.7:
                    return "upper_area"
                elif position < 0.3:
                    return "lower_area"
                else:
                    return "middle_area"
            else:
                return "middle_area"
    
    async def _call_openai_api(self, prompt: str) -> str:
        """OpenAI API 호출
        
        Args:
            prompt: 분석 프롬프트
        
        Returns:
            AI 응답
        """
        try:
            response = await self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.prompt_manager.get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                response_format={"type": "json_object"}
            )
            
            return response.choices[0].message.content
        
        except Exception as e:
            self.logger.error(f"OpenAI API 호출 오류: {e}")
            # 기본 응답 반환
            return json.dumps({
                "decision": "hold",
                "confidence": 30,
                "reasoning": "API 호출 오류로 인한 기본 응답",
                "target_price": None,
                "stop_loss": None,
                "quantity": 0
            })
    
    def _parse_ai_response(self, ai_response: str, symbol: str, risk_level: RiskLevel) -> TradingDecision:
        """AI 응답 파싱
        
        Args:
            ai_response: AI 응답 JSON
            symbol: 종목코드
            risk_level: 리스크 레벨
        
        Returns:
            매매 의사결정
        """
        try:
            data = json.loads(ai_response)
            
            # 의사결정 타입 변환
            decision_str = data.get('decision', 'hold').lower()
            decision = DecisionType(decision_str) if decision_str in [d.value for d in DecisionType] else DecisionType.HOLD
            
            # 신뢰도 변환
            confidence_score = float(data.get('confidence', 50))
            confidence_level = self._get_confidence_level(confidence_score)
            
            # 리스크 레벨에 따른 조정
            if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                if decision == DecisionType.BUY:
                    decision = DecisionType.HOLD
                    confidence_score *= 0.5
                    confidence_level = self._get_confidence_level(confidence_score)
            
            return TradingDecision(
                symbol=symbol,
                decision=decision,
                confidence=confidence_level,
                confidence_score=confidence_score,
                target_price=data.get('target_price'),
                stop_loss=data.get('stop_loss'),
                take_profit=data.get('take_profit'),
                quantity=int(data.get('quantity', 0)),
                reasoning=data.get('reasoning', ''),
                risk_level=risk_level,
                urgency=int(data.get('urgency', 5)),
                expected_return=float(data.get('expected_return', 0)),
                max_loss=float(data.get('max_loss', 0)),
                holding_period=data.get('holding_period', 'scalping'),
                technical_signals=data.get('technical_signals', []),
                fundamental_factors=data.get('fundamental_factors', []),
                market_conditions=data.get('market_conditions', {}),
                timestamp=datetime.now()
            )
        
        except Exception as e:
            self.logger.error(f"AI 응답 파싱 오류: {e}")
            return self._get_safe_default_decision(symbol)
    
    def _get_confidence_level(self, score: float) -> ConfidenceLevel:
        """신뢰도 점수를 레벨로 변환"""
        if score >= 90:
            return ConfidenceLevel.VERY_HIGH
        elif score >= 75:
            return ConfidenceLevel.HIGH
        elif score >= 50:
            return ConfidenceLevel.MEDIUM
        elif score >= 25:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    async def _validate_and_adjust_decision(self, decision: TradingDecision, 
                                          portfolio_data: Dict[str, Any]) -> TradingDecision:
        """의사결정 검증 및 조정
        
        Args:
            decision: 원본 의사결정
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            검증된 의사결정
        """
        try:
            # 신뢰도 임계값 체크
            if decision.confidence_score < self.min_confidence_threshold:
                decision.decision = DecisionType.HOLD
                decision.quantity = 0
                decision.reasoning += " (신뢰도 부족으로 보류)"
            
            # 매수 의사결정 검증
            if decision.decision == DecisionType.BUY:
                # 자금 여유 체크
                available_cash = portfolio_data.get('available_cash', 0)
                required_cash = decision.quantity * (decision.target_price or 0)
                
                if required_cash > available_cash:
                    # 수량 조정
                    if decision.target_price and decision.target_price > 0:
                        decision.quantity = int(available_cash * 0.95 / decision.target_price)
                    else:
                        decision.decision = DecisionType.HOLD
                        decision.quantity = 0
                
                # 개별 거래 리스크 분석
                if decision.quantity > 0 and decision.target_price:
                    trade_risk = await self.risk_analyzer.analyze_trade_risk(
                        decision.symbol, 'buy', decision.quantity, 
                        decision.target_price, portfolio_data
                    )
                    
                    if trade_risk.recommended_action == "CANCEL":
                        decision.decision = DecisionType.HOLD
                        decision.quantity = 0
                        decision.reasoning += " (거래 리스크로 인한 취소)"
            
            # 매도 의사결정 검증
            elif decision.decision == DecisionType.SELL:
                # 보유 수량 체크
                position = None
                for pos in portfolio_data.get('positions', []):
                    if pos.get('symbol') == decision.symbol:
                        position = pos
                        break
                
                if not position or position.get('quantity', 0) <= 0:
                    decision.decision = DecisionType.HOLD
                    decision.quantity = 0
                    decision.reasoning += " (보유 포지션 없음)"
                else:
                    # 수량 조정
                    max_quantity = position.get('quantity', 0)
                    decision.quantity = min(decision.quantity, max_quantity)
            
            return decision
        
        except Exception as e:
            self.logger.error(f"의사결정 검증 오류: {e}")
            return self._get_safe_default_decision(decision.symbol)
    
    def _get_safe_default_decision(self, symbol: str) -> TradingDecision:
        """안전한 기본 의사결정 반환"""
        return TradingDecision(
            symbol=symbol,
            decision=DecisionType.HOLD,
            confidence=ConfidenceLevel.LOW,
            confidence_score=30.0,
            target_price=None,
            stop_loss=None,
            take_profit=None,
            quantity=0,
            reasoning="시스템 오류로 인한 안전 모드",
            risk_level=RiskLevel.HIGH,
            urgency=1,
            expected_return=0.0,
            max_loss=0.0,
            holding_period="hold",
            technical_signals=[],
            fundamental_factors=[],
            market_conditions={},
            timestamp=datetime.now()
        )
    
    async def _record_decision(self, decision: TradingDecision):
        """의사결정 기록
        
        Args:
            decision: 매매 의사결정
        """
        try:
            await self.db.insert_ai_decision({
                'symbol': decision.symbol,
                'decision': decision.decision.value,
                'confidence': decision.confidence_score,
                'target_price': decision.target_price,
                'stop_loss': decision.stop_loss,
                'quantity': decision.quantity,
                'reasoning': decision.reasoning,
                'risk_level': decision.risk_level.value,
                'expected_return': decision.expected_return,
                'timestamp': decision.timestamp
            })
        
        except Exception as e:
            self.logger.error(f"의사결정 기록 오류: {e}")
    
    def _update_performance_metrics(self, decision: TradingDecision):
        """성능 메트릭 업데이트
        
        Args:
            decision: 매매 의사결정
        """
        try:
            self.performance_metrics['total_decisions'] += 1
            
            # 신뢰도 평균 업데이트
            total = self.performance_metrics['total_decisions']
            current_avg = self.performance_metrics['avg_confidence']
            self.performance_metrics['avg_confidence'] = (
                (current_avg * (total - 1) + decision.confidence_score) / total
            )
            
            # 의사결정 히스토리 추가 (최대 1000개)
            self.decision_history.append(decision)
            if len(self.decision_history) > 1000:
                self.decision_history.pop(0)
        
        except Exception as e:
            self.logger.error(f"성능 메트릭 업데이트 오류: {e}")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """캐시 유효성 확인
        
        Args:
            cache_key: 캐시 키
        
        Returns:
            캐시 유효 여부
        """
        if cache_key not in self.decision_cache:
            return False
        
        cache_time = self.decision_cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).total_seconds() < self.cache_ttl
    
    def _manage_cache_size(self):
        """캐시 크기 관리
        
        최대 캐시 크기를 초과하면 오래된 항목부터 제거
        """
        if len(self.decision_cache) >= self.max_cache_size:
            # 타임스탬프 기준으로 정렬하여 오래된 항목부터 제거
            sorted_items = sorted(
                self.decision_cache.items(),
                key=lambda x: x[1]['timestamp']
            )
            
            # 절반 정도 제거
            remove_count = len(sorted_items) // 2
            for i in range(remove_count):
                del self.decision_cache[sorted_items[i][0]]
            
            self.logger.debug(f"캐시 정리 완료: {remove_count}개 항목 제거")
    
    async def get_market_sentiment(self) -> Dict[str, Any]:
        """전체 시장 심리 분석
        
        Returns:
            시장 심리 분석 결과
        """
        try:
            # 최근 뉴스 감정 분석
            recent_news = await self.db.get_recent_news(hours=2)
            
            if not recent_news:
                return {
                    'sentiment': 'neutral',
                    'score': 0,
                    'confidence': 0.3
                }
            
            # 감정 점수 계산
            sentiment_scores = [news.get('sentiment', 0) for news in recent_news]
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
            
            # 감정 분류
            if avg_sentiment > 0.3:
                sentiment = 'bullish'
            elif avg_sentiment < -0.3:
                sentiment = 'bearish'
            else:
                sentiment = 'neutral'
            
            return {
                'sentiment': sentiment,
                'score': avg_sentiment,
                'confidence': min(len(recent_news) / 50, 1.0),
                'news_count': len(recent_news)
            }
        
        except Exception as e:
            self.logger.error(f"시장 심리 분석 오류: {e}")
            return {
                'sentiment': 'neutral',
                'score': 0,
                'confidence': 0.0
            }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """성능 요약 정보
        
        Returns:
            성능 요약
        """
        return {
            'total_decisions': self.performance_metrics['total_decisions'],
            'avg_confidence': self.performance_metrics['avg_confidence'],
            'recent_decisions': len([d for d in self.decision_history 
                                   if (datetime.now() - d.timestamp).total_seconds() < 3600]),
            'decision_distribution': self._get_decision_distribution(),
            'confidence_distribution': self._get_confidence_distribution()
        }
    
    def _get_decision_distribution(self) -> Dict[str, int]:
        """의사결정 분포"""
        distribution = {decision.value: 0 for decision in DecisionType}
        
        for decision in self.decision_history[-100:]:  # 최근 100개
            distribution[decision.decision.value] += 1
        
        return distribution
    
    def _get_confidence_distribution(self) -> Dict[str, int]:
        """신뢰도 분포"""
        distribution = {'high': 0, 'medium': 0, 'low': 0}
        
        for decision in self.decision_history[-100:]:  # 최근 100개
            if decision.confidence_score >= 75:
                distribution['high'] += 1
            elif decision.confidence_score >= 50:
                distribution['medium'] += 1
            else:
                distribution['low'] += 1
        
        return distribution
    
    async def analyze_news_impact(self, news_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """뉴스 영향도 분석
        
        Args:
            news_data: 뉴스 데이터 (title, content, url, source, importance 포함)
        
        Returns:
            뉴스 분석 결과
        """
        try:
            # 뉴스 제목과 내용 추출
            title = news_data.get('title', '')
            content = news_data.get('content', '')
            
            if not title:
                return None
            
            # AI 분석을 위한 뉴스 텍스트 준비
            news_text = f"제목: {title}\n내용: {content}"
            
            # PromptManager를 사용하여 뉴스 분석 프롬프트 생성
            prompt = self.prompt_manager.build_news_analysis_prompt(news_data)
            system_prompt = self.prompt_manager.get_news_analysis_system_prompt()
            
            # OpenAI API 호출
            response = await self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            # 응답 파싱
            content = response.choices[0].message.content.strip()
            
            # JSON 파싱 시도
            try:
                # JSON 블록 추출
                if '```json' in content:
                    json_start = content.find('```json') + 7
                    json_end = content.find('```', json_start)
                    json_content = content[json_start:json_end].strip()
                elif '{' in content and '}' in content:
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    json_content = content[json_start:json_end]
                else:
                    json_content = content
                
                analysis_result = json.loads(json_content)
                
                # 결과 검증 및 기본값 설정
                validated_result = {
                    'sentiment_score': max(-1.0, min(1.0, analysis_result.get('sentiment_score', 0.0))),
                    'market_impact': analysis_result.get('market_impact', 'medium'),
                    'confidence': max(0.0, min(1.0, analysis_result.get('confidence', 0.5))),
                    'decision': analysis_result.get('decision', 'neutral'),
                    'reasoning': analysis_result.get('reasoning', '분석 결과 없음'),
                    'affected_sector': analysis_result.get('affected_sector', ''),  # 가장 큰 영향받는 업종 하나
                    'affected_stock': analysis_result.get('affected_stock', ''),  # 특정 종목 또는 주도주 하나
                    'time_horizon': analysis_result.get('time_horizon', 'medium')
                }
                
                self.logger.info(f"뉴스 분석 완료 - 감정점수: {validated_result['sentiment_score']:.2f}, "
                               f"시장영향: {validated_result['market_impact']}, "
                               f"신뢰도: {validated_result['confidence']:.2f}, "
                               f"영향업종: {validated_result['affected_sector']}, "
                               f"영향종목: {validated_result['affected_stock']}")
                
                return validated_result
                
            except json.JSONDecodeError as e:
                self.logger.error(f"뉴스 분석 JSON 파싱 오류: {e}")
                # 기본 분석 결과 반환
                return {
                    'sentiment_score': 0.0,
                    'market_impact': 'low',
                    'confidence': 0.3,
                    'decision': 'neutral',
                    'reasoning': 'AI 분석 파싱 실패로 기본값 적용',
                    'affected_sector': '',  # 가장 큰 영향받는 업종 하나
                    'affected_stock': '',  # 특정 종목 또는 주도주 하나
                    'time_horizon': 'short'
                }
        
        except Exception as e:
            self.logger.error(f"뉴스 영향도 분석 오류: {e}")
            return None
    
    async def emergency_analysis(self, symbol: str) -> TradingDecision:
        """긴급 상황 분석
        
        Args:
            symbol: 종목코드
        
        Returns:
            긴급 의사결정
        """
        try:
            # 간단한 긴급 분석
            market_data = await self._collect_market_data(symbol)
            current_price = market_data.get('current_price', 0)
            change_rate = market_data.get('change_rate', 0)
            
            # 급격한 변동 체크
            if abs(change_rate) > 10:  # 10% 이상 변동
                decision = DecisionType.EMERGENCY_EXIT if change_rate < 0 else DecisionType.HOLD
                urgency = 10
                reasoning = f"급격한 가격 변동 감지 ({change_rate:.1f}%)"
            else:
                decision = DecisionType.HOLD
                urgency = 5
                reasoning = "정상 범위 내 변동"
            
            return TradingDecision(
                symbol=symbol,
                decision=decision,
                confidence=ConfidenceLevel.HIGH,
                confidence_score=85.0,
                target_price=current_price,
                stop_loss=None,
                take_profit=None,
                quantity=0,
                reasoning=reasoning,
                risk_level=RiskLevel.HIGH if abs(change_rate) > 10 else RiskLevel.MEDIUM,
                urgency=urgency,
                expected_return=0.0,
                max_loss=0.0,
                holding_period="emergency",
                technical_signals=[],
                fundamental_factors=[],
                market_conditions={'emergency': True},
                timestamp=datetime.now()
            )
        
        except Exception as e:
            self.logger.error(f"긴급 분석 오류: {e}")
            return self._get_safe_default_decision(symbol)