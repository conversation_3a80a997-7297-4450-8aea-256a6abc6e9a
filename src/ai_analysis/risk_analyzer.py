# -*- coding: utf-8 -*-
"""
리스크 분석기

포트폴리오 및 개별 거래 리스크 분석
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager

class RiskLevel(Enum):
    """리스크 레벨 열거형"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class RiskMetrics:
    """리스크 메트릭 데이터 클래스"""
    overall_risk: RiskLevel
    risk_score: float  # 0-100
    max_position_size: int
    recommended_action: str
    risk_factors: List[str]
    confidence: float

class RiskAnalyzer:
    """
    리스크 분석기 클래스
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        """리스크 분석기 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
        """
        self.config = config_manager
        self.db = db_manager
        self.logger = get_logger()
        
        # 리스크 설정
        self.max_single_position = self.config.get('risk_management.max_single_position', 0.1)  # 10%
        self.max_daily_loss = self.config.get('risk_management.max_daily_loss', 0.05)  # 5%
        self.max_total_exposure = self.config.get('risk_management.max_total_exposure', 0.8)  # 80%
        self.volatility_threshold = self.config.get('risk_management.volatility_threshold', 0.03)  # 3%
        
        # 리스크 가중치
        self.risk_weights = {
            'volatility': 0.25,
            'liquidity': 0.20,
            'concentration': 0.20,
            'market_condition': 0.15,
            'time_factor': 0.10,
            'correlation': 0.10
        }
        
        # 시장 상황별 리스크 배수
        self.market_risk_multipliers = {
            'bull': 0.8,
            'bear': 1.5,
            'sideways': 1.0,
            'volatile': 1.3
        }
    
    async def analyze_portfolio_risk(self, portfolio_data: Dict[str, Any]) -> RiskMetrics:
        """포트폴리오 전체 리스크 분석
        
        Args:
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            리스크 메트릭
        """
        try:
            risk_factors = []
            risk_scores = {}
            
            # 1. 집중도 리스크 분석
            concentration_risk = self._analyze_concentration_risk(portfolio_data)
            risk_scores['concentration'] = concentration_risk['score']
            if concentration_risk['risk_level'] != RiskLevel.LOW:
                risk_factors.extend(concentration_risk['factors'])
            
            # 2. 유동성 리스크 분석
            liquidity_risk = await self._analyze_liquidity_risk(portfolio_data)
            risk_scores['liquidity'] = liquidity_risk['score']
            if liquidity_risk['risk_level'] != RiskLevel.LOW:
                risk_factors.extend(liquidity_risk['factors'])
            
            # 3. 변동성 리스크 분석
            volatility_risk = await self._analyze_volatility_risk(portfolio_data)
            risk_scores['volatility'] = volatility_risk['score']
            if volatility_risk['risk_level'] != RiskLevel.LOW:
                risk_factors.extend(volatility_risk['factors'])
            
            # 4. 시장 상황 리스크 분석
            market_risk = await self._analyze_market_condition_risk()
            risk_scores['market_condition'] = market_risk['score']
            if market_risk['risk_level'] != RiskLevel.LOW:
                risk_factors.extend(market_risk['factors'])
            
            # 5. 시간 리스크 분석
            time_risk = self._analyze_time_risk()
            risk_scores['time_factor'] = time_risk['score']
            if time_risk['risk_level'] != RiskLevel.LOW:
                risk_factors.extend(time_risk['factors'])
            
            # 6. 상관관계 리스크 분석
            correlation_risk = await self._analyze_correlation_risk(portfolio_data)
            risk_scores['correlation'] = correlation_risk['score']
            if correlation_risk['risk_level'] != RiskLevel.LOW:
                risk_factors.extend(correlation_risk['factors'])
            
            # 종합 리스크 점수 계산
            overall_score = sum(
                score * self.risk_weights[factor]
                for factor, score in risk_scores.items()
            )
            
            # 리스크 레벨 결정
            overall_risk = self._determine_risk_level(overall_score)
            
            # 권장 액션 결정
            recommended_action = self._get_recommended_action(overall_risk, risk_factors)
            
            # 최대 포지션 크기 계산
            max_position_size = self._calculate_max_position_size(overall_score, portfolio_data)
            
            return RiskMetrics(
                overall_risk=overall_risk,
                risk_score=overall_score,
                max_position_size=max_position_size,
                recommended_action=recommended_action,
                risk_factors=list(set(risk_factors)),
                confidence=0.85
            )
        
        except Exception as e:
            self.logger.error(f"포트폴리오 리스크 분석 오류: {e}")
            return RiskMetrics(
                overall_risk=RiskLevel.HIGH,
                risk_score=80.0,
                max_position_size=0,
                recommended_action="HOLD",
                risk_factors=["분석 오류 발생"],
                confidence=0.0
            )
    
    def _analyze_concentration_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """집중도 리스크 분석
        
        Args:
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            집중도 리스크 분석 결과
        """
        try:
            positions = portfolio_data.get('positions', [])
            total_value = portfolio_data.get('total_balance', 1)
            
            if not positions:
                return {
                    'score': 0,
                    'risk_level': RiskLevel.LOW,
                    'factors': []
                }
            
            # 개별 포지션 비중 계산
            position_weights = []
            for position in positions:
                weight = position.get('eval_amount', 0) / total_value
                position_weights.append(weight)
            
            # 최대 포지션 비중
            max_weight = max(position_weights) if position_weights else 0
            
            # 상위 3개 포지션 집중도
            top3_concentration = sum(sorted(position_weights, reverse=True)[:3])
            
            # 허핀달 지수 (집중도 측정)
            hhi = sum(w**2 for w in position_weights)
            
            risk_factors = []
            score = 0
            
            # 최대 포지션 비중 체크
            if max_weight > self.max_single_position * 1.5:
                score += 40
                risk_factors.append(f"단일 포지션 과도한 집중 ({max_weight:.1%})")
            elif max_weight > self.max_single_position:
                score += 20
                risk_factors.append(f"단일 포지션 집중도 높음 ({max_weight:.1%})")
            
            # 상위 3개 집중도 체크
            if top3_concentration > 0.6:
                score += 30
                risk_factors.append(f"상위 3개 종목 과도한 집중 ({top3_concentration:.1%})")
            elif top3_concentration > 0.4:
                score += 15
                risk_factors.append(f"상위 3개 종목 집중도 높음 ({top3_concentration:.1%})")
            
            # HHI 체크 (0.25 이상이면 고집중)
            if hhi > 0.25:
                score += 20
                risk_factors.append(f"포트폴리오 집중도 높음 (HHI: {hhi:.3f})")
            
            risk_level = self._determine_risk_level(score)
            
            return {
                'score': score,
                'risk_level': risk_level,
                'factors': risk_factors
            }
        
        except Exception as e:
            self.logger.error(f"집중도 리스크 분석 오류: {e}")
            return {
                'score': 50,
                'risk_level': RiskLevel.MEDIUM,
                'factors': ["집중도 분석 오류"]
            }
    
    async def _analyze_liquidity_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """유동성 리스크 분석
        
        Args:
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            유동성 리스크 분석 결과
        """
        try:
            positions = portfolio_data.get('positions', [])
            risk_factors = []
            score = 0
            
            for position in positions:
                symbol = position.get('symbol')
                quantity = position.get('quantity', 0)
                
                # 최신 시장 데이터 조회
                market_data = await self.db.get_latest_market_data(symbol)
                if not market_data:
                    score += 20
                    risk_factors.append(f"{symbol}: 시장 데이터 없음")
                    continue
                
                # 거래량 분석
                volume = market_data.get('volume', 0)
                avg_volume = market_data.get('volume_ma_20', volume)  # 20일 평균 거래량
                
                # 보유 수량 대비 일평균 거래량 비율
                if avg_volume > 0:
                    liquidity_ratio = quantity / avg_volume
                    
                    if liquidity_ratio > 0.1:  # 보유량이 일평균 거래량의 10% 초과
                        score += 30
                        risk_factors.append(f"{symbol}: 유동성 부족 (보유비중 {liquidity_ratio:.1%})")
                    elif liquidity_ratio > 0.05:  # 5% 초과
                        score += 15
                        risk_factors.append(f"{symbol}: 유동성 주의 (보유비중 {liquidity_ratio:.1%})")
                
                # 호가 스프레드 분석 (있는 경우)
                bid_price = market_data.get('bid_price', 0)
                ask_price = market_data.get('ask_price', 0)
                
                if bid_price > 0 and ask_price > 0:
                    spread_ratio = (ask_price - bid_price) / bid_price
                    
                    if spread_ratio > 0.02:  # 스프레드가 2% 초과
                        score += 25
                        risk_factors.append(f"{symbol}: 호가 스프레드 과대 ({spread_ratio:.2%})")
                    elif spread_ratio > 0.01:  # 1% 초과
                        score += 10
                        risk_factors.append(f"{symbol}: 호가 스프레드 높음 ({spread_ratio:.2%})")
            
            risk_level = self._determine_risk_level(score)
            
            return {
                'score': score,
                'risk_level': risk_level,
                'factors': risk_factors
            }
        
        except Exception as e:
            self.logger.error(f"유동성 리스크 분석 오류: {e}")
            return {
                'score': 40,
                'risk_level': RiskLevel.MEDIUM,
                'factors': ["유동성 분석 오류"]
            }
    
    async def _analyze_volatility_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """변동성 리스크 분석
        
        Args:
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            변동성 리스크 분석 결과
        """
        try:
            positions = portfolio_data.get('positions', [])
            risk_factors = []
            score = 0
            
            volatilities = []
            
            for position in positions:
                symbol = position.get('symbol')
                
                # 최근 가격 히스토리 조회 (1시간)
                # 실제로는 데이터베이스에서 조회해야 함
                # 여기서는 시뮬레이션
                recent_prices = await self._get_recent_prices(symbol, minutes=60)
                
                if len(recent_prices) >= 10:
                    # 변동성 계산 (표준편차)
                    price_changes = []
                    for i in range(1, len(recent_prices)):
                        change = (recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
                        price_changes.append(change)
                    
                    if price_changes:
                        volatility = np.std(price_changes)
                        volatilities.append(volatility)
                        
                        # 변동성 임계값 체크
                        if volatility > self.volatility_threshold * 2:
                            score += 35
                            risk_factors.append(f"{symbol}: 극심한 변동성 ({volatility:.2%})")
                        elif volatility > self.volatility_threshold:
                            score += 20
                            risk_factors.append(f"{symbol}: 높은 변동성 ({volatility:.2%})")
            
            # 포트폴리오 전체 변동성
            if volatilities:
                avg_volatility = np.mean(volatilities)
                max_volatility = max(volatilities)
                
                if max_volatility > self.volatility_threshold * 3:
                    score += 25
                    risk_factors.append(f"포트폴리오 최대 변동성 과도 ({max_volatility:.2%})")
                
                if avg_volatility > self.volatility_threshold * 1.5:
                    score += 15
                    risk_factors.append(f"포트폴리오 평균 변동성 높음 ({avg_volatility:.2%})")
            
            risk_level = self._determine_risk_level(score)
            
            return {
                'score': score,
                'risk_level': risk_level,
                'factors': risk_factors
            }
        
        except Exception as e:
            self.logger.error(f"변동성 리스크 분석 오류: {e}")
            return {
                'score': 30,
                'risk_level': RiskLevel.MEDIUM,
                'factors': ["변동성 분석 오류"]
            }
    
    async def _analyze_market_condition_risk(self) -> Dict[str, Any]:
        """시장 상황 리스크 분석
        
        Returns:
            시장 상황 리스크 분석 결과
        """
        try:
            risk_factors = []
            score = 0
            
            # 현재 시간 체크
            now = datetime.now()
            current_hour = now.hour
            current_minute = now.minute
            
            # 장 마감 임박 리스크
            if current_hour == 15 and current_minute >= 20:
                score += 40
                risk_factors.append("장 마감 임박 (10분 이내)")
            elif current_hour == 15 and current_minute >= 10:
                score += 25
                risk_factors.append("장 마감 임박 (20분 이내)")
            elif current_hour == 15:
                score += 15
                risk_factors.append("장 마감 시간대")
            
            # 장 초반 변동성 리스크
            if current_hour == 9 and current_minute < 30:
                score += 20
                risk_factors.append("장 초반 높은 변동성 시간대")
            
            # 점심시간 유동성 리스크
            if 12 <= current_hour < 13:
                score += 10
                risk_factors.append("점심시간 유동성 감소")
            
            # 주말 및 공휴일 체크
            if now.weekday() >= 5:  # 토요일, 일요일
                score += 100  # 최대 리스크
                risk_factors.append("주말 - 거래 불가")
            
            # VIX 지수 등 시장 공포 지수 (실제로는 외부 API에서 가져와야 함)
            # 여기서는 시뮬레이션
            market_fear_index = 20  # 0-100
            
            if market_fear_index > 70:
                score += 30
                risk_factors.append(f"시장 공포 지수 높음 ({market_fear_index})")
            elif market_fear_index > 50:
                score += 15
                risk_factors.append(f"시장 불안정 ({market_fear_index})")
            
            risk_level = self._determine_risk_level(score)
            
            return {
                'score': score,
                'risk_level': risk_level,
                'factors': risk_factors
            }
        
        except Exception as e:
            self.logger.error(f"시장 상황 리스크 분석 오류: {e}")
            return {
                'score': 25,
                'risk_level': RiskLevel.MEDIUM,
                'factors': ["시장 상황 분석 오류"]
            }
    
    def _analyze_time_risk(self) -> Dict[str, Any]:
        """시간 리스크 분석
        
        Returns:
            시간 리스크 분석 결과
        """
        try:
            risk_factors = []
            score = 0
            
            now = datetime.now()
            
            # 장 마감까지 남은 시간
            if now.weekday() < 5:  # 평일
                close_time = now.replace(hour=15, minute=30, second=0).replace(microsecond=0)
                if now < close_time:
                    time_to_close = (close_time - now).total_seconds() / 60  # 분
                    
                    if time_to_close < 10:
                        score += 50
                        risk_factors.append(f"장 마감 임박 ({time_to_close:.0f}분 남음)")
                    elif time_to_close < 30:
                        score += 30
                        risk_factors.append(f"장 마감 근접 ({time_to_close:.0f}분 남음)")
                    elif time_to_close < 60:
                        score += 15
                        risk_factors.append(f"장 마감 1시간 이내")
            
            # 금요일 리스크 (주말 갭 위험)
            if now.weekday() == 4:  # 금요일
                score += 10
                risk_factors.append("금요일 - 주말 갭 리스크")
            
            # 월요일 리스크 (주말 뉴스 영향)
            if now.weekday() == 0 and now.hour < 11:  # 월요일 오전
                score += 15
                risk_factors.append("월요일 오전 - 주말 뉴스 영향")
            
            risk_level = self._determine_risk_level(score)
            
            return {
                'score': score,
                'risk_level': risk_level,
                'factors': risk_factors
            }
        
        except Exception as e:
            self.logger.error(f"시간 리스크 분석 오류: {e}")
            return {
                'score': 20,
                'risk_level': RiskLevel.MEDIUM,
                'factors': ["시간 분석 오류"]
            }
    
    async def _analyze_correlation_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """상관관계 리스크 분석
        
        Args:
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            상관관계 리스크 분석 결과
        """
        try:
            positions = portfolio_data.get('positions', [])
            risk_factors = []
            score = 0
            
            if len(positions) < 2:
                return {
                    'score': 0,
                    'risk_level': RiskLevel.LOW,
                    'factors': []
                }
            
            # 섹터별 집중도 분석 (실제로는 종목 정보에서 섹터 정보를 가져와야 함)
            # 여기서는 시뮬레이션
            sector_exposure = {}
            
            for position in positions:
                symbol = position.get('symbol')
                weight = position.get('eval_amount', 0) / portfolio_data.get('total_balance', 1)
                
                # 간단한 섹터 매핑 (실제로는 더 정교해야 함)
                sector = self._get_sector_by_symbol(symbol)
                
                if sector in sector_exposure:
                    sector_exposure[sector] += weight
                else:
                    sector_exposure[sector] = weight
            
            # 섹터 집중도 체크
            max_sector_weight = max(sector_exposure.values()) if sector_exposure else 0
            
            if max_sector_weight > 0.5:
                score += 30
                risk_factors.append(f"단일 섹터 과도한 집중 ({max_sector_weight:.1%})")
            elif max_sector_weight > 0.3:
                score += 15
                risk_factors.append(f"단일 섹터 집중도 높음 ({max_sector_weight:.1%})")
            
            # 대형주/중소형주 집중도 (시가총액 기준)
            large_cap_weight = 0
            small_cap_weight = 0
            
            for position in positions:
                symbol = position.get('symbol')
                weight = position.get('eval_amount', 0) / portfolio_data.get('total_balance', 1)
                
                # 간단한 시가총액 분류 (실제로는 더 정교해야 함)
                if self._is_large_cap(symbol):
                    large_cap_weight += weight
                else:
                    small_cap_weight += weight
            
            if small_cap_weight > 0.6:
                score += 25
                risk_factors.append(f"중소형주 과도한 집중 ({small_cap_weight:.1%})")
            elif small_cap_weight > 0.4:
                score += 10
                risk_factors.append(f"중소형주 집중도 높음 ({small_cap_weight:.1%})")
            
            risk_level = self._determine_risk_level(score)
            
            return {
                'score': score,
                'risk_level': risk_level,
                'factors': risk_factors
            }
        
        except Exception as e:
            self.logger.error(f"상관관계 리스크 분석 오류: {e}")
            return {
                'score': 20,
                'risk_level': RiskLevel.MEDIUM,
                'factors': ["상관관계 분석 오류"]
            }
    
    def _determine_risk_level(self, score: float) -> RiskLevel:
        """리스크 점수를 기반으로 리스크 레벨 결정
        
        Args:
            score: 리스크 점수 (0-100)
        
        Returns:
            리스크 레벨
        """
        if score >= 70:
            return RiskLevel.CRITICAL
        elif score >= 50:
            return RiskLevel.HIGH
        elif score >= 25:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _get_recommended_action(self, risk_level: RiskLevel, risk_factors: List[str]) -> str:
        """리스크 레벨에 따른 권장 액션 결정
        
        Args:
            risk_level: 리스크 레벨
            risk_factors: 리스크 요인 리스트
        
        Returns:
            권장 액션
        """
        if risk_level == RiskLevel.CRITICAL:
            return "EMERGENCY_EXIT"  # 긴급 청산
        elif risk_level == RiskLevel.HIGH:
            if any("장 마감" in factor for factor in risk_factors):
                return "CLOSE_POSITIONS"  # 포지션 청산
            else:
                return "REDUCE_EXPOSURE"  # 노출 축소
        elif risk_level == RiskLevel.MEDIUM:
            return "MONITOR_CLOSELY"  # 면밀한 모니터링
        else:
            return "NORMAL_OPERATION"  # 정상 운영
    
    def _calculate_max_position_size(self, risk_score: float, portfolio_data: Dict[str, Any]) -> int:
        """리스크 점수에 따른 최대 포지션 크기 계산
        
        Args:
            risk_score: 리스크 점수
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            최대 포지션 크기 (원)
        """
        total_balance = portfolio_data.get('total_balance', 0)
        
        # 기본 최대 포지션 크기
        base_max_position = total_balance * self.max_single_position
        
        # 리스크 점수에 따른 조정
        if risk_score >= 70:
            multiplier = 0.2  # 80% 축소
        elif risk_score >= 50:
            multiplier = 0.5  # 50% 축소
        elif risk_score >= 25:
            multiplier = 0.7  # 30% 축소
        else:
            multiplier = 1.0  # 조정 없음
        
        return int(base_max_position * multiplier)
    
    async def _get_recent_prices(self, symbol: str, minutes: int = 60) -> List[float]:
        """최근 가격 데이터 조회
        
        Args:
            symbol: 종목코드
            minutes: 조회할 분 수
        
        Returns:
            가격 리스트
        """
        try:
            # 실제로는 데이터베이스에서 조회
            # 여기서는 시뮬레이션 데이터 반환
            base_price = 50000
            prices = []
            
            for i in range(minutes):
                # 랜덤 워크 시뮬레이션
                change = np.random.normal(0, 0.001)  # 0.1% 표준편차
                if i == 0:
                    price = base_price
                else:
                    price = prices[-1] * (1 + change)
                prices.append(price)
            
            return prices
        
        except Exception as e:
            self.logger.error(f"가격 데이터 조회 오류: {e}")
            return []
    
    def _get_sector_by_symbol(self, symbol: str) -> str:
        """종목코드로 섹터 조회
        
        Args:
            symbol: 종목코드
        
        Returns:
            섹터명
        """
        # 간단한 매핑 (실제로는 더 정교한 데이터베이스 조회 필요)
        sector_map = {
            '005930': 'IT',  # 삼성전자
            '000660': 'IT',  # SK하이닉스
            '035420': 'IT',  # NAVER
            '005380': 'AUTO',  # 현대차
            '051910': 'BIO',  # LG화학
            '035720': 'IT',  # 카카오
        }
        
        return sector_map.get(symbol, 'ETC')
    
    def _is_large_cap(self, symbol: str) -> bool:
        """대형주 여부 판단
        
        Args:
            symbol: 종목코드
        
        Returns:
            대형주 여부
        """
        # 간단한 매핑 (실제로는 시가총액 데이터 필요)
        large_caps = ['005930', '000660', '035420', '005380', '051910', '035720']
        return symbol in large_caps
    
    async def analyze_trade_risk(self, symbol: str, side: str, quantity: int, 
                               price: float, portfolio_data: Dict[str, Any]) -> RiskMetrics:
        """개별 거래 리스크 분석
        
        Args:
            symbol: 종목코드
            side: 매매구분 (buy/sell)
            quantity: 수량
            price: 가격
            portfolio_data: 포트폴리오 데이터
        
        Returns:
            거래 리스크 메트릭
        """
        try:
            risk_factors = []
            score = 0
            
            trade_value = quantity * price
            total_balance = portfolio_data.get('total_balance', 1)
            
            # 거래 규모 리스크
            trade_ratio = trade_value / total_balance
            
            if trade_ratio > self.max_single_position * 1.5:
                score += 50
                risk_factors.append(f"거래 규모 과대 ({trade_ratio:.1%})")
            elif trade_ratio > self.max_single_position:
                score += 30
                risk_factors.append(f"거래 규모 큼 ({trade_ratio:.1%})")
            
            # 기존 포지션과의 관계
            existing_position = None
            for pos in portfolio_data.get('positions', []):
                if pos.get('symbol') == symbol:
                    existing_position = pos
                    break
            
            if existing_position and side == 'buy':
                # 추가 매수시 집중도 증가
                current_value = existing_position.get('eval_amount', 0)
                new_total_value = current_value + trade_value
                new_ratio = new_total_value / total_balance
                
                if new_ratio > self.max_single_position * 2:
                    score += 40
                    risk_factors.append(f"단일 종목 과도한 집중 예상 ({new_ratio:.1%})")
                elif new_ratio > self.max_single_position * 1.5:
                    score += 25
                    risk_factors.append(f"단일 종목 집중도 높음 ({new_ratio:.1%})")
            
            # 시장 데이터 기반 리스크
            market_data = await self.db.get_latest_market_data(symbol)
            if market_data:
                # 변동성 체크
                change_rate = abs(market_data.get('change_rate', 0))
                if change_rate > 5:
                    score += 30
                    risk_factors.append(f"높은 변동성 ({change_rate:.1f}%)")
                elif change_rate > 3:
                    score += 15
                    risk_factors.append(f"변동성 주의 ({change_rate:.1f}%)")
                
                # 거래량 체크
                volume_ratio = market_data.get('volume_ratio', 1.0)
                if volume_ratio < 0.3:
                    score += 25
                    risk_factors.append(f"거래량 부족 (평균 대비 {volume_ratio:.1f}배)")
                elif volume_ratio < 0.5:
                    score += 10
                    risk_factors.append(f"거래량 감소 (평균 대비 {volume_ratio:.1f}배)")
            
            # 시간 리스크
            time_risk = self._analyze_time_risk()
            score += time_risk['score'] * 0.3  # 가중치 적용
            risk_factors.extend(time_risk['factors'])
            
            risk_level = self._determine_risk_level(score)
            recommended_action = "PROCEED" if risk_level in [RiskLevel.LOW, RiskLevel.MEDIUM] else "CANCEL"
            
            return RiskMetrics(
                overall_risk=risk_level,
                risk_score=score,
                max_position_size=int(trade_value) if recommended_action == "PROCEED" else 0,
                recommended_action=recommended_action,
                risk_factors=risk_factors,
                confidence=0.8
            )
        
        except Exception as e:
            self.logger.error(f"거래 리스크 분석 오류: {e}")
            return RiskMetrics(
                overall_risk=RiskLevel.HIGH,
                risk_score=70.0,
                max_position_size=0,
                recommended_action="CANCEL",
                risk_factors=["분석 오류 발생"],
                confidence=0.0
            )
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """리스크 분석 요약 정보
        
        Returns:
            리스크 분석 요약
        """
        return {
            'max_single_position': self.max_single_position,
            'max_daily_loss': self.max_daily_loss,
            'max_total_exposure': self.max_total_exposure,
            'volatility_threshold': self.volatility_threshold,
            'risk_weights': self.risk_weights
        }