# -*- coding: utf-8 -*-
"""
알림 관리자

시스템 알림 및 경고 관리
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import json

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager

class AlertSeverity(Enum):
    """알림 심각도"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """알림 유형"""
    SYSTEM = "system"
    TRADING = "trading"
    PERFORMANCE = "performance"
    RISK = "risk"
    MARKET = "market"
    ERROR = "error"

class AlertStatus(Enum):
    """알림 상태"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"

@dataclass
class Alert:
    """알림 데이터 클래스"""
    id: str
    type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    timestamp: datetime
    status: AlertStatus = AlertStatus.ACTIVE
    source: str = ""
    metadata: Dict[str, Any] = None
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class AlertManager:
    """
    알림 관리자 클래스
    
    시스템 알림 및 경고를 관리하고 적절한 채널로 전송
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        """알림 관리자 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
        """
        self.config = config_manager
        self.db = db_manager
        self.logger = get_logger()
        
        # 활성 알림
        self.active_alerts: Dict[str, Alert] = {}
        
        # 알림 규칙
        self.alert_rules = {
            # 시스템 알림
            'high_cpu_usage': {
                'type': AlertType.SYSTEM,
                'severity': AlertSeverity.HIGH,
                'threshold': 80.0,
                'duration': 300,  # 5분
                'cooldown': 1800,  # 30분
                'message': 'CPU 사용률이 {value:.1f}%로 높습니다'
            },
            'high_memory_usage': {
                'type': AlertType.SYSTEM,
                'severity': AlertSeverity.HIGH,
                'threshold': 85.0,
                'duration': 300,
                'cooldown': 1800,
                'message': '메모리 사용률이 {value:.1f}%로 높습니다'
            },
            'api_error_rate': {
                'type': AlertType.SYSTEM,
                'severity': AlertSeverity.MEDIUM,
                'threshold': 5.0,
                'duration': 600,
                'cooldown': 3600,
                'message': 'API 오류율이 {value:.2f}%로 높습니다'
            },
            
            # 거래 알림
            'large_loss': {
                'type': AlertType.TRADING,
                'severity': AlertSeverity.HIGH,
                'threshold': -100000,  # -10만원
                'duration': 0,
                'cooldown': 3600,
                'message': '큰 손실이 발생했습니다: {value:,.0f}원'
            },
            'daily_loss_limit': {
                'type': AlertType.TRADING,
                'severity': AlertSeverity.CRITICAL,
                'threshold': -500000,  # -50만원
                'duration': 0,
                'cooldown': 0,
                'message': '일일 손실 한도를 초과했습니다: {value:,.0f}원'
            },
            'position_concentration': {
                'type': AlertType.RISK,
                'severity': AlertSeverity.MEDIUM,
                'threshold': 30.0,  # 30%
                'duration': 0,
                'cooldown': 7200,
                'message': '포지션 집중도가 {value:.1f}%로 높습니다'
            },
            
            # 성능 알림
            'low_win_rate': {
                'type': AlertType.PERFORMANCE,
                'severity': AlertSeverity.MEDIUM,
                'threshold': 40.0,  # 40%
                'duration': 0,
                'cooldown': 7200,
                'message': '승률이 {value:.1f}%로 낮습니다'
            },
            'high_drawdown': {
                'type': AlertType.PERFORMANCE,
                'severity': AlertSeverity.HIGH,
                'threshold': 10.0,  # 10%
                'duration': 0,
                'cooldown': 3600,
                'message': '최대 낙폭이 {value:.2f}%입니다'
            },
            
            # 시장 알림
            'market_volatility': {
                'type': AlertType.MARKET,
                'severity': AlertSeverity.MEDIUM,
                'threshold': 3.0,  # 3%
                'duration': 0,
                'cooldown': 3600,
                'message': '시장 변동성이 {value:.2f}%로 높습니다'
            }
        }
        
        # 알림 콜백
        self.alert_callbacks: List[Callable] = []
        
        # 알림 히스토리
        self.alert_history: List[Alert] = []
        self.max_history_size = 1000
        
        # 알림 통계
        self.alert_stats = {
            'total_alerts': 0,
            'alerts_by_type': {},
            'alerts_by_severity': {},
            'last_reset': datetime.now()
        }
        
        # 억제된 알림
        self.suppressed_alerts: Dict[str, datetime] = {}
        
        # 모니터링 태스크
        self.monitoring_task = None
        self.is_running = False
        
        # 알림 큐
        self.alert_queue = asyncio.Queue()
        self.processor_task = None
    
    async def initialize(self):
        """알림 관리자 초기화"""
        try:
            # 기존 알림 로드
            await self._load_active_alerts()
            
            # 알림 처리 시작
            await self.start()
            
            self.logger.info("알림 관리자 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"알림 관리자 초기화 오류: {e}")
    
    async def start(self):
        """알림 모니터링 시작"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 알림 처리 태스크 시작
        self.processor_task = asyncio.create_task(self._alert_processor_loop())
        
        # 모니터링 태스크 시작
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        self.logger.info("알림 모니터링 시작")
    
    async def stop(self):
        """알림 모니터링 중지"""
        self.is_running = False
        
        # 태스크 취소
        if self.processor_task:
            self.processor_task.cancel()
            try:
                await self.processor_task
            except asyncio.CancelledError:
                pass
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("알림 모니터링 중지")
    
    async def _alert_processor_loop(self):
        """알림 처리 루프"""
        while self.is_running:
            try:
                # 알림 큐에서 알림 가져오기
                try:
                    alert = await asyncio.wait_for(self.alert_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # 알림 처리
                await self._process_alert(alert)
                
                # 처리 완료 표시
                self.alert_queue.task_done()
            
            except Exception as e:
                self.logger.error(f"알림 처리 루프 오류: {e}")
                await asyncio.sleep(5)
    
    async def _monitoring_loop(self):
        """모니터링 루프"""
        while self.is_running:
            try:
                # 활성 알림 상태 확인
                await self._check_active_alerts()
                
                # 오래된 알림 정리
                await self._cleanup_old_alerts()
                
                # 억제된 알림 해제 확인
                await self._check_suppressed_alerts()
                
                # 30초 대기
                await asyncio.sleep(30)
            
            except Exception as e:
                self.logger.error(f"모니터링 루프 오류: {e}")
                await asyncio.sleep(60)
    
    async def create_alert(
        self,
        alert_type: AlertType,
        severity: AlertSeverity,
        title: str,
        message: str,
        source: str = "",
        metadata: Dict[str, Any] = None
    ) -> str:
        """알림 생성
        
        Args:
            alert_type: 알림 유형
            severity: 심각도
            title: 제목
            message: 메시지
            source: 소스
            metadata: 메타데이터
        
        Returns:
            알림 ID
        """
        try:
            # 알림 ID 생성
            alert_id = f"{alert_type.value}_{int(datetime.now().timestamp() * 1000)}"
            
            # 알림 객체 생성
            alert = Alert(
                id=alert_id,
                type=alert_type,
                severity=severity,
                title=title,
                message=message,
                timestamp=datetime.now(),
                source=source,
                metadata=metadata or {}
            )
            
            # 알림 큐에 추가
            await self.alert_queue.put(alert)
            
            return alert_id
        
        except Exception as e:
            self.logger.error(f"알림 생성 오류: {e}")
            return ""
    
    async def _process_alert(self, alert: Alert):
        """알림 처리
        
        Args:
            alert: 처리할 알림
        """
        try:
            # 중복 알림 확인
            if await self._is_duplicate_alert(alert):
                self.logger.debug(f"중복 알림 무시: {alert.title}")
                return
            
            # 억제된 알림 확인
            if await self._is_suppressed_alert(alert):
                self.logger.debug(f"억제된 알림 무시: {alert.title}")
                return
            
            # 활성 알림에 추가
            self.active_alerts[alert.id] = alert
            
            # 히스토리에 추가
            self.alert_history.append(alert)
            if len(self.alert_history) > self.max_history_size:
                self.alert_history.pop(0)
            
            # 통계 업데이트
            await self._update_alert_stats(alert)
            
            # 데이터베이스에 저장
            await self._save_alert(alert)
            
            # 콜백 호출
            await self._notify_callbacks(alert)
            
            self.logger.info(f"알림 처리 완료: {alert.title} ({alert.severity.value})")
        
        except Exception as e:
            self.logger.error(f"알림 처리 오류: {e}")
    
    async def _is_duplicate_alert(self, alert: Alert) -> bool:
        """중복 알림 확인
        
        Args:
            alert: 확인할 알림
        
        Returns:
            중복 여부
        """
        try:
            # 같은 유형의 활성 알림 확인
            for active_alert in self.active_alerts.values():
                if (
                    active_alert.type == alert.type and
                    active_alert.title == alert.title and
                    active_alert.status == AlertStatus.ACTIVE
                ):
                    # 시간 차이 확인 (5분 이내)
                    time_diff = (alert.timestamp - active_alert.timestamp).total_seconds()
                    if time_diff < 300:
                        return True
            
            return False
        
        except Exception as e:
            self.logger.error(f"중복 알림 확인 오류: {e}")
            return False
    
    async def _is_suppressed_alert(self, alert: Alert) -> bool:
        """억제된 알림 확인
        
        Args:
            alert: 확인할 알림
        
        Returns:
            억제 여부
        """
        try:
            suppression_key = f"{alert.type.value}_{alert.title}"
            
            if suppression_key in self.suppressed_alerts:
                suppressed_until = self.suppressed_alerts[suppression_key]
                if datetime.now() < suppressed_until:
                    return True
                else:
                    # 억제 해제
                    del self.suppressed_alerts[suppression_key]
            
            return False
        
        except Exception as e:
            self.logger.error(f"억제된 알림 확인 오류: {e}")
            return False
    
    async def _update_alert_stats(self, alert: Alert):
        """알림 통계 업데이트
        
        Args:
            alert: 알림
        """
        try:
            self.alert_stats['total_alerts'] += 1
            
            # 유형별 통계
            alert_type = alert.type.value
            if alert_type not in self.alert_stats['alerts_by_type']:
                self.alert_stats['alerts_by_type'][alert_type] = 0
            self.alert_stats['alerts_by_type'][alert_type] += 1
            
            # 심각도별 통계
            severity = alert.severity.value
            if severity not in self.alert_stats['alerts_by_severity']:
                self.alert_stats['alerts_by_severity'][severity] = 0
            self.alert_stats['alerts_by_severity'][severity] += 1
        
        except Exception as e:
            self.logger.error(f"알림 통계 업데이트 오류: {e}")
    
    async def _save_alert(self, alert: Alert):
        """알림 저장
        
        Args:
            alert: 저장할 알림
        """
        try:
            alert_data = {
                'id': alert.id,
                'type': alert.type.value,
                'severity': alert.severity.value,
                'title': alert.title,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'status': alert.status.value,
                'source': alert.source,
                'metadata': json.dumps(alert.metadata),
                'acknowledged_at': alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
                'resolved_at': alert.resolved_at.isoformat() if alert.resolved_at else None
            }
            
            await self.db.save_alert(alert_data)
        
        except Exception as e:
            self.logger.error(f"알림 저장 오류: {e}")
    
    async def _notify_callbacks(self, alert: Alert):
        """콜백 알림
        
        Args:
            alert: 알림
        """
        try:
            for callback in self.alert_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(alert)
                    else:
                        callback(alert)
                except Exception as e:
                    self.logger.error(f"콜백 호출 오류: {e}")
        
        except Exception as e:
            self.logger.error(f"콜백 알림 오류: {e}")
    
    async def acknowledge_alert(self, alert_id: str, user: str = "system") -> bool:
        """알림 확인
        
        Args:
            alert_id: 알림 ID
            user: 확인한 사용자
        
        Returns:
            확인 성공 여부
        """
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.status = AlertStatus.ACKNOWLEDGED
                alert.acknowledged_at = datetime.now()
                alert.metadata['acknowledged_by'] = user
                
                # 데이터베이스 업데이트
                await self._save_alert(alert)
                
                self.logger.info(f"알림 확인: {alert.title} by {user}")
                return True
            
            return False
        
        except Exception as e:
            self.logger.error(f"알림 확인 오류: {e}")
            return False
    
    async def resolve_alert(self, alert_id: str, user: str = "system") -> bool:
        """알림 해결
        
        Args:
            alert_id: 알림 ID
            user: 해결한 사용자
        
        Returns:
            해결 성공 여부
        """
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = datetime.now()
                alert.metadata['resolved_by'] = user
                
                # 데이터베이스 업데이트
                await self._save_alert(alert)
                
                # 활성 알림에서 제거
                del self.active_alerts[alert_id]
                
                self.logger.info(f"알림 해결: {alert.title} by {user}")
                return True
            
            return False
        
        except Exception as e:
            self.logger.error(f"알림 해결 오류: {e}")
            return False
    
    async def suppress_alert_type(self, alert_type: AlertType, title: str, duration_minutes: int):
        """알림 유형 억제
        
        Args:
            alert_type: 알림 유형
            title: 알림 제목
            duration_minutes: 억제 시간(분)
        """
        try:
            suppression_key = f"{alert_type.value}_{title}"
            suppressed_until = datetime.now() + timedelta(minutes=duration_minutes)
            
            self.suppressed_alerts[suppression_key] = suppressed_until
            
            self.logger.info(f"알림 억제: {suppression_key} until {suppressed_until}")
        
        except Exception as e:
            self.logger.error(f"알림 억제 오류: {e}")
    
    async def check_metric_threshold(self, metric_name: str, value: float, metadata: Dict[str, Any] = None):
        """메트릭 임계값 확인
        
        Args:
            metric_name: 메트릭 이름
            value: 현재 값
            metadata: 추가 메타데이터
        """
        try:
            if metric_name not in self.alert_rules:
                return
            
            rule = self.alert_rules[metric_name]
            threshold = rule['threshold']
            
            # 임계값 확인
            threshold_exceeded = False
            
            if metric_name in ['large_loss', 'daily_loss_limit']:
                # 손실 관련 (값이 임계값보다 작을 때)
                threshold_exceeded = value < threshold
            else:
                # 일반적인 경우 (값이 임계값보다 클 때)
                threshold_exceeded = value > threshold
            
            if threshold_exceeded:
                # 쿨다운 확인
                if await self._check_cooldown(metric_name):
                    # 알림 생성
                    await self.create_alert(
                        alert_type=rule['type'],
                        severity=rule['severity'],
                        title=f"{metric_name.replace('_', ' ').title()} Alert",
                        message=rule['message'].format(value=value),
                        source="metric_monitor",
                        metadata={
                            'metric_name': metric_name,
                            'value': value,
                            'threshold': threshold,
                            **(metadata or {})
                        }
                    )
        
        except Exception as e:
            self.logger.error(f"메트릭 임계값 확인 오류: {e}")
    
    async def _check_cooldown(self, metric_name: str) -> bool:
        """쿨다운 확인
        
        Args:
            metric_name: 메트릭 이름
        
        Returns:
            알림 생성 가능 여부
        """
        try:
            if metric_name not in self.alert_rules:
                return True
            
            cooldown_seconds = self.alert_rules[metric_name]['cooldown']
            if cooldown_seconds == 0:
                return True
            
            # 최근 알림 확인
            cutoff_time = datetime.now() - timedelta(seconds=cooldown_seconds)
            
            for alert in self.alert_history:
                if (
                    alert.metadata.get('metric_name') == metric_name and
                    alert.timestamp > cutoff_time
                ):
                    return False
            
            return True
        
        except Exception as e:
            self.logger.error(f"쿨다운 확인 오류: {e}")
            return True
    
    async def _check_active_alerts(self):
        """활성 알림 상태 확인"""
        try:
            current_time = datetime.now()
            
            # 오래된 활성 알림 자동 해결
            alerts_to_resolve = []
            
            for alert_id, alert in self.active_alerts.items():
                # 24시간 이상 된 알림은 자동 해결
                if (current_time - alert.timestamp).total_seconds() > 86400:
                    alerts_to_resolve.append(alert_id)
            
            for alert_id in alerts_to_resolve:
                await self.resolve_alert(alert_id, "auto_resolve")
        
        except Exception as e:
            self.logger.error(f"활성 알림 확인 오류: {e}")
    
    async def _cleanup_old_alerts(self):
        """오래된 알림 정리"""
        try:
            # 7일 이상 된 알림 히스토리 정리
            cutoff_time = datetime.now() - timedelta(days=7)
            
            self.alert_history = [
                alert for alert in self.alert_history
                if alert.timestamp > cutoff_time
            ]
            
            # 데이터베이스에서도 정리
            await self.db.cleanup_old_alerts(cutoff_time)
        
        except Exception as e:
            self.logger.error(f"오래된 알림 정리 오류: {e}")
    
    async def _check_suppressed_alerts(self):
        """억제된 알림 해제 확인"""
        try:
            current_time = datetime.now()
            
            # 만료된 억제 제거
            expired_suppressions = [
                key for key, until_time in self.suppressed_alerts.items()
                if current_time >= until_time
            ]
            
            for key in expired_suppressions:
                del self.suppressed_alerts[key]
                self.logger.info(f"알림 억제 해제: {key}")
        
        except Exception as e:
            self.logger.error(f"억제된 알림 확인 오류: {e}")
    
    async def _load_active_alerts(self):
        """활성 알림 로드"""
        try:
            # 데이터베이스에서 활성 알림 조회
            alerts_data = await self.db.get_active_alerts()
            
            for alert_data in alerts_data:
                alert = Alert(
                    id=alert_data['id'],
                    type=AlertType(alert_data['type']),
                    severity=AlertSeverity(alert_data['severity']),
                    title=alert_data['title'],
                    message=alert_data['message'],
                    timestamp=datetime.fromisoformat(alert_data['timestamp']),
                    status=AlertStatus(alert_data['status']),
                    source=alert_data['source'],
                    metadata=json.loads(alert_data['metadata']) if alert_data['metadata'] else {},
                    acknowledged_at=datetime.fromisoformat(alert_data['acknowledged_at']) if alert_data['acknowledged_at'] else None,
                    resolved_at=datetime.fromisoformat(alert_data['resolved_at']) if alert_data['resolved_at'] else None
                )
                
                self.active_alerts[alert.id] = alert
            
            self.logger.info(f"활성 알림 로드 완료: {len(self.active_alerts)}개")
        
        except Exception as e:
            self.logger.error(f"활성 알림 로드 오류: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """알림 콜백 추가
        
        Args:
            callback: 콜백 함수
        """
        try:
            self.alert_callbacks.append(callback)
            self.logger.info(f"알림 콜백 추가: {callback.__name__}")
        
        except Exception as e:
            self.logger.error(f"알림 콜백 추가 오류: {e}")
    
    def remove_alert_callback(self, callback: Callable):
        """알림 콜백 제거
        
        Args:
            callback: 콜백 함수
        """
        try:
            if callback in self.alert_callbacks:
                self.alert_callbacks.remove(callback)
                self.logger.info(f"알림 콜백 제거: {callback.__name__}")
        
        except Exception as e:
            self.logger.error(f"알림 콜백 제거 오류: {e}")
    
    def get_active_alerts(self, alert_type: Optional[AlertType] = None, severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """활성 알림 조회
        
        Args:
            alert_type: 알림 유형 필터
            severity: 심각도 필터
        
        Returns:
            활성 알림 목록
        """
        try:
            alerts = list(self.active_alerts.values())
            
            if alert_type:
                alerts = [alert for alert in alerts if alert.type == alert_type]
            
            if severity:
                alerts = [alert for alert in alerts if alert.severity == severity]
            
            return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
        
        except Exception as e:
            self.logger.error(f"활성 알림 조회 오류: {e}")
            return []
    
    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """알림 히스토리 조회
        
        Args:
            hours: 조회 시간 범위
        
        Returns:
            알림 히스토리
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            return [
                alert for alert in self.alert_history
                if alert.timestamp > cutoff_time
            ]
        
        except Exception as e:
            self.logger.error(f"알림 히스토리 조회 오류: {e}")
            return []
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """알림 통계 조회
        
        Returns:
            알림 통계
        """
        try:
            stats = self.alert_stats.copy()
            stats['active_alerts'] = len(self.active_alerts)
            stats['suppressed_alerts'] = len(self.suppressed_alerts)
            
            return stats
        
        except Exception as e:
            self.logger.error(f"알림 통계 조회 오류: {e}")
            return {}
    
    def update_alert_rules(self, new_rules: Dict[str, Dict[str, Any]]):
        """알림 규칙 업데이트
        
        Args:
            new_rules: 새로운 규칙
        """
        try:
            self.alert_rules.update(new_rules)
            self.logger.info(f"알림 규칙 업데이트: {len(new_rules)}개")
        
        except Exception as e:
            self.logger.error(f"알림 규칙 업데이트 오류: {e}")
    
    def reset_alert_stats(self):
        """알림 통계 초기화"""
        try:
            self.alert_stats = {
                'total_alerts': 0,
                'alerts_by_type': {},
                'alerts_by_severity': {},
                'last_reset': datetime.now()
            }
            
            self.logger.info("알림 통계 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"알림 통계 초기화 오류: {e}")
    
    async def send_test_alert(self):
        """테스트 알림 발송"""
        try:
            await self.create_alert(
                alert_type=AlertType.SYSTEM,
                severity=AlertSeverity.LOW,
                title="Test Alert",
                message=f"테스트 알림입니다. 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                source="test",
                metadata={'test': True}
            )
            
            self.logger.info("테스트 알림 발송 완료")
        
        except Exception as e:
            self.logger.error(f"테스트 알림 발송 오류: {e}")
    
    async def close(self):
        """알림 관리자 종료"""
        try:
            # 모니터링 중지
            await self.stop()
            
            # 활성 알림 저장
            for alert in self.active_alerts.values():
                await self._save_alert(alert)
            
            self.logger.info("알림 관리자 종료 완료")
        
        except Exception as e:
            self.logger.error(f"알림 관리자 종료 오류: {e}")