# -*- coding: utf-8 -*-
"""
성능 모니터

매매 성과 및 시스템 성능 모니터링
"""

import asyncio
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager

@dataclass
class PerformanceMetrics:
    """성능 메트릭 데이터 클래스"""
    timestamp: datetime
    
    # 수익성 지표
    total_return: float = 0.0
    daily_return: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    
    # 거래 지표
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    avg_trade_duration: float = 0.0
    avg_win_amount: float = 0.0
    avg_loss_amount: float = 0.0
    
    # 리스크 지표
    var_95: float = 0.0  # Value at Risk (95%)
    volatility: float = 0.0
    beta: float = 0.0
    correlation_kospi: float = 0.0
    
    # 시스템 지표
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    api_latency: float = 0.0
    error_rate: float = 0.0
    uptime: float = 0.0

@dataclass
class TradeAnalysis:
    """거래 분석 데이터 클래스"""
    symbol: str
    entry_time: datetime
    exit_time: datetime
    entry_price: float
    exit_price: float
    quantity: int
    pnl: float
    pnl_rate: float
    holding_period: timedelta
    trade_type: str  # scalping, day_trading
    ai_confidence: float
    market_condition: str

class PerformanceMonitor:
    """
    성능 모니터 클래스
    
    매매 성과 및 시스템 성능을 실시간으로 모니터링
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        """성능 모니터 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
        """
        self.config = config_manager
        self.db = db_manager
        self.logger = get_logger()
        
        # 성능 데이터
        self.current_metrics = PerformanceMetrics(timestamp=datetime.now())
        self.metrics_history: deque = deque(maxlen=1000)  # 최근 1000개 메트릭
        self.trade_history: List[TradeAnalysis] = []
        
        # 실시간 추적
        self.daily_returns: deque = deque(maxlen=252)  # 1년치 일일 수익률
        self.portfolio_values: deque = deque(maxlen=1000)  # 포트폴리오 가치 히스토리
        self.drawdown_history: deque = deque(maxlen=1000)  # 낙폭 히스토리
        
        # 시스템 모니터링
        self.start_time = datetime.now()
        self.api_call_times: deque = deque(maxlen=100)  # API 호출 시간
        self.error_count = 0
        self.total_operations = 0
        
        # 설정
        self.update_interval = self.config.get('monitoring.update_interval', 60)  # 1분
        self.benchmark_symbol = self.config.get('monitoring.benchmark_symbol', 'KOSPI')
        
        # 모니터링 태스크
        self.monitoring_task = None
        self.is_monitoring = False
        
        # 알림 임계값
        self.alert_thresholds = {
            'max_drawdown': self.config.get('monitoring.max_drawdown_alert', 10.0),  # 10%
            'daily_loss': self.config.get('monitoring.daily_loss_alert', 5.0),  # 5%
            'error_rate': self.config.get('monitoring.error_rate_alert', 5.0),  # 5%
            'cpu_usage': self.config.get('monitoring.cpu_usage_alert', 80.0),  # 80%
            'memory_usage': self.config.get('monitoring.memory_usage_alert', 80.0)  # 80%
        }
    
    async def initialize(self):
        """성능 모니터 초기화"""
        try:
            # 기존 데이터 로드
            await self._load_historical_data()
            
            # 모니터링 시작
            await self.start_monitoring()
            
            self.logger.info("성능 모니터 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"성능 모니터 초기화 오류: {e}")
    
    async def start_monitoring(self):
        """모니터링 시작"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("성능 모니터링 시작")
    
    async def stop_monitoring(self):
        """모니터링 중지"""
        self.is_monitoring = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("성능 모니터링 중지")
    
    async def _monitoring_loop(self):
        """모니터링 루프"""
        while self.is_monitoring:
            try:
                # 성능 메트릭 업데이트
                await self._update_performance_metrics()
                
                # 시스템 메트릭 업데이트
                await self._update_system_metrics()
                
                # 알림 확인
                await self._check_alerts()
                
                # 메트릭 저장
                await self._save_metrics()
                
                # 대기
                await asyncio.sleep(self.update_interval)
            
            except Exception as e:
                self.logger.error(f"모니터링 루프 오류: {e}")
                await asyncio.sleep(30)
    
    async def _update_performance_metrics(self):
        """성능 메트릭 업데이트"""
        try:
            # 포트폴리오 데이터 조회
            portfolio_data = await self._get_portfolio_data()
            
            if not portfolio_data:
                return
            
            # 기본 메트릭 계산
            self.current_metrics.timestamp = datetime.now()
            
            # 총 수익률
            initial_capital = self.config.get('trading.initial_capital', 10000000)  # 1천만원
            current_value = portfolio_data.get('total_value', initial_capital)
            self.current_metrics.total_return = (current_value - initial_capital) / initial_capital * 100
            
            # 포트폴리오 가치 히스토리 업데이트
            self.portfolio_values.append((datetime.now(), current_value))
            
            # 일일 수익률 계산
            if len(self.portfolio_values) >= 2:
                prev_value = self.portfolio_values[-2][1]
                self.current_metrics.daily_return = (current_value - prev_value) / prev_value * 100
                self.daily_returns.append(self.current_metrics.daily_return)
            
            # 거래 통계 업데이트
            await self._update_trade_statistics()
            
            # 리스크 메트릭 계산
            await self._calculate_risk_metrics()
            
            # 메트릭 히스토리에 추가
            self.metrics_history.append(self.current_metrics)
        
        except Exception as e:
            self.logger.error(f"성능 메트릭 업데이트 오류: {e}")
    
    async def _update_trade_statistics(self):
        """거래 통계 업데이트"""
        try:
            # 최근 거래 데이터 조회
            recent_trades = await self._get_recent_trades()
            
            if not recent_trades:
                return
            
            # 거래 통계 계산
            total_trades = len(recent_trades)
            winning_trades = len([t for t in recent_trades if t['pnl'] > 0])
            losing_trades = total_trades - winning_trades
            
            self.current_metrics.total_trades = total_trades
            self.current_metrics.winning_trades = winning_trades
            self.current_metrics.losing_trades = losing_trades
            
            # 승률
            self.current_metrics.win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            # 평균 수익/손실
            winning_amounts = [t['pnl'] for t in recent_trades if t['pnl'] > 0]
            losing_amounts = [t['pnl'] for t in recent_trades if t['pnl'] < 0]
            
            self.current_metrics.avg_win_amount = sum(winning_amounts) / len(winning_amounts) if winning_amounts else 0
            self.current_metrics.avg_loss_amount = sum(losing_amounts) / len(losing_amounts) if losing_amounts else 0
            
            # 수익 팩터 (총 수익 / 총 손실)
            total_profit = sum(winning_amounts) if winning_amounts else 0
            total_loss = abs(sum(losing_amounts)) if losing_amounts else 0
            self.current_metrics.profit_factor = total_profit / total_loss if total_loss > 0 else 0
            
            # 평균 거래 기간
            durations = [t.get('duration', 0) for t in recent_trades if t.get('duration')]
            self.current_metrics.avg_trade_duration = sum(durations) / len(durations) if durations else 0
        
        except Exception as e:
            self.logger.error(f"거래 통계 업데이트 오류: {e}")
    
    async def _calculate_risk_metrics(self):
        """리스크 메트릭 계산"""
        try:
            if len(self.daily_returns) < 30:  # 최소 30일 데이터 필요
                return
            
            returns = list(self.daily_returns)
            
            # 변동성 (일일 수익률의 표준편차)
            import statistics
            self.current_metrics.volatility = statistics.stdev(returns) if len(returns) > 1 else 0
            
            # 샤프 비율 (무위험 수익률을 0으로 가정)
            avg_return = statistics.mean(returns)
            self.current_metrics.sharpe_ratio = avg_return / self.current_metrics.volatility if self.current_metrics.volatility > 0 else 0
            
            # VaR 95% (하위 5% 수익률)
            sorted_returns = sorted(returns)
            var_index = int(len(sorted_returns) * 0.05)
            self.current_metrics.var_95 = sorted_returns[var_index] if var_index < len(sorted_returns) else 0
            
            # 최대 낙폭 (MDD) 계산
            await self._calculate_max_drawdown()
            
            # 베타 계산 (벤치마크 대비)
            await self._calculate_beta()
        
        except Exception as e:
            self.logger.error(f"리스크 메트릭 계산 오류: {e}")
    
    async def _calculate_max_drawdown(self):
        """최대 낙폭 계산"""
        try:
            if len(self.portfolio_values) < 2:
                return
            
            values = [v[1] for v in self.portfolio_values]
            peak = values[0]
            max_dd = 0.0
            
            for value in values:
                if value > peak:
                    peak = value
                
                drawdown = (peak - value) / peak * 100
                if drawdown > max_dd:
                    max_dd = drawdown
                
                self.drawdown_history.append((datetime.now(), drawdown))
            
            self.current_metrics.max_drawdown = max_dd
        
        except Exception as e:
            self.logger.error(f"최대 낙폭 계산 오류: {e}")
    
    async def _calculate_beta(self):
        """베타 계산"""
        try:
            # 벤치마크 데이터 조회 (KOSPI)
            benchmark_returns = await self._get_benchmark_returns()
            
            if not benchmark_returns or len(self.daily_returns) < len(benchmark_returns):
                return
            
            # 공분산과 분산 계산
            portfolio_returns = list(self.daily_returns)[-len(benchmark_returns):]
            
            if len(portfolio_returns) != len(benchmark_returns):
                return
            
            # 베타 = Cov(포트폴리오, 벤치마크) / Var(벤치마크)
            import statistics
            
            portfolio_mean = statistics.mean(portfolio_returns)
            benchmark_mean = statistics.mean(benchmark_returns)
            
            covariance = sum((p - portfolio_mean) * (b - benchmark_mean) 
                           for p, b in zip(portfolio_returns, benchmark_returns)) / len(portfolio_returns)
            
            benchmark_variance = statistics.variance(benchmark_returns)
            
            self.current_metrics.beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
            
            # 상관계수 계산
            portfolio_std = statistics.stdev(portfolio_returns)
            benchmark_std = statistics.stdev(benchmark_returns)
            
            self.current_metrics.correlation_kospi = covariance / (portfolio_std * benchmark_std) if (portfolio_std * benchmark_std) > 0 else 0
        
        except Exception as e:
            self.logger.error(f"베타 계산 오류: {e}")
    
    async def _update_system_metrics(self):
        """시스템 메트릭 업데이트"""
        try:
            # CPU 사용률
            self.current_metrics.cpu_usage = psutil.cpu_percent(interval=1)
            
            # 메모리 사용률
            memory = psutil.virtual_memory()
            self.current_metrics.memory_usage = memory.percent
            
            # API 지연시간 (평균)
            if self.api_call_times:
                self.current_metrics.api_latency = sum(self.api_call_times) / len(self.api_call_times)
            
            # 에러율
            self.current_metrics.error_rate = (self.error_count / self.total_operations * 100) if self.total_operations > 0 else 0
            
            # 가동시간
            uptime = datetime.now() - self.start_time
            self.current_metrics.uptime = uptime.total_seconds() / 3600  # 시간 단위
        
        except Exception as e:
            self.logger.error(f"시스템 메트릭 업데이트 오류: {e}")
    
    async def _check_alerts(self):
        """알림 조건 확인"""
        try:
            alerts = []
            
            # 최대 낙폭 알림
            if self.current_metrics.max_drawdown > self.alert_thresholds['max_drawdown']:
                alerts.append({
                    'type': 'max_drawdown',
                    'message': f"최대 낙폭 경고: {self.current_metrics.max_drawdown:.2f}%",
                    'severity': 'high'
                })
            
            # 일일 손실 알림
            if self.current_metrics.daily_return < -self.alert_thresholds['daily_loss']:
                alerts.append({
                    'type': 'daily_loss',
                    'message': f"일일 손실 경고: {self.current_metrics.daily_return:.2f}%",
                    'severity': 'medium'
                })
            
            # 에러율 알림
            if self.current_metrics.error_rate > self.alert_thresholds['error_rate']:
                alerts.append({
                    'type': 'error_rate',
                    'message': f"에러율 경고: {self.current_metrics.error_rate:.2f}%",
                    'severity': 'medium'
                })
            
            # CPU 사용률 알림
            if self.current_metrics.cpu_usage > self.alert_thresholds['cpu_usage']:
                alerts.append({
                    'type': 'cpu_usage',
                    'message': f"CPU 사용률 경고: {self.current_metrics.cpu_usage:.1f}%",
                    'severity': 'low'
                })
            
            # 메모리 사용률 알림
            if self.current_metrics.memory_usage > self.alert_thresholds['memory_usage']:
                alerts.append({
                    'type': 'memory_usage',
                    'message': f"메모리 사용률 경고: {self.current_metrics.memory_usage:.1f}%",
                    'severity': 'low'
                })
            
            # 알림 발송
            for alert in alerts:
                await self._send_alert(alert)
        
        except Exception as e:
            self.logger.error(f"알림 확인 오류: {e}")
    
    async def _send_alert(self, alert: Dict[str, Any]):
        """알림 발송
        
        Args:
            alert: 알림 정보
        """
        try:
            # 알림 로그 기록
            self.logger.warning(f"[{alert['severity'].upper()}] {alert['message']}")
            
            # 데이터베이스에 알림 저장
            await self.db.insert_alert({
                'type': alert['type'],
                'message': alert['message'],
                'severity': alert['severity'],
                'timestamp': datetime.now(),
                'metrics': asdict(self.current_metrics)
            })
        
        except Exception as e:
            self.logger.error(f"알림 발송 오류: {e}")
    
    async def _get_portfolio_data(self) -> Dict[str, Any]:
        """포트폴리오 데이터 조회
        
        Returns:
            포트폴리오 데이터
        """
        try:
            # 포지션 데이터 조회
            positions = await self.db.get_all_positions()
            
            total_value = 0.0
            total_cost = 0.0
            total_pnl = 0.0
            
            for position in positions:
                if position.get('status') == 'open':
                    market_value = position.get('market_value', 0)
                    cost = position.get('total_cost', 0)
                    pnl = position.get('unrealized_pnl', 0)
                    
                    total_value += market_value
                    total_cost += cost
                    total_pnl += pnl
            
            # 현금 잔고 추가 (실제로는 KIS API에서 조회)
            cash_balance = self.config.get('trading.initial_capital', 10000000) - total_cost
            total_value += cash_balance
            
            return {
                'total_value': total_value,
                'total_cost': total_cost,
                'total_pnl': total_pnl,
                'cash_balance': cash_balance,
                'position_count': len([p for p in positions if p.get('status') == 'open'])
            }
        
        except Exception as e:
            self.logger.error(f"포트폴리오 데이터 조회 오류: {e}")
            return {}
    
    async def _get_recent_trades(self) -> List[Dict[str, Any]]:
        """최근 거래 데이터 조회
        
        Returns:
            최근 거래 리스트
        """
        try:
            # 최근 30일 거래 데이터 조회
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            trades = await self.db.get_trades_by_date_range(start_date, end_date)
            
            return trades
        
        except Exception as e:
            self.logger.error(f"최근 거래 조회 오류: {e}")
            return []
    
    async def _get_benchmark_returns(self) -> List[float]:
        """벤치마크 수익률 조회
        
        Returns:
            벤치마크 일일 수익률 리스트
        """
        try:
            # KOSPI 지수 데이터 조회 (실제로는 외부 API에서 조회)
            # 여기서는 더미 데이터 반환
            import random
            return [random.gauss(0, 1) for _ in range(min(30, len(self.daily_returns)))]
        
        except Exception as e:
            self.logger.error(f"벤치마크 수익률 조회 오류: {e}")
            return []
    
    async def _load_historical_data(self):
        """과거 데이터 로드"""
        try:
            # 과거 성능 메트릭 로드
            historical_metrics = await self.db.get_performance_metrics(limit=100)
            
            for metric_data in historical_metrics:
                metric = PerformanceMetrics(
                    timestamp=metric_data.get('timestamp', datetime.now()),
                    total_return=metric_data.get('total_return', 0.0),
                    daily_return=metric_data.get('daily_return', 0.0),
                    win_rate=metric_data.get('win_rate', 0.0),
                    profit_factor=metric_data.get('profit_factor', 0.0),
                    sharpe_ratio=metric_data.get('sharpe_ratio', 0.0),
                    max_drawdown=metric_data.get('max_drawdown', 0.0)
                )
                
                self.metrics_history.append(metric)
                
                # 일일 수익률 히스토리 구성
                if metric.daily_return != 0:
                    self.daily_returns.append(metric.daily_return)
            
            self.logger.info(f"과거 성능 데이터 로드 완료: {len(self.metrics_history)}개")
        
        except Exception as e:
            self.logger.error(f"과거 데이터 로드 오류: {e}")
    
    async def _save_metrics(self):
        """메트릭 저장"""
        try:
            # PerformanceMetrics를 데이터베이스 형식으로 변환
            metrics_dict = asdict(self.current_metrics)
            
            # 각 메트릭을 개별적으로 저장
            metrics_to_save = []
            for key, value in metrics_dict.items():
                if key == 'timestamp':
                    continue  # timestamp는 데이터베이스에서 자동 생성
                
                if isinstance(value, (int, float)):
                    metrics_to_save.append({
                        'metric_type': key,
                        'value': float(value),
                        'details': {'timestamp': metrics_dict['timestamp'].isoformat()}
                    })
            
            if metrics_to_save:
                await self.db.insert_performance_metrics(metrics_to_save)
        
        except Exception as e:
            self.logger.error(f"메트릭 저장 오류: {e}")
    
    def record_api_call_time(self, call_time: float):
        """API 호출 시간 기록
        
        Args:
            call_time: 호출 시간 (초)
        """
        self.api_call_times.append(call_time)
    
    def record_operation(self, success: bool = True):
        """작업 결과 기록
        
        Args:
            success: 성공 여부
        """
        self.total_operations += 1
        if not success:
            self.error_count += 1
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """현재 성능 메트릭 조회
        
        Returns:
            현재 성능 메트릭
        """
        return self.current_metrics
    
    def get_metrics_history(self, days: int = 30) -> List[PerformanceMetrics]:
        """성능 메트릭 히스토리 조회
        
        Args:
            days: 조회할 일수
        
        Returns:
            성능 메트릭 히스토리
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            return [
                metric for metric in self.metrics_history
                if metric.timestamp >= cutoff_date
            ]
        
        except Exception as e:
            self.logger.error(f"메트릭 히스토리 조회 오류: {e}")
            return []
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """성능 요약 정보
        
        Returns:
            성능 요약
        """
        try:
            metrics = self.current_metrics
            
            # 등급 계산
            performance_grade = self._calculate_performance_grade()
            
            # 개선 제안
            suggestions = self._generate_improvement_suggestions()
            
            return {
                'current_metrics': asdict(metrics),
                'performance_grade': performance_grade,
                'key_insights': {
                    'best_metric': self._get_best_metric(),
                    'worst_metric': self._get_worst_metric(),
                    'trend': self._get_performance_trend()
                },
                'suggestions': suggestions,
                'risk_assessment': {
                    'risk_level': self._assess_risk_level(),
                    'risk_factors': self._identify_risk_factors()
                }
            }
        
        except Exception as e:
            self.logger.error(f"성능 요약 생성 오류: {e}")
            return {}
    
    def _calculate_performance_grade(self) -> str:
        """성능 등급 계산
        
        Returns:
            성능 등급 (A+, A, B+, B, C+, C, D)
        """
        try:
            metrics = self.current_metrics
            score = 0
            
            # 수익률 점수 (40점)
            if metrics.total_return >= 20:
                score += 40
            elif metrics.total_return >= 10:
                score += 30
            elif metrics.total_return >= 5:
                score += 20
            elif metrics.total_return >= 0:
                score += 10
            
            # 승률 점수 (20점)
            if metrics.win_rate >= 70:
                score += 20
            elif metrics.win_rate >= 60:
                score += 15
            elif metrics.win_rate >= 50:
                score += 10
            elif metrics.win_rate >= 40:
                score += 5
            
            # 샤프 비율 점수 (20점)
            if metrics.sharpe_ratio >= 2.0:
                score += 20
            elif metrics.sharpe_ratio >= 1.5:
                score += 15
            elif metrics.sharpe_ratio >= 1.0:
                score += 10
            elif metrics.sharpe_ratio >= 0.5:
                score += 5
            
            # 최대 낙폭 점수 (20점)
            if metrics.max_drawdown <= 5:
                score += 20
            elif metrics.max_drawdown <= 10:
                score += 15
            elif metrics.max_drawdown <= 15:
                score += 10
            elif metrics.max_drawdown <= 20:
                score += 5
            
            # 등급 결정
            if score >= 90:
                return "A+"
            elif score >= 80:
                return "A"
            elif score >= 70:
                return "B+"
            elif score >= 60:
                return "B"
            elif score >= 50:
                return "C+"
            elif score >= 40:
                return "C"
            else:
                return "D"
        
        except Exception as e:
            self.logger.error(f"성능 등급 계산 오류: {e}")
            return "N/A"
    
    def _get_best_metric(self) -> str:
        """최고 성과 메트릭 식별"""
        try:
            metrics = self.current_metrics
            
            scores = {
                'win_rate': min(metrics.win_rate / 70 * 100, 100),
                'profit_factor': min(metrics.profit_factor / 2 * 100, 100),
                'sharpe_ratio': min(metrics.sharpe_ratio / 2 * 100, 100),
                'total_return': min(metrics.total_return / 20 * 100, 100)
            }
            
            return max(scores, key=scores.get)
        
        except Exception as e:
            self.logger.error(f"최고 메트릭 식별 오류: {e}")
            return "unknown"
    
    def _get_worst_metric(self) -> str:
        """최악 성과 메트릭 식별"""
        try:
            metrics = self.current_metrics
            
            scores = {
                'win_rate': min(metrics.win_rate / 70 * 100, 100),
                'profit_factor': min(metrics.profit_factor / 2 * 100, 100),
                'sharpe_ratio': min(metrics.sharpe_ratio / 2 * 100, 100),
                'max_drawdown': max(100 - metrics.max_drawdown * 5, 0)  # 낙폭은 낮을수록 좋음
            }
            
            return min(scores, key=scores.get)
        
        except Exception as e:
            self.logger.error(f"최악 메트릭 식별 오류: {e}")
            return "unknown"
    
    def _get_performance_trend(self) -> str:
        """성과 트렌드 분석"""
        try:
            if len(self.metrics_history) < 7:
                return "insufficient_data"
            
            recent_returns = [m.daily_return for m in list(self.metrics_history)[-7:]]
            
            # 최근 7일 평균과 이전 7일 평균 비교
            if len(self.metrics_history) >= 14:
                prev_returns = [m.daily_return for m in list(self.metrics_history)[-14:-7]]
                
                recent_avg = sum(recent_returns) / len(recent_returns)
                prev_avg = sum(prev_returns) / len(prev_returns)
                
                if recent_avg > prev_avg * 1.1:
                    return "improving"
                elif recent_avg < prev_avg * 0.9:
                    return "declining"
                else:
                    return "stable"
            
            return "stable"
        
        except Exception as e:
            self.logger.error(f"성과 트렌드 분석 오류: {e}")
            return "unknown"
    
    def _generate_improvement_suggestions(self) -> List[str]:
        """개선 제안 생성"""
        try:
            suggestions = []
            metrics = self.current_metrics
            
            # 승률 개선
            if metrics.win_rate < 50:
                suggestions.append("승률이 낮습니다. 진입 조건을 더 엄격하게 설정하거나 손절 기준을 조정해보세요.")
            
            # 수익 팩터 개선
            if metrics.profit_factor < 1.2:
                suggestions.append("수익 팩터가 낮습니다. 익절 타이밍을 늦추거나 손절을 빠르게 해보세요.")
            
            # 최대 낙폭 개선
            if metrics.max_drawdown > 15:
                suggestions.append("최대 낙폭이 큽니다. 포지션 크기를 줄이거나 리스크 관리를 강화하세요.")
            
            # 샤프 비율 개선
            if metrics.sharpe_ratio < 1.0:
                suggestions.append("샤프 비율이 낮습니다. 변동성을 줄이거나 수익률을 높이는 전략을 고려하세요.")
            
            # 시스템 성능
            if metrics.error_rate > 3:
                suggestions.append("에러율이 높습니다. 시스템 안정성을 점검하세요.")
            
            return suggestions
        
        except Exception as e:
            self.logger.error(f"개선 제안 생성 오류: {e}")
            return []
    
    def _assess_risk_level(self) -> str:
        """리스크 수준 평가"""
        try:
            metrics = self.current_metrics
            risk_score = 0
            
            # 변동성
            if metrics.volatility > 3:
                risk_score += 2
            elif metrics.volatility > 2:
                risk_score += 1
            
            # 최대 낙폭
            if metrics.max_drawdown > 20:
                risk_score += 3
            elif metrics.max_drawdown > 10:
                risk_score += 2
            elif metrics.max_drawdown > 5:
                risk_score += 1
            
            # VaR
            if metrics.var_95 < -5:
                risk_score += 2
            elif metrics.var_95 < -3:
                risk_score += 1
            
            # 리스크 수준 결정
            if risk_score >= 5:
                return "high"
            elif risk_score >= 3:
                return "medium"
            else:
                return "low"
        
        except Exception as e:
            self.logger.error(f"리스크 수준 평가 오류: {e}")
            return "unknown"
    
    def _identify_risk_factors(self) -> List[str]:
        """리스크 요인 식별"""
        try:
            risk_factors = []
            metrics = self.current_metrics
            
            if metrics.max_drawdown > 15:
                risk_factors.append("높은 최대 낙폭")
            
            if metrics.volatility > 3:
                risk_factors.append("높은 변동성")
            
            if metrics.var_95 < -5:
                risk_factors.append("높은 VaR")
            
            if metrics.beta > 1.5:
                risk_factors.append("높은 시장 베타")
            
            if metrics.win_rate < 40:
                risk_factors.append("낮은 승률")
            
            return risk_factors
        
        except Exception as e:
            self.logger.error(f"리스크 요인 식별 오류: {e}")
            return []
    
    async def generate_daily_report(self) -> Dict[str, Any]:
        """일일 리포트 생성
        
        Returns:
            일일 리포트
        """
        try:
            # 오늘 거래 데이터
            today = datetime.now().date()
            today_trades = await self.db.get_trades_by_date(today)
            
            # 오늘 성과
            today_pnl = sum(trade.get('pnl', 0) for trade in today_trades)
            today_trades_count = len(today_trades)
            today_win_count = len([t for t in today_trades if t.get('pnl', 0) > 0])
            
            # 포트폴리오 현황
            portfolio_data = await self._get_portfolio_data()
            
            return {
                'date': today,
                'daily_performance': {
                    'pnl': today_pnl,
                    'return_rate': self.current_metrics.daily_return,
                    'trades_count': today_trades_count,
                    'win_count': today_win_count,
                    'win_rate': (today_win_count / today_trades_count * 100) if today_trades_count > 0 else 0
                },
                'portfolio_status': portfolio_data,
                'current_metrics': asdict(self.current_metrics),
                'top_performers': await self._get_top_performing_stocks(today),
                'alerts': await self._get_today_alerts(),
                'system_status': {
                    'uptime': self.current_metrics.uptime,
                    'error_rate': self.current_metrics.error_rate,
                    'api_latency': self.current_metrics.api_latency
                }
            }
        
        except Exception as e:
            self.logger.error(f"일일 리포트 생성 오류: {e}")
            return {}
    
    async def _get_top_performing_stocks(self, date) -> List[Dict[str, Any]]:
        """상위 수익 종목 조회"""
        try:
            trades = await self.db.get_trades_by_date(date)
            
            # 종목별 수익 집계
            stock_performance = defaultdict(lambda: {'pnl': 0, 'trades': 0})
            
            for trade in trades:
                symbol = trade.get('symbol', '')
                pnl = trade.get('pnl', 0)
                
                stock_performance[symbol]['pnl'] += pnl
                stock_performance[symbol]['trades'] += 1
            
            # 수익률 순으로 정렬
            sorted_stocks = sorted(
                stock_performance.items(),
                key=lambda x: x[1]['pnl'],
                reverse=True
            )
            
            return [
                {
                    'symbol': symbol,
                    'pnl': data['pnl'],
                    'trades': data['trades']
                }
                for symbol, data in sorted_stocks[:5]
            ]
        
        except Exception as e:
            self.logger.error(f"상위 수익 종목 조회 오류: {e}")
            return []
    
    async def _get_today_alerts(self) -> List[Dict[str, Any]]:
        """오늘 알림 조회"""
        try:
            today = datetime.now().date()
            alerts = await self.db.get_alerts_by_date(today)
            
            return alerts
        
        except Exception as e:
            self.logger.error(f"오늘 알림 조회 오류: {e}")
            return []
    
    async def close(self):
        """성능 모니터 종료"""
        try:
            # 모니터링 중지
            await self.stop_monitoring()
            
            # 최종 메트릭 저장
            await self._save_metrics()
            
            self.logger.info("성능 모니터 종료 완료")
        
        except Exception as e:
            self.logger.error(f"성능 모니터 종료 오류: {e}")