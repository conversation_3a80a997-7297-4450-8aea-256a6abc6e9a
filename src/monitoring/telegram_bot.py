# -*- coding: utf-8 -*-
"""
텔레그램 봇

실시간 알림 및 모니터링 정보 전송
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager

class TelegramBot:
    """
    텔레그램 봇 클래스
    
    실시간 알림 및 모니터링 정보를 텔레그램으로 전송
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        """텔레그램 봇 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
        """
        self.config = config_manager
        self.db = db_manager
        self.logger = get_logger()
        
        # 텔레그램 설정
        self.bot_token = self.config.get_telegram_bot_token()
        self.chat_id = self.config.get_telegram_chat_id()
        self.api_url = f"https://api.telegram.org/bot{self.bot_token}"
        
        # 메시지 큐
        self.message_queue = asyncio.Queue()
        self.is_running = False
        self.sender_task = None
        
        # 알림 설정
        self.alert_levels = {
            'low': '🟡',
            'medium': '🟠', 
            'high': '🔴',
            'critical': '🚨'
        }
        
        # 메시지 템플릿
        self.templates = {
            'startup': '🚀 StockBot 시작됨\n시간: {timestamp}',
            'shutdown': '🛑 StockBot 종료됨\n시간: {timestamp}',
            'trade_executed': '💰 거래 체결\n종목: {symbol}\n구분: {side}\n수량: {quantity}주\n가격: {price:,}원\n손익: {pnl:+,.0f}원',
            'position_opened': '📈 포지션 진입\n종목: {symbol}\n수량: {quantity}주\n가격: {price:,}원\n신뢰도: {confidence:.1%}',
            'position_closed': '📉 포지션 청산\n종목: {symbol}\n수량: {quantity}주\n가격: {price:,}원\n손익: {pnl:+,.0f}원 ({pnl_rate:+.2f}%)',
            'alert': '{icon} {level} 알림\n{message}\n시간: {timestamp}',
            'daily_summary': '📊 일일 요약 ({date})\n\n💰 손익: {pnl:+,.0f}원 ({return_rate:+.2f}%)\n📈 거래: {trades}회 (승률: {win_rate:.1f}%)\n💼 포트폴리오: {portfolio_value:,.0f}원\n📍 포지션: {positions}개',
            'performance_alert': '⚠️ 성능 알림\n{metric}: {value}\n임계값: {threshold}\n조치 필요: {action}',
            'system_status': '🖥️ 시스템 상태\nCPU: {cpu:.1f}%\n메모리: {memory:.1f}%\n가동시간: {uptime:.1f}시간\n에러율: {error_rate:.2f}%'
        }
        
        # 메시지 제한
        self.max_messages_per_minute = 20
        self.message_timestamps = []
        
        # 요약 발송 시간
        self.daily_summary_time = self.config.get('telegram.daily_summary_time', '18:00')
        self.last_summary_date = None
    
    async def initialize(self):
        """텔레그램 봇 초기화"""
        try:
            # 봇 정보 확인
            if not await self._verify_bot():
                raise Exception("텔레그램 봇 인증 실패")
            
            # 메시지 발송 태스크 시작
            await self.start()
            
            # 시작 메시지 발송
            await self.send_startup_message()
            
            self.logger.info("텔레그램 봇 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"텔레그램 봇 초기화 오류: {e}")
    
    async def start(self):
        """메시지 발송 시작"""
        if self.is_running:
            return
        
        self.is_running = True
        self.sender_task = asyncio.create_task(self._message_sender_loop())
        self.logger.info("텔레그램 메시지 발송 시작")
    
    async def stop(self):
        """메시지 발송 중지"""
        self.is_running = False
        
        if self.sender_task:
            self.sender_task.cancel()
            try:
                await self.sender_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("텔레그램 메시지 발송 중지")
    
    async def _verify_bot(self) -> bool:
        """봇 인증 확인
        
        Returns:
            인증 성공 여부
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/getMe"
                
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            bot_info = data.get('result', {})
                            self.logger.info(f"텔레그램 봇 인증 성공: {bot_info.get('username')}")
                            return True
            
            return False
        
        except Exception as e:
            self.logger.error(f"봇 인증 확인 오류: {e}")
            return False
    
    async def _message_sender_loop(self):
        """메시지 발송 루프"""
        while self.is_running:
            try:
                # 메시지 큐에서 메시지 가져오기
                try:
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    # 일일 요약 발송 확인
                    await self._check_daily_summary()
                    continue
                
                # 메시지 발송 제한 확인
                if not self._check_rate_limit():
                    # 1분 후 재시도
                    await asyncio.sleep(60)
                    await self.message_queue.put(message)
                    continue
                
                # 메시지 발송
                await self._send_message(message)
                
                # 발송 완료 표시
                self.message_queue.task_done()
                
                # 발송 간격 (API 제한 고려)
                await asyncio.sleep(0.1)
            
            except Exception as e:
                self.logger.error(f"메시지 발송 루프 오류: {e}")
                await asyncio.sleep(5)
    
    def _check_rate_limit(self) -> bool:
        """메시지 발송 제한 확인
        
        Returns:
            발송 가능 여부
        """
        try:
            now = datetime.now()
            
            # 1분 이전 타임스탬프 제거
            self.message_timestamps = [
                ts for ts in self.message_timestamps
                if (now - ts).total_seconds() < 60
            ]
            
            # 제한 확인
            if len(self.message_timestamps) >= self.max_messages_per_minute:
                return False
            
            # 현재 시간 추가
            self.message_timestamps.append(now)
            return True
        
        except Exception as e:
            self.logger.error(f"발송 제한 확인 오류: {e}")
            return True
    
    async def _send_message(self, message: str):
        """메시지 발송
        
        Args:
            message: 발송할 메시지
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/sendMessage"
                
                payload = {
                    'chat_id': self.chat_id,
                    'text': message,
                    'parse_mode': 'HTML',
                    'disable_web_page_preview': True
                }
                
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            self.logger.debug(f"메시지 발송 성공: {message[:50]}...")
                        else:
                            self.logger.error(f"메시지 발송 실패: {data.get('description')}")
                    else:
                        self.logger.error(f"HTTP 오류: {response.status}")
        
        except Exception as e:
            self.logger.error(f"메시지 발송 오류: {e}")
    
    async def _check_daily_summary(self):
        """일일 요약 발송 확인"""
        try:
            now = datetime.now()
            today = now.date()
            
            # 이미 오늘 요약을 보냈는지 확인
            if self.last_summary_date == today:
                return
            
            # 요약 발송 시간 확인
            summary_hour, summary_minute = map(int, self.daily_summary_time.split(':'))
            summary_time = now.replace(hour=summary_hour, minute=summary_minute, second=0).replace(microsecond=0)
            
            if now >= summary_time:
                await self.send_daily_summary()
                self.last_summary_date = today
        
        except Exception as e:
            self.logger.error(f"일일 요약 확인 오류: {e}")
    
    async def send_startup_message(self):
        """시작 메시지 발송"""
        try:
            message = self.templates['startup'].format(
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"시작 메시지 발송 오류: {e}")
    
    async def send_shutdown_message(self):
        """종료 메시지 발송"""
        try:
            message = self.templates['shutdown'].format(
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            await self.message_queue.put(message)
            
            # 메시지 발송 완료까지 대기
            await self.message_queue.join()
        
        except Exception as e:
            self.logger.error(f"종료 메시지 발송 오류: {e}")
    
    async def send_trade_notification(self, trade_data: Dict[str, Any]):
        """거래 알림 발송
        
        Args:
            trade_data: 거래 데이터
        """
        try:
            if trade_data.get('side') == 'buy':
                # 포지션 진입
                message = self.templates['position_opened'].format(
                    symbol=trade_data.get('symbol', ''),
                    quantity=trade_data.get('quantity', 0),
                    price=trade_data.get('price', 0),
                    confidence=trade_data.get('confidence', 0)
                )
            else:
                # 포지션 청산
                message = self.templates['position_closed'].format(
                    symbol=trade_data.get('symbol', ''),
                    quantity=trade_data.get('quantity', 0),
                    price=trade_data.get('price', 0),
                    pnl=trade_data.get('pnl', 0),
                    pnl_rate=trade_data.get('pnl_rate', 0)
                )
            
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"거래 알림 발송 오류: {e}")
    
    async def send_alert(self, alert_data: Dict[str, Any]):
        """알림 발송
        
        Args:
            alert_data: 알림 데이터
        """
        try:
            level = alert_data.get('severity', 'low')
            icon = self.alert_levels.get(level, '⚠️')
            
            message = self.templates['alert'].format(
                icon=icon,
                level=level.upper(),
                message=alert_data.get('message', ''),
                timestamp=datetime.now().strftime('%H:%M:%S')
            )
            
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"알림 발송 오류: {e}")
    
    async def send_performance_alert(self, metric: str, value: float, threshold: float, action: str):
        """성능 알림 발송
        
        Args:
            metric: 메트릭 이름
            value: 현재 값
            threshold: 임계값
            action: 권장 조치
        """
        try:
            message = self.templates['performance_alert'].format(
                metric=metric,
                value=value,
                threshold=threshold,
                action=action
            )
            
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"성능 알림 발송 오류: {e}")
    
    async def send_system_status(self, status_data: Dict[str, Any]):
        """시스템 상태 발송
        
        Args:
            status_data: 시스템 상태 데이터
        """
        try:
            message = self.templates['system_status'].format(
                cpu=status_data.get('cpu_usage', 0),
                memory=status_data.get('memory_usage', 0),
                uptime=status_data.get('uptime', 0),
                error_rate=status_data.get('error_rate', 0)
            )
            
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"시스템 상태 발송 오류: {e}")
    
    async def send_daily_summary(self):
        """일일 요약 발송"""
        try:
            # 오늘 거래 데이터 조회
            today = datetime.now()
            summary_data = await self._get_daily_summary_data(today)
            
            if not summary_data:
                return
            
            message = self.templates['daily_summary'].format(
                date=today.date().strftime('%Y-%m-%d'),
                pnl=summary_data.get('pnl', 0),
                return_rate=summary_data.get('return_rate', 0),
                trades=summary_data.get('trades', 0),
                win_rate=summary_data.get('win_rate', 0),
                portfolio_value=summary_data.get('portfolio_value', 0),
                positions=summary_data.get('positions', 0)
            )
            
            await self.message_queue.put(message)
            
            # 상세 정보 추가 발송
            await self._send_detailed_summary(summary_data)
        
        except Exception as e:
            self.logger.error(f"일일 요약 발송 오류: {e}")
    
    async def _get_daily_summary_data(self, date) -> Dict[str, Any]:
        """일일 요약 데이터 조회
        
        Args:
            date: 조회 날짜
        
        Returns:
            요약 데이터
        """
        try:
            # 오늘 거래 조회
            trades = await self.db.get_trades_by_date(date)
            
            # 포트폴리오 현황 조회
            positions = await self.db.get_all_positions()
            active_positions = [p for p in positions if p.get('status') == 'open']
            
            # 계산
            total_pnl = sum(trade.get('pnl', 0) for trade in trades)
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.get('pnl', 0) > 0])
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            # 포트폴리오 가치
            portfolio_value = sum(pos.get('market_value', 0) for pos in active_positions)
            
            # 수익률 계산 (초기 자본 대비)
            initial_capital = self.config.get('trading.initial_capital', 10000000)
            return_rate = (total_pnl / initial_capital * 100) if initial_capital > 0 else 0
            
            return {
                'pnl': total_pnl,
                'return_rate': return_rate,
                'trades': total_trades,
                'win_rate': win_rate,
                'portfolio_value': portfolio_value,
                'positions': len(active_positions),
                'trades_detail': trades,
                'positions_detail': active_positions
            }
        
        except Exception as e:
            self.logger.error(f"일일 요약 데이터 조회 오류: {e}")
            return {}
    
    async def _send_detailed_summary(self, summary_data: Dict[str, Any]):
        """상세 요약 발송
        
        Args:
            summary_data: 요약 데이터
        """
        try:
            # 상위 수익 종목
            trades = summary_data.get('trades_detail', [])
            if trades:
                # 종목별 수익 집계
                from collections import defaultdict
                stock_pnl = defaultdict(float)
                
                for trade in trades:
                    symbol = trade.get('symbol', '')
                    pnl = trade.get('pnl', 0)
                    stock_pnl[symbol] += pnl
                
                # 상위 3개 종목
                top_stocks = sorted(stock_pnl.items(), key=lambda x: x[1], reverse=True)[:3]
                
                if top_stocks:
                    top_message = "📈 상위 수익 종목:\n"
                    for i, (symbol, pnl) in enumerate(top_stocks, 1):
                        top_message += f"{i}. {symbol}: {pnl:+,.0f}원\n"
                    
                    await self.message_queue.put(top_message)
            
            # 현재 포지션
            positions = summary_data.get('positions_detail', [])
            if positions:
                pos_message = "💼 현재 포지션:\n"
                
                for pos in positions[:5]:  # 상위 5개만
                    symbol = pos.get('symbol', '')
                    quantity = pos.get('quantity', 0)
                    pnl = pos.get('unrealized_pnl', 0)
                    pnl_rate = pos.get('unrealized_pnl_rate', 0)
                    
                    pos_message += f"• {symbol}: {quantity}주 ({pnl:+,.0f}원, {pnl_rate:+.2f}%)\n"
                
                await self.message_queue.put(pos_message)
        
        except Exception as e:
            self.logger.error(f"상세 요약 발송 오류: {e}")
    
    async def send_custom_message(self, message: str):
        """사용자 정의 메시지 발송
        
        Args:
            message: 발송할 메시지
        """
        try:
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"사용자 정의 메시지 발송 오류: {e}")
    
    async def send_market_analysis(self, analysis_data: Dict[str, Any]):
        """시장 분석 발송
        
        Args:
            analysis_data: 분석 데이터
        """
        try:
            message = "📊 시장 분석\n\n"
            
            # 시장 상황
            market_condition = analysis_data.get('market_condition', '')
            if market_condition:
                message += f"📈 시장 상황: {market_condition}\n"
            
            # 추천 종목
            recommendations = analysis_data.get('recommendations', [])
            if recommendations:
                message += "\n🎯 추천 종목:\n"
                for rec in recommendations[:3]:
                    symbol = rec.get('symbol', '')
                    reason = rec.get('reason', '')
                    confidence = rec.get('confidence', 0)
                    
                    message += f"• {symbol} (신뢰도: {confidence:.1%})\n  {reason}\n"
            
            # 리스크 요인
            risk_factors = analysis_data.get('risk_factors', [])
            if risk_factors:
                message += "\n⚠️ 리스크 요인:\n"
                for risk in risk_factors[:3]:
                    message += f"• {risk}\n"
            
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"시장 분석 발송 오류: {e}")
    
    async def send_emergency_alert(self, emergency_data: Dict[str, Any]):
        """긴급 알림 발송
        
        Args:
            emergency_data: 긴급 상황 데이터
        """
        try:
            message = "🚨 긴급 알림 🚨\n\n"
            
            emergency_type = emergency_data.get('type', '')
            description = emergency_data.get('description', '')
            action_taken = emergency_data.get('action_taken', '')
            
            message += f"유형: {emergency_type}\n"
            message += f"상황: {description}\n"
            
            if action_taken:
                message += f"조치: {action_taken}\n"
            
            message += f"\n시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 긴급 메시지는 우선 발송
            await self.message_queue.put(message)
        
        except Exception as e:
            self.logger.error(f"긴급 알림 발송 오류: {e}")
    
    def get_queue_size(self) -> int:
        """메시지 큐 크기 조회
        
        Returns:
            큐에 대기 중인 메시지 수
        """
        return self.message_queue.qsize()
    
    def is_connected(self) -> bool:
        """연결 상태 확인
        
        Returns:
            연결 상태
        """
        return self.is_running and bool(self.bot_token) and bool(self.chat_id)
    
    async def test_connection(self) -> bool:
        """연결 테스트
        
        Returns:
            연결 테스트 결과
        """
        try:
            test_message = f"🔧 연결 테스트\n시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            await self.send_custom_message(test_message)
            
            return True
        
        except Exception as e:
            self.logger.error(f"연결 테스트 오류: {e}")
            return False
    
    async def get_chat_info(self) -> Dict[str, Any]:
        """채팅 정보 조회
        
        Returns:
            채팅 정보
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/getChat"
                
                payload = {'chat_id': self.chat_id}
                
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            return data.get('result', {})
            
            return {}
        
        except Exception as e:
            self.logger.error(f"채팅 정보 조회 오류: {e}")
            return {}
    
    def update_templates(self, new_templates: Dict[str, str]):
        """메시지 템플릿 업데이트
        
        Args:
            new_templates: 새로운 템플릿 딕셔너리
        """
        try:
            self.templates.update(new_templates)
            self.logger.info(f"메시지 템플릿 업데이트: {len(new_templates)}개")
        
        except Exception as e:
            self.logger.error(f"템플릿 업데이트 오류: {e}")
    
    def set_alert_thresholds(self, thresholds: Dict[str, float]):
        """알림 임계값 설정
        
        Args:
            thresholds: 임계값 딕셔너리
        """
        try:
            # 기존 임계값과 병합
            for key, value in thresholds.items():
                if key in ['max_drawdown', 'daily_loss', 'error_rate', 'cpu_usage', 'memory_usage']:
                    self.alert_thresholds[key] = value
            
            self.logger.info(f"알림 임계값 업데이트: {thresholds}")
        
        except Exception as e:
            self.logger.error(f"임계값 설정 오류: {e}")
    
    async def close(self):
        """텔레그램 봇 종료"""
        try:
            # 종료 메시지 발송
            await self.send_shutdown_message()
            
            # 메시지 발송 중지
            await self.stop()
            
            self.logger.info("텔레그램 봇 종료 완료")
        
        except Exception as e:
            self.logger.error(f"텔레그램 봇 종료 오류: {e}")