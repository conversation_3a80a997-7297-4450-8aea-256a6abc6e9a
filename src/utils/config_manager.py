# -*- coding: utf-8 -*-
"""
설정 관리자

YAML 설정 파일을 로드하고 관리하는 모듈
"""

import os
import yaml
from pathlib import Path
from typing import Any, Dict
from dotenv import load_dotenv

class ConfigManager:
    """
    설정 관리자 클래스
    """
    
    def __init__(self, config_path: str = None):
        """설정 관리자 초기화
        
        Args:
            config_path: 설정 파일 경로 (기본값: config.yaml)
        """
        # 프로젝트 루트 경로 설정
        self.project_root = Path(__file__).parent.parent.parent
        
        # .env 파일 로드
        env_path = self.project_root / ".env"
        if env_path.exists():
            load_dotenv(env_path)
        
        if config_path is None:
            # 프로젝트 루트의 config.yaml 사용
            config_path = self.project_root / "config.yaml"
        
        self.config_path = Path(config_path)
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """설정 파일 로드
        
        Returns:
            설정 딕셔너리
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 환경 변수로 덮어쓰기
            self._override_with_env_vars(config)
            
            return config
            
        except FileNotFoundError:
            raise FileNotFoundError(f"설정 파일을 찾을 수 없습니다: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"설정 파일 파싱 오류: {e}")
    
    def _override_with_env_vars(self, config: Dict[str, Any]):
        """환경 변수로 설정 덮어쓰기
        
        Args:
            config: 설정 딕셔너리
        """
        # KIS API 설정
        if 'kis' in config:
            # 실제 거래 계좌 설정
            if 'real' in config['kis']:
                config['kis']['real']['app_key'] = os.getenv('KIS_REAL_APP_KEY', config['kis']['real']['app_key'])
                config['kis']['real']['app_secret'] = os.getenv('KIS_REAL_APP_SECRET', config['kis']['real']['app_secret'])
                config['kis']['real']['account_number'] = os.getenv('KIS_REAL_ACCOUNT_NUMBER', config['kis']['real']['account_number'])
            
            # 모의 거래 계좌 설정
            if 'demo' in config['kis']:
                config['kis']['demo']['app_key'] = os.getenv('KIS_DEMO_APP_KEY', config['kis']['demo']['app_key'])
                config['kis']['demo']['app_secret'] = os.getenv('KIS_DEMO_APP_SECRET', config['kis']['demo']['app_secret'])
                config['kis']['demo']['account_number'] = os.getenv('KIS_DEMO_ACCOUNT_NUMBER', config['kis']['demo']['account_number'])
        
        # OpenAI API 설정
        if 'ai' in config:
            config['ai']['openai_api_key'] = os.getenv('OPENAI_API_KEY', config['ai']['openai_api_key'])
        
        # 텔레그램 설정
        if 'telegram' in config:
            config['telegram']['bot_token'] = os.getenv('TELEGRAM_BOT_TOKEN', config['telegram']['bot_token'])
            config['telegram']['chat_id'] = os.getenv('TELEGRAM_CHAT_ID', config['telegram']['chat_id'])
        
        # DART API 설정
        if 'data_collection' in config and 'dart_api_key' in config['data_collection']:
            config['data_collection']['dart_api_key'] = os.getenv('DART_API_KEY', config['data_collection']['dart_api_key'])
    
    def get(self, key: str, default: Any = None) -> Any:
        """설정 값 조회
        
        Args:
            key: 설정 키 (점으로 구분된 중첩 키 지원, 예: 'kis.app_key')
            default: 기본값
        
        Returns:
            설정 값
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """설정 값 설정
        
        Args:
            key: 설정 키 (점으로 구분된 중첩 키 지원)
            value: 설정 값
        """
        keys = key.split('.')
        config = self._config
        
        # 중첩 딕셔너리 생성
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_kis_config(self) -> Dict[str, Any]:
        """KIS API 설정 조회
        
        Returns:
            KIS API 설정 딕셔너리
        """
        return self.get('kis', {})
    
    def get_kis_app_key(self) -> str:
        """KIS API 앱 키 조회 (환경에 따라 실제/모의 계좌)
        
        Returns:
            KIS API 앱 키
        """
        environment = self.get('trading.environment', 'demo')
        if environment == 'demo':
            return self.get('kis.demo.app_key', '')
        else:
            return self.get('kis.real.app_key', '')
    
    def get_kis_app_secret(self) -> str:
        """KIS API 앱 시크릿 조회 (환경에 따라 실제/모의 계좌)
        
        Returns:
            KIS API 앱 시크릿
        """
        environment = self.get('trading.environment', 'demo')
        if environment == 'demo':
            return self.get('kis.demo.app_secret', '')
        else:
            return self.get('kis.real.app_secret', '')
    
    def get_kis_account_no(self) -> str:
        """KIS 계좌번호 조회 (환경에 따라 실제/모의 계좌)
        
        Returns:
            KIS 계좌번호
        """
        environment = self.get('trading.environment', 'demo')
        if environment == 'demo':
            return self.get('kis.demo.account_number', '')
        else:
            return self.get('kis.real.account_number', '')
    
    def get_kis_account_product_cd(self) -> str:
        """KIS 계좌상품코드 조회 (환경에 따라 실제/모의 계좌)
        
        Returns:
            KIS 계좌상품코드
        """
        environment = self.get('trading.environment', 'demo')
        if environment == 'demo':
            return self.get('kis.demo.account_product_code', '01')
        else:
            return self.get('kis.real.account_product_code', '01')
    
    def get_kis_api_url(self) -> str:
        """KIS API URL 조회 (환경에 따라 실제/모의 서버)
        
        Returns:
            KIS API URL
        """
        environment = self.get('trading.environment', 'demo')
        if environment == 'demo':
            return self.get('kis.endpoints.demo.api_url', 'https://openapivts.koreainvestment.com:29443')
        else:
            return self.get('kis.endpoints.real.api_url', 'https://openapi.koreainvestment.com:9443')
    
    def get_kis_websocket_url(self) -> str:
        """KIS WebSocket URL 조회 (환경에 따라 실제/모의 서버)
        
        Returns:
            KIS WebSocket URL
        """
        environment = self.get('trading.environment', 'demo')
        if environment == 'demo':
            return self.get('kis.endpoints.demo.websocket_url', 'ws://ops.koreainvestment.com:31000')
        else:
            return self.get('kis.endpoints.real.websocket_url', 'ws://ops.koreainvestment.com:21000')
    
    def get_openai_config(self) -> Dict[str, Any]:
        """OpenAI API 설정 조회
        
        Returns:
            OpenAI API 설정 딕셔너리
        """
        # ai 섹션에서 OpenAI 관련 설정만 추출
        ai_config = self.get('ai', {})
        return {
            'api_key': ai_config.get('openai_api_key', ''),
            'model': ai_config.get('model', 'gpt-4o-mini'),
            'max_tokens': ai_config.get('max_tokens', 2000),
            'temperature': ai_config.get('temperature', 0.2),
            'timeout': ai_config.get('timeout', 30.0)
        }
    
    def get_openai_api_key(self) -> str:
        """OpenAI API 키 조회
        
        Returns:
            OpenAI API 키
        """
        return self.get('ai.openai_api_key', '')
    
    def get_telegram_config(self) -> Dict[str, Any]:
        """텔레그램 설정 조회
        
        Returns:
            텔레그램 설정 딕셔너리
        """
        return self.get('telegram', {})
    
    def get_telegram_bot_token(self) -> str:
        """텔레그램 봇 토큰 조회
        
        Returns:
            텔레그램 봇 토큰
        """
        return self.get('telegram.bot_token', '')
    
    def get_telegram_chat_id(self) -> str:
        """텔레그램 채팅 ID 조회
        
        Returns:
            텔레그램 채팅 ID
        """
        return self.get('telegram.chat_id', '')
    
    def get_trading_config(self) -> Dict[str, Any]:
        """매매 설정 조회
        
        Returns:
            매매 설정 딕셔너리
        """
        return self.get('trading', {})
    
    def get_data_collection_config(self) -> Dict[str, Any]:
        """데이터 수집 설정 조회
        
        Returns:
            데이터 수집 설정 딕셔너리
        """
        return self.get('data_collection', {})
    
    def is_demo_mode(self) -> bool:
        """데모 모드 여부 확인
        
        Returns:
            데모 모드 여부
        """
        return self.get('trading.environment', 'demo') == 'demo'
    
    def get_performance_config(self) -> Dict[str, Any]:
        """성능 설정 조회
        
        Returns:
            성능 설정 딕셔너리
        """
        return self.get('performance', {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """보안 설정 조회
        
        Returns:
            보안 설정 딕셔너리
        """
        return self.get('security', {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """모니터링 설정 조회
        
        Returns:
            모니터링 설정 딕셔너리
        """
        return self.get('monitoring', {})
    
    def get_development_config(self) -> Dict[str, Any]:
        """개발/테스트 설정 조회
        
        Returns:
            개발/테스트 설정 딕셔너리
        """
        return self.get('development', {})
    
    def is_debug_mode(self) -> bool:
        """디버그 모드 여부 확인
        
        Returns:
            디버그 모드 여부
        """
        return self.get('development.debug_mode', False)
    
    def is_test_mode(self) -> bool:
        """테스트 모드 여부 확인
        
        Returns:
            테스트 모드 여부
        """
        return self.get('development.test_mode', False)
    
    def is_simulation_mode(self) -> bool:
        """시뮬레이션 모드 여부 확인
        
        Returns:
            시뮬레이션 모드 여부
        """
        return self.get('development.simulation_mode', False)
    
    def get_api_base_url(self) -> str:
        """API 기본 URL 조회 (get_kis_api_url과 동일)
        
        Returns:
            API 기본 URL
        """
        return self.get_kis_api_url()
    
    def get_websocket_url(self) -> str:
        """웹소켓 URL 조회 (get_kis_websocket_url과 동일)
        
        Returns:
            웹소켓 URL
        """
        return self.get_kis_websocket_url()
    
    def save_config(self):
        """설정을 파일에 저장"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            raise IOError(f"설정 파일 저장 오류: {e}")
    
    def reload_config(self):
        """설정 파일 다시 로드"""
        self._config = self._load_config()
    

    

    
    @property
    def config(self) -> Dict[str, Any]:
        """전체 설정 딕셔너리 반환"""
        return self._config.copy()