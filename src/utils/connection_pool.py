# -*- coding: utf-8 -*-
"""
SQLite 연결 풀 관리자

데이터베이스 연결을 효율적으로 관리하기 위한 연결 풀 구현
"""

import asyncio
import aiosqlite
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager
from .logger import get_logger

class DatabaseConnectionPool:
    """
    SQLite 연결 풀 관리 클래스
    
    여러 연결을 효율적으로 관리하여 데이터베이스 락 문제를 최소화하고
    성능을 향상시키는 연결 풀 구현
    """
    
    def __init__(self, db_path: str, pool_size: int = 10, max_overflow: int = 5):
        """
        연결 풀 초기화
        
        Args:
            db_path: 데이터베이스 파일 경로
            pool_size: 기본 연결 풀 크기
            max_overflow: 최대 추가 연결 수
        """
        self.db_path = db_path
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.logger = get_logger()
        
        # 연결 풀 및 관리 변수 초기화
        self._pool: List[aiosqlite.Connection] = []
        self._used: Dict[aiosqlite.Connection, bool] = {}
        self._overflow = 0
        self._initialized = False
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """
        연결 풀 초기화 - 기본 연결들을 미리 생성
        """
        if self._initialized:
            return
            
        async with self._lock:
            if self._initialized:
                return
                
            self.logger.info(f"SQLite 연결 풀 초기화 시작 (크기: {self.pool_size})")
            
            # 기본 연결 풀 생성
            for _ in range(self.pool_size):
                conn = await self._create_connection()
                self._pool.append(conn)
                self._used[conn] = False
            
            self._initialized = True
            self.logger.info(f"SQLite 연결 풀 초기화 완료 (크기: {len(self._pool)})")
    
    async def _create_connection(self) -> aiosqlite.Connection:
        """
        새로운 데이터베이스 연결 생성
        
        Returns:
            새로운 SQLite 연결
        """
        conn = await aiosqlite.connect(self.db_path)
        conn.row_factory = aiosqlite.Row
        
        # 성능 및 안정성 최적화 설정
        await conn.execute("PRAGMA journal_mode=WAL")
        await conn.execute("PRAGMA synchronous=NORMAL")
        await conn.execute("PRAGMA cache_size=10000")
        await conn.execute("PRAGMA temp_store=memory")
        await conn.execute("PRAGMA busy_timeout=30000")  # 30초 타임아웃
        await conn.execute("PRAGMA wal_autocheckpoint=1000")  # 1000 프레임마다 체크포인트
        await conn.execute("PRAGMA journal_size_limit=67108864")  # 64MB WAL 파일 크기 제한
        
        return conn
    
    @asynccontextmanager
    async def get_connection(self):
        """
        연결 풀에서 사용 가능한 연결 획득
        
        Returns:
            SQLite 연결 (컨텍스트 매니저)
        """
        conn = None
        created = False
        
        # 연결 획득
        async with self._lock:
            # 1. 풀에서 사용 가능한 연결 찾기
            for existing_conn in self._pool:
                if not self._used[existing_conn]:
                    conn = existing_conn
                    self._used[conn] = True
                    break
            
            # 2. 사용 가능한 연결이 없으면 오버플로우 연결 생성
            if conn is None and self._overflow < self.max_overflow:
                conn = await self._create_connection()
                self._used[conn] = True
                self._overflow += 1
                created = True
                self.logger.debug(f"오버플로우 연결 생성 (현재: {self._overflow}/{self.max_overflow})")
        
        if conn is None:
            # 모든 연결이 사용 중이고 오버플로우도 최대치인 경우
            self.logger.warning("모든 연결이 사용 중입니다. 연결이 반환될 때까지 대기합니다.")
            
            # 연결이 반환될 때까지 대기
            while conn is None:
                await asyncio.sleep(0.1)  # 짧은 대기 후 재시도
                
                async with self._lock:
                    for existing_conn in self._pool:
                        if not self._used[existing_conn]:
                            conn = existing_conn
                            self._used[conn] = True
                            break
        
        try:
            # 연결 반환
            yield conn
        finally:
            # 연결 반환 처리
            async with self._lock:
                if conn in self._used:
                    # 오버플로우 연결이면 닫고 제거
                    if created and self._overflow > 0:
                        await conn.close()
                        del self._used[conn]
                        self._overflow -= 1
                    else:
                        # 기본 풀 연결이면 재사용 표시
                        self._used[conn] = False
    
    async def close_all(self):
        """
        모든 연결 종료
        """
        async with self._lock:
            # 모든 연결 종료
            for conn in list(self._used.keys()):
                try:
                    await conn.close()
                except Exception as e:
                    self.logger.error(f"연결 종료 오류: {e}")
            
            # 변수 초기화
            self._pool = []
            self._used = {}
            self._overflow = 0
            self._initialized = False
            
            self.logger.info("모든 데이터베이스 연결이 종료되었습니다.")