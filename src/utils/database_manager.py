# -*- coding: utf-8 -*-
"""
데이터베이스 관리자

SQLite 기반 고성능 데이터베이스 관리 시스템
"""

import sqlite3
import asyncio
import aiosqlite
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import json
import threading
from contextlib import asynccontextmanager
import weakref
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

from .logger import get_logger
from .connection_pool import DatabaseConnectionPool


class DatabaseManager:
    """
    데이터베이스 관리자 클래스 (싱글톤 패턴)
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_path: str = "data/stockbot.db", config_manager=None):
        """싱글톤 패턴 구현"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, db_path: str = "data/stockbot.db", config_manager=None):
        """데이터베이스 관리자 초기화 (싱글톤)
        
        Args:
            db_path: 데이터베이스 파일 경로
            config_manager: 설정 관리자
        """
        # 이미 초기화된 경우 스킵
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self.db_path = Path(db_path)
        self.logger = get_logger()
        self.config = config_manager
        
        # 데이터베이스 설정
        if self.config:
            db_config = self.config.get('database', {})
            self.backup_interval = db_config.get('backup_interval', 3600)  # 1시간
            pool_size = db_config.get('pool_size', 10)
            max_overflow = db_config.get('max_overflow', 5)
            self.pool_timeout = db_config.get('pool_timeout', 30)
            self.pool_recycle = db_config.get('pool_recycle', 3600)
            
            # 백업 설정
            backup_config = db_config.get('backup', {})
            self.backup_enabled = backup_config.get('enabled', True)
            self.backup_interval_hours = backup_config.get('interval_hours', 6)
            self.backup_retention_days = backup_config.get('retention_days', 30)
            self.backup_path = Path(backup_config.get('path', 'data/backups'))
        else:
            # 기본값
            self.backup_interval = 3600
            pool_size = 10
            max_overflow = 5
            self.backup_enabled = True
            self.backup_interval_hours = 6
            self.backup_retention_days = 30
            self.backup_path = Path('data/backups')
        
        # 데이터베이스 디렉토리 생성
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        if self.backup_enabled:
            self.backup_path.mkdir(parents=True, exist_ok=True)
        
        # 연결 풀 초기화
        self._connection_pool = DatabaseConnectionPool(
            str(self.db_path), 
            pool_size=pool_size, 
            max_overflow=max_overflow
        )
        
        self._initialized = True
        self.logger.info(f"데이터베이스 매니저 초기화 완료: {self.db_path}")
    
    async def initialize(self):
        """데이터베이스 초기화"""
        self.logger.info("데이터베이스 초기화 시작")
        
        # 연결 풀 초기화
        await self._connection_pool.initialize()
        
        # 테이블 생성 및 마이그레이션
        async with self._connection_pool.get_connection() as db:
            # 테이블 생성
            await self._create_tables(db)
            
            # 데이터베이스 마이그레이션
            await self._migrate_database(db)
            
            await db.commit()
        
        self.logger.info("데이터베이스 초기화 완료")
    
    async def check_title_duplicate(self, title: str) -> bool:
        """제목 완전 일치 중복 검사
        
        Args:
            title: 검사할 기사 제목
            
        Returns:
            bool: 동일한 제목의 기사가 있으면 True
        """
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    "SELECT COUNT(*) FROM news_data WHERE title = ?", 
                    (title,)
                )
                count = (await cursor.fetchone())[0]
                return count > 0
                
        except Exception as e:
            self.logger.error(f"제목 중복 검사 중 오류 발생: {e}")
            # 오류 발생 시 안전하게 중복이 아님으로 처리
            return False
    
    async def is_news_duplicate(self, url: str) -> bool:
        """뉴스 중복 확인 (URL 기반)
        
        Args:
            url: 기사 URL
            
        Returns:
            중복 여부
        """
        async with self.get_connection() as db:
            cursor = await db.execute(
                "SELECT COUNT(*) FROM news_data WHERE url = ?", 
                (url,)
            )
            count = await cursor.fetchone()
            return count[0] > 0 if count else False
    
    async def check_news_duplicates_batch(self, urls: List[str]) -> List[bool]:
        """뉴스 중복 확인 (배치 처리)
        
        Args:
            urls: URL 리스트
            
        Returns:
            각 URL의 중복 여부 리스트
        """
        if not urls:
            return []
            
        async with self.get_connection() as db:
            # IN 절을 사용한 배치 쿼리
            placeholders = ','.join('?' * len(urls))
            cursor = await db.execute(
                f"SELECT url FROM news_data WHERE url IN ({placeholders})", 
                urls
            )
            existing_urls = {row[0] for row in await cursor.fetchall()}
            
            # 각 URL의 중복 여부 반환
            return [url in existing_urls for url in urls]
    
    def preprocess_text(self, text: str) -> str:
        """텍스트 전처리 (유사도 계산용)
        
        Args:
            text: 원본 텍스트
            
        Returns:
            전처리된 텍스트
        """
        if not text:
            return ""
        
        # HTML 태그 제거
        text = re.sub(r'<[^>]+>', '', text)
        # 특수문자 제거 (한글, 영문, 숫자, 공백만 유지)
        text = re.sub(r'[^가-힣a-zA-Z0-9\s]', ' ', text)
        # 연속된 공백을 하나로 변환
        text = re.sub(r'\s+', ' ', text)
        # 앞뒤 공백 제거
        text = text.strip()
        
        return text
    

    
    def get_vectorizer(self, vectorizer_type: str = 'similarity') -> TfidfVectorizer:
        """TF-IDF 벡터화기 생성 (공통 설정)
        
        Args:
            vectorizer_type: 벡터화기 타입 ('similarity' 또는 'batch')
            
        Returns:
            TfidfVectorizer: 설정된 벡터화기
        """
        if vectorizer_type == 'batch':
            # 배치 처리용 설정 (더 엄격한 설정)
            return TfidfVectorizer(
                max_features=1000,  # 최대 특성 수 제한
                stop_words=None,    # 한글 불용어 처리는 별도로
                ngram_range=(1, 2), # 1-gram과 2-gram 사용
                min_df=1,           # 최소 문서 빈도
                max_df=0.95         # 최대 문서 빈도 (너무 흔한 단어 제외)
            )
        else:
            # 일반 유사도 계산용 설정 (더 관대한 설정)
            return TfidfVectorizer(
                max_features=None,  # 특성 수 제한 제거
                stop_words=None,    # 한글 불용어 처리는 별도로
                ngram_range=(1, 1), # 1-gram만 사용 (단순화)
                min_df=1,           # 최소 문서 빈도
                max_df=1.0,         # 최대 문서 빈도 제한 제거
                token_pattern=r'\b\w+\b'  # 단어 토큰 패턴 명시
            )
    

    
    async def get_recent_news_for_similarity(self, days: int = None, limit: int = None) -> List[Dict[str, Any]]:
        """유사도 비교를 위한 최근 뉴스 조회
        
        Args:
            days: 조회할 일수 (None이면 설정값 사용, 기본 7일)
            limit: 최대 조회 건수 (None이면 설정값 사용, 기본 100건)
            
        Returns:
            최근 뉴스 리스트 (id, title, content 포함)
        """
        # 설정값이 없으면 기본값 사용
        if days is None:
            days = 7
        if limit is None:
            limit = 100
        async with self.get_connection() as db:
            # 최근 N일간의 뉴스 조회 (제목과 내용이 있는 것만)
            cursor = await db.execute(
                """
                SELECT id, title, content 
                FROM news_data 
                WHERE created_at >= datetime('now', '-{} days')
                AND title IS NOT NULL AND title != ''
                AND content IS NOT NULL AND content != ''
                ORDER BY created_at DESC
                LIMIT ?
                """.format(days),
                (limit,)
            )
            rows = await cursor.fetchall()
            
            return [
                {
                    'id': row[0],
                    'title': row[1],
                    'content': row[2]
                }
                for row in rows
            ]
    
    async def get_news_by_date_range(self, start_date, end_date) -> List[Dict[str, Any]]:
        """날짜 범위로 뉴스 조회
        
        Args:
            start_date: 시작 날짜 (date 객체)
            end_date: 종료 날짜 (date 객체)
            
        Returns:
            뉴스 리스트 (id, title, content, created_at 포함)
        """
        try:
            # date 객체를 datetime 문자열로 변환
            start_datetime = f"{start_date} 00:00:00"
            end_datetime = f"{end_date} 23:59:59"
            
            async with self.get_connection() as db:
                cursor = await db.execute(
                    """
                    SELECT id, title, content, created_at 
                    FROM news_data 
                    WHERE created_at >= ? AND created_at <= ?
                    AND title IS NOT NULL AND title != ''
                    AND content IS NOT NULL AND content != ''
                    ORDER BY created_at DESC
                    """,
                    (start_datetime, end_datetime)
                )
                rows = await cursor.fetchall()
                
                return [
                    {
                        'id': row[0],
                        'title': row[1],
                        'content': row[2],
                        'created_at': row[3]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"날짜 범위 뉴스 조회 오류: {e}")
            return []
    
    async def calculate_similarity_between_texts(self, text1: str, text2: str) -> float:
        """두 텍스트 간의 유사도 계산 (TF-IDF + 코사인 유사도)
        
        Args:
            text1: 첫 번째 텍스트
            text2: 두 번째 텍스트
            
        Returns:
            float: 유사도 (0.0 ~ 1.0)
        """
        try:
            # 텍스트 전처리
            processed_text1 = self.preprocess_text(text1)
            processed_text2 = self.preprocess_text(text2)
            
            self.logger.debug(f"전처리된 텍스트1: {processed_text1[:100]}...")
            self.logger.debug(f"전처리된 텍스트2: {processed_text2[:100]}...")
            
            if not processed_text1 or not processed_text2:
                self.logger.debug("전처리된 텍스트가 비어있음")
                return 0.0
            
            # TF-IDF 벡터화 (일반 유사도 계산용 설정 사용)
            vectorizer = self.get_vectorizer('similarity')
            
            # 두 텍스트 벡터화
            texts = [processed_text1, processed_text2]
            tfidf_matrix = vectorizer.fit_transform(texts)
            
            self.logger.debug(f"TF-IDF 매트릭스 형태: {tfidf_matrix.shape}")
            self.logger.debug(f"특성 개수: {len(vectorizer.get_feature_names_out())}")
            
            # 벡터가 비어있는지 확인
            if tfidf_matrix.nnz == 0:
                self.logger.debug("TF-IDF 벡터가 비어있음")
                return 0.0
            
            # 코사인 유사도 계산
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            self.logger.debug(f"계산된 유사도: {similarity}")
            
            return float(similarity)
            
        except Exception as e:
            self.logger.error(f"텍스트 간 유사도 계산 중 오류 발생: {e}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")
            return 0.0
    
    async def check_content_similarity(self, title: str, content: str, similarity_threshold: float = 0.8, days_back: int = 7) -> Tuple[bool, float]:
        """새로운 뉴스 내용과 기존 뉴스들의 유사도 검사
        
        Args:
            title: 새로운 뉴스 제목
            content: 새로운 뉴스 내용
            similarity_threshold: 유사도 임계값 (기본 0.8)
            days_back: 검사할 과거 일수 (기본 7일)
            
        Returns:
            Tuple[bool, float]: (유사한 뉴스가 있으면 True, 최대 유사도)
        """
        try:
            # 새로운 뉴스의 제목과 내용을 결합
            new_text = f"{title} {content}"
            
            # 최근 뉴스들 조회 (지정된 일수, 최대 100건)
            recent_news = await self.get_recent_news_for_similarity(days=days_back, limit=100)
            
            if not recent_news:
                self.logger.debug("비교할 기존 뉴스가 없음")
                return False, 0.0
            
            max_similarity = 0.0
            
            # 각 기존 뉴스와 유사도 비교
            for news in recent_news:
                existing_text = f"{news['title']} {news['content']}"
                
                # 유사도 계산
                similarity = await self.calculate_similarity_between_texts(new_text, existing_text)
                
                # 최대 유사도 업데이트
                if similarity > max_similarity:
                    max_similarity = similarity
                
                # 임계값을 초과하면 중복으로 판단
                if similarity >= similarity_threshold:
                    self.logger.info(f"유사한 뉴스 발견 - ID: {news['id']}, 유사도: {similarity:.3f}")
                    return True, similarity
            
            self.logger.debug(f"유사한 뉴스 없음 - 검사한 뉴스 수: {len(recent_news)}, 최대 유사도: {max_similarity:.3f}")
            return False, max_similarity
            
        except Exception as e:
            self.logger.error(f"내용 유사도 검사 중 오류 발생: {e}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")
            # 오류 발생 시 안전하게 중복이 아님으로 처리
            return False, 0.0
        
        self.logger.info("데이터베이스 초기화 완료")
    
    async def _create_tables(self, db: aiosqlite.Connection):
        """테이블 생성
        
        Args:
            db: 데이터베이스 연결
        """
        
        # 시장 데이터 테이블
        await db.execute("""
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                price REAL NOT NULL,
                volume INTEGER NOT NULL,
                bid_price REAL,
                ask_price REAL,
                bid_volume INTEGER,
                ask_volume INTEGER,
                change_rate REAL,
                market_cap REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 뉴스 데이터 테이블
        await db.execute("""
            CREATE TABLE IF NOT EXISTS news_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                source TEXT,
                url TEXT UNIQUE,  -- URL로 중복 방지
                published_at DATETIME,
                symbols TEXT,  -- JSON 배열
                sentiment REAL,
                importance INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 공시 데이터 테이블 (향후 확장을 위해)
        await db.execute("""
            CREATE TABLE IF NOT EXISTS disclosure_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                company_code TEXT,
                disclosure_type TEXT,
                url TEXT UNIQUE,
                published_at DATETIME,
                importance INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # AI 분석 결과 테이블
        await db.execute("""
            CREATE TABLE IF NOT EXISTS ai_decisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                decision TEXT NOT NULL,  -- buy, sell, hold
                confidence REAL NOT NULL,
                target_quantity INTEGER,
                target_price REAL,
                stop_loss REAL,
                take_profit REAL,
                reasoning TEXT,
                input_data TEXT,  -- JSON
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 주문 테이블
        await db.execute("""
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT UNIQUE,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,  -- buy, sell
                order_type TEXT NOT NULL,  -- market, limit
                quantity INTEGER NOT NULL,
                price REAL,
                status TEXT NOT NULL,  -- pending, filled, cancelled, failed
                filled_quantity INTEGER DEFAULT 0,
                filled_price REAL,
                commission REAL DEFAULT 0,
                slippage REAL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 포지션 테이블
        await db.execute("""
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL UNIQUE,
                quantity INTEGER NOT NULL,
                avg_price REAL NOT NULL,
                current_price REAL,
                unrealized_pnl REAL DEFAULT 0,
                realized_pnl REAL DEFAULT 0,
                entry_time DATETIME NOT NULL,
                last_update DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 성능 메트릭 테이블
        await db.execute("""
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_type TEXT NOT NULL,
                value REAL NOT NULL,
                details TEXT,  -- JSON
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 인덱스 생성
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_market_data_symbol_time ON market_data(symbol, timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_news_data_published ON news_data(published_at)",
            "CREATE INDEX IF NOT EXISTS idx_news_data_url ON news_data(url)",
            "CREATE INDEX IF NOT EXISTS idx_ai_decisions_symbol_time ON ai_decisions(symbol, created_at)",
            "CREATE INDEX IF NOT EXISTS idx_orders_symbol_status ON orders(symbol, status)",
            "CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_performance_metrics_type_time ON performance_metrics(metric_type, timestamp)"
        ]
        
        for index_sql in indexes:
            await db.execute(index_sql)
    
    async def _migrate_database(self, db: aiosqlite.Connection):
        """데이터베이스 마이그레이션
        
        Args:
            db: 데이터베이스 연결
        """
        try:
            # 기존 url_key 컬럼이 있다면 제거하고 url을 UNIQUE로 변경
            cursor = await db.execute("PRAGMA table_info(news_data)")
            columns = await cursor.fetchall()
            column_names = [column[1] for column in columns]
            
            if 'url_key' in column_names:
                self.logger.info("url_key 컬럼 제거 및 url UNIQUE 제약조건 추가")
                
                # 임시 테이블 생성
                await db.execute("""
                    CREATE TABLE news_data_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT,
                        source TEXT,
                        url TEXT UNIQUE,
                        published_at DATETIME,
                        symbols TEXT,
                        sentiment REAL,
                        importance INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 기존 데이터 복사 (url_key 제외)
                await db.execute("""
                    INSERT INTO news_data_new (id, title, content, source, url, published_at, symbols, sentiment, importance, created_at)
                    SELECT id, title, content, source, url, published_at, symbols, sentiment, importance, created_at
                    FROM news_data
                    WHERE url IS NOT NULL
                """)
                
                # 기존 테이블 삭제 및 새 테이블로 교체
                await db.execute("DROP TABLE news_data")
                await db.execute("ALTER TABLE news_data_new RENAME TO news_data")
                
                # 인덱스 재생성
                await db.execute("CREATE INDEX IF NOT EXISTS idx_news_data_published ON news_data(published_at)")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_news_data_url ON news_data(url)")
                
                self.logger.info("url_key 컬럼 제거 및 url UNIQUE 제약조건 추가 완료")
                
            # ai_decisions 테이블에 source_type, source_id 컬럼 추가 (다양한 데이터 소스 지원)
            cursor = await db.execute("PRAGMA table_info(ai_decisions)")
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # news_id 컬럼이 있다면 제거하고 새로운 구조로 변경
            if 'news_id' in column_names and 'source_type' not in column_names:
                # 기존 news_id 데이터를 새로운 구조로 마이그레이션
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN source_type TEXT")
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN source_id INTEGER")
                
                # 기존 news_id 데이터를 source_type='news', source_id=news_id로 변경
                await db.execute("""
                    UPDATE ai_decisions 
                    SET source_type = 'news', source_id = news_id 
                    WHERE news_id IS NOT NULL
                """)
                
                await db.execute("CREATE INDEX IF NOT EXISTS idx_ai_decisions_source ON ai_decisions(source_type, source_id)")
                self.logger.info("ai_decisions 테이블에 source_type, source_id 컬럼 추가 및 기존 데이터 마이그레이션 완료")
            
            elif 'source_type' not in column_names:
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN source_type TEXT")
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN source_id INTEGER")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_ai_decisions_source ON ai_decisions(source_type, source_id)")
                self.logger.info("ai_decisions 테이블에 source_type, source_id 컬럼 추가 완료")
            
            # 뉴스 분석 결과를 위한 새로운 컬럼들 추가
            if 'sentiment_score' not in column_names:
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN sentiment_score REAL")
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN market_impact TEXT")
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN affected_sector TEXT")
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN affected_stock TEXT")
                await db.execute("ALTER TABLE ai_decisions ADD COLUMN time_horizon TEXT")
                self.logger.info("ai_decisions 테이블에 뉴스 분석 결과 컬럼들 추가 완료")
                
        except Exception as e:
            self.logger.error(f"데이터베이스 마이그레이션 오류: {e}")
    

    
    @asynccontextmanager
    async def get_connection(self):
        """데이터베이스 연결 컨텍스트 매니저 (연결 풀 사용)"""
        async with self._connection_pool.get_connection() as db:
            yield db
    
    async def execute(self, query: str, params: tuple = None):
        """SQL 쿼리 실행
        
        Args:
            query: SQL 쿼리
            params: 쿼리 매개변수
            
        Returns:
            쿼리 실행 결과
        """
        async with self.get_connection() as db:
            if params:
                cursor = await db.execute(query, params)
            else:
                cursor = await db.execute(query)
            await db.commit()
            return cursor
    
    async def insert_market_data(self, data: Dict[str, Any]):
        """시장 데이터 삽입
        
        Args:
            data: 시장 데이터
        """
        async with self.get_connection() as db:
            await db.execute("""
                INSERT INTO market_data (
                    symbol, timestamp, price, volume, bid_price, ask_price,
                    bid_volume, ask_volume, change_rate, market_cap
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['symbol'],
                data['timestamp'],
                data['price'],
                data['volume'],
                data.get('bid_price'),
                data.get('ask_price'),
                data.get('bid_volume'),
                data.get('ask_volume'),
                data.get('change_rate'),
                data.get('market_cap')
            ))
            await db.commit()
    
    async def insert_news_data(self, data: Dict[str, Any]) -> int:
        """뉴스 데이터 삽입
        
        Args:
            data: 뉴스 데이터
            
        Returns:
            int: 삽입된 뉴스의 ID
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                INSERT INTO news_data (
                    title, content, source, url, published_at
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                data['title'],
                data.get('content'),
                data.get('source'),
                data.get('url'),
                data.get('published_at')
            ))
            await db.commit()
            return cursor.lastrowid
    
    async def insert_disclosure_data(self, data: Dict[str, Any]) -> int:
        """공시 데이터 삽입
        
        Args:
            data: 공시 데이터
            
        Returns:
            int: 삽입된 공시의 ID
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                INSERT INTO disclosure_data (
                    title, content, company_code, disclosure_type, url, published_at, importance
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                data['title'],
                data.get('content'),
                data.get('company_code'),
                data.get('disclosure_type'),
                data.get('url'),
                data.get('published_at'),
                data.get('importance', 0)
            ))
            await db.commit()
            return cursor.lastrowid
    
    async def insert_ai_decision(self, data: Dict[str, Any]):
        """AI 분석 결과 삽입
        
        Args:
            data: AI 분석 결과
        """
        async with self.get_connection() as db:
            await db.execute("""
                INSERT INTO ai_decisions (
                    symbol, decision, confidence, target_quantity,
                    target_price, stop_loss, take_profit, reasoning, input_data, 
                    source_type, source_id, sentiment_score, market_impact, 
                    affected_sector, affected_stock, time_horizon
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['symbol'],
                data['decision'],
                data['confidence'],
                data.get('target_quantity'),
                data.get('target_price'),
                data.get('stop_loss'),
                data.get('take_profit'),
                data.get('reasoning'),
                json.dumps(data.get('input_data', {})),
                data.get('source_type'),  # 데이터 소스 타입 (news, disclosure, etc.)
                data.get('source_id'),    # 해당 소스의 ID
                data.get('sentiment_score'),  # 감정 점수
                data.get('market_impact'),    # 시장 영향도
                data.get('affected_sector'),  # 영향받는 섹터
                data.get('affected_stock'),   # 영향받는 종목
                data.get('time_horizon')      # 시간 지평
            ))
            await db.commit()
    
    async def insert_order(self, data: Dict[str, Any]):
        """주문 데이터 삽입
        
        Args:
            data: 주문 데이터
        """
        async with self.get_connection() as db:
            await db.execute("""
                INSERT INTO orders (
                    order_id, symbol, side, order_type, quantity,
                    price, status, filled_quantity, filled_price,
                    commission, slippage
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['order_id'],
                data['symbol'],
                data['side'],
                data['order_type'],
                data['quantity'],
                data.get('price'),
                data['status'],
                data.get('filled_quantity', 0),
                data.get('filled_price'),
                data.get('commission', 0),
                data.get('slippage', 0)
            ))
            await db.commit()
    
    async def update_order_status(self, order_id: str, status: str, **kwargs):
        """주문 상태 업데이트
        
        Args:
            order_id: 주문 ID
            status: 새로운 상태
            **kwargs: 추가 업데이트 필드
        """
        async with self.get_connection() as db:
            # 기본 업데이트 필드
            update_fields = ['status = ?', 'updated_at = CURRENT_TIMESTAMP']
            values = [status]
            
            # 추가 필드 처리
            for field, value in kwargs.items():
                if field in ['filled_quantity', 'filled_price', 'commission', 'slippage']:
                    update_fields.append(f'{field} = ?')
                    values.append(value)
            
            values.append(order_id)
            
            await db.execute(f"""
                UPDATE orders SET {', '.join(update_fields)}
                WHERE order_id = ?
            """, values)
            await db.commit()
    
    async def upsert_position(self, data: Dict[str, Any]):
        """포지션 업서트 (삽입 또는 업데이트)
        
        Args:
            data: 포지션 데이터
        """
        async with self.get_connection() as db:
            await db.execute("""
                INSERT OR REPLACE INTO positions (
                    symbol, quantity, avg_price, current_price,
                    unrealized_pnl, realized_pnl, entry_time, last_update
                ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                data['symbol'],
                data['quantity'],
                data['avg_price'],
                data.get('current_price'),
                data.get('unrealized_pnl', 0),
                data.get('realized_pnl', 0),
                data.get('entry_time', datetime.now())
            ))
            await db.commit()
    
    async def get_latest_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """최신 시장 데이터 조회
        
        Args:
            symbol: 종목코드
        
        Returns:
            최신 시장 데이터
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                SELECT * FROM market_data
                WHERE symbol = ?
                ORDER BY timestamp DESC
                LIMIT 1
            """, (symbol,))
            row = await cursor.fetchone()
            return dict(row) if row else None
    
    async def get_recent_news(self, hours: int = 24) -> List[Dict[str, Any]]:
        """최근 뉴스 조회
        
        Args:
            hours: 조회할 시간 범위 (시간)
        
        Returns:
            뉴스 목록
        """
        since = datetime.now() - timedelta(hours=hours)
        
        async with self.get_connection() as db:
            cursor = await db.execute("""
                SELECT * FROM news_data
                WHERE published_at >= ?
                ORDER BY published_at DESC
            """, (since,))
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def get_active_positions(self) -> List[Dict[str, Any]]:
        """활성 포지션 조회
        
        Returns:
            활성 포지션 목록
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                SELECT * FROM positions
                WHERE quantity != 0
                ORDER BY entry_time DESC
            """)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def get_pending_orders(self) -> List[Dict[str, Any]]:
        """대기 중인 주문 조회
        
        Returns:
            대기 중인 주문 목록
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                SELECT * FROM orders
                WHERE status = 'pending'
                ORDER BY created_at ASC
            """)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def insert_performance_metric(self, metric_type: str, value: float, details: Dict[str, Any] = None):
        """성능 메트릭 삽입
        
        Args:
            metric_type: 메트릭 유형
            value: 메트릭 값
            details: 상세 정보
        """
        async with self.get_connection() as db:
            await db.execute("""
                INSERT INTO performance_metrics (metric_type, value, details)
                VALUES (?, ?, ?)
            """, (
                metric_type,
                value,
                json.dumps(details) if details else None
            ))
            await db.commit()
    
    async def insert_performance_metrics(self, metrics: List[Dict[str, Any]]):
        """성능 메트릭 배치 삽입
        
        Args:
            metrics: 메트릭 목록
        """
        async with self.get_connection() as db:
            for metric in metrics:
                await db.execute("""
                    INSERT INTO performance_metrics (metric_type, value, details)
                    VALUES (?, ?, ?)
                """, (
                    metric['metric_type'],
                    metric['value'],
                    json.dumps(metric.get('details')) if metric.get('details') else None
                ))
            await db.commit()
    
    async def get_performance_metrics(self, metric_type: str = None, days: int = 30, limit: int = None) -> List[Dict[str, Any]]:
        """성능 메트릭 조회
        
        Args:
            metric_type: 메트릭 유형 (None이면 전체)
            days: 조회할 일수
            limit: 최대 조회 개수
        
        Returns:
            성능 메트릭 목록
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        async with self.get_connection() as db:
            if metric_type:
                query = """
                    SELECT * FROM performance_metrics
                    WHERE metric_type = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                """
                params = (metric_type, cutoff_date)
            else:
                query = """
                    SELECT * FROM performance_metrics
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                """
                params = (cutoff_date,)
            
            if limit:
                query += " LIMIT ?"
                params = params + (limit,)
            
            cursor = await db.execute(query, params)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def get_all_positions(self) -> List[Dict[str, Any]]:
        """모든 포지션 조회 (0 수량 포함)
        
        Returns:
            모든 포지션 목록
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                SELECT * FROM positions
                ORDER BY entry_time DESC
            """)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def get_trades_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """날짜 범위별 거래 내역 조회
        
        Args:
            start_date: 시작 날짜
            end_date: 종료 날짜
        
        Returns:
            거래 내역 목록
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                SELECT * FROM orders
                WHERE status = 'filled'
                AND created_at BETWEEN ? AND ?
                ORDER BY created_at DESC
            """, (start_date, end_date))
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def get_trades_by_date(self, date: datetime) -> List[Dict[str, Any]]:
        """특정 날짜의 거래 내역 조회
        
        Args:
            date: 조회할 날짜
        
        Returns:
            거래 내역 목록
        """
        start_date = date.replace(hour=0, minute=0, second=0).replace(microsecond=0)
        end_date = start_date.replace(hour=23, minute=59, second=59).replace(microsecond=999999)
        return await self.get_trades_by_date_range(start_date, end_date)
    
    async def save_alert(self, alert_data: Dict[str, Any]):
        """알림 저장
        
        Args:
            alert_data: 알림 데이터
        """
        # alerts 테이블이 없으면 생성
        async with self.get_connection() as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    source TEXT,
                    metadata TEXT,
                    acknowledged_at TEXT,
                    resolved_at TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            await db.execute("""
                INSERT OR REPLACE INTO alerts 
                (id, type, severity, title, message, timestamp, status, source, metadata, acknowledged_at, resolved_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                alert_data.get('id', f"alert_{int(datetime.now().timestamp() * 1000)}"),
                alert_data.get('type', 'UNKNOWN'),
                alert_data.get('severity', 'LOW'),
                alert_data.get('title', ''),
                alert_data.get('message', ''),
                alert_data.get('timestamp', datetime.now().isoformat()),
                alert_data.get('status', 'active'),
                alert_data.get('source', ''),
                alert_data.get('metadata', None),
                alert_data.get('acknowledged_at', None),
                alert_data.get('resolved_at', None)
            ))
            await db.commit()
    
    async def get_active_alerts(self) -> List[Dict[str, Any]]:
        """활성 알림 조회
        
        Returns:
            활성 알림 목록
        """
        async with self.get_connection() as db:
            # alerts 테이블이 없으면 생성
            await db.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    source TEXT,
                    metadata TEXT,
                    acknowledged_at TEXT,
                    resolved_at TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cursor = await db.execute("""
                SELECT * FROM alerts
                WHERE status = 'active'
                ORDER BY timestamp DESC
            """)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def cleanup_old_alerts(self, cutoff_time: datetime):
        """오래된 알림 정리
        
        Args:
            cutoff_time: 정리 기준 시간
        """
        async with self.get_connection() as db:
            # alerts 테이블이 없으면 생성
            await db.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    source TEXT,
                    metadata TEXT,
                    acknowledged_at TEXT,
                    resolved_at TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            await db.execute("""
                DELETE FROM alerts
                WHERE created_at < ?
            """, (cutoff_time.isoformat(),))
            await db.commit()
    
    async def get_active_orders(self) -> List[Dict[str, Any]]:
        """활성 주문 조회
        
        Returns:
            활성 주문 목록
        """
        async with self.get_connection() as db:
            cursor = await db.execute("""
                SELECT * FROM orders
                WHERE status IN ('pending', 'partial')
                ORDER BY created_at DESC
            """)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def cleanup_old_market_data(self, days: int = 30):
        """오래된 시장 데이터 정리
        
        Args:
            days: 보관할 일수
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        async with self.get_connection() as db:
            await db.execute("""
                DELETE FROM market_data
                WHERE created_at < ?
            """, (cutoff_date.isoformat(),))
            await db.commit()
        
        self.logger.info(f"오래된 시장 데이터 정리 완료: {days}일 이전 데이터 삭제")
    
    async def cleanup_old_data(self, days: int = 30):
        """오래된 데이터 정리
        
        Args:
            days: 보관할 일수
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        async with self.get_connection() as db:
            # 오래된 시장 데이터 삭제
            await db.execute("""
                DELETE FROM market_data
                WHERE created_at < ?
            """, (cutoff_date.isoformat(),))
            
            # 오래된 뉴스 데이터 삭제
            await db.execute("""
                DELETE FROM news_data
                WHERE created_at < ?
            """, (cutoff_date.isoformat(),))
            
            # 오래된 성능 메트릭 삭제
            await db.execute("""
                DELETE FROM performance_metrics
                WHERE timestamp < ?
            """, (cutoff_date.isoformat(),))
            
            await db.commit()
        
        self.logger.info(f"오래된 데이터 정리 완료: {days}일 이전 데이터 삭제")
    
    async def close(self):
        """데이터베이스 연결 종료"""
        self.logger.info("데이터베이스 연결 종료")
        if hasattr(self, '_connection_pool'):
            await self._connection_pool.close_all()
            self.logger.info("데이터베이스 연결 풀 종료 완료")