# -*- coding: utf-8 -*-
"""
스케줄러 모듈

지정된 시간에 자동으로 작업을 실행하는 스케줄러
"""

import asyncio
import schedule
import threading
from datetime import datetime, time
from typing import Callable, List, Optional, Dict, Any
from .logger import get_logger
from .config_manager import ConfigManager


class TaskScheduler:
    """
    작업 스케줄러
    
    지정된 시간에 자동으로 작업을 실행하는 스케줄러
    """
    
    def __init__(self, config_manager: ConfigManager):
        """스케줄러 초기화
        
        Args:
            config_manager: 설정 관리자
        """
        self.config = config_manager
        self.logger = get_logger()
        
        # 스케줄러 상태
        self.is_running = False
        self.scheduler_thread = None
        self.stop_event = threading.Event()
        
        # 등록된 작업들
        self.scheduled_jobs = []
        
        # 기본 마스터 데이터 업데이트 시간들 (1분 후에 실행)
        self.master_update_times = [
            "06:01",  # 06:00 + 1분
            "06:56",  # 06:55 + 1분
            "07:36",  # 07:35 + 1분
            "07:56",  # 07:55 + 1분
            "08:46",  # 08:45 + 1분
            "09:47",  # 09:46 + 1분
            "10:56",  # 10:55 + 1분
            "17:11",  # 17:10 + 1분
            "17:31",  # 17:30 + 1분
            "17:56",  # 17:55 + 1분
            "18:11",  # 18:10 + 1분
            "18:31",  # 18:30 + 1분
            "18:56",  # 18:55 + 1분
        ]
        
    def add_daily_job(self, time_str: str, job_func: Callable, *args, **kwargs) -> None:
        """
        매일 지정된 시간에 실행할 작업 추가
        
        Args:
            time_str: 실행 시간 (HH:MM 형식)
            job_func: 실행할 함수
            *args: 함수 인자
            **kwargs: 함수 키워드 인자
        """
        try:
            # 시간 형식 검증
            datetime.strptime(time_str, "%H:%M")
            
            # 스케줄 등록
            job = schedule.every().day.at(time_str).do(self._run_async_job, job_func, *args, **kwargs)
            self.scheduled_jobs.append({
                'time': time_str,
                'function': job_func.__name__,
                'job': job
            })
            
            self.logger.info(f"작업 스케줄 등록: {job_func.__name__} at {time_str}")
            
        except ValueError as e:
            self.logger.error(f"잘못된 시간 형식: {time_str} - {e}")
            raise
    
    def add_master_update_jobs(self, update_func: Callable) -> None:
        """
        마스터 데이터 업데이트 작업들을 모든 지정된 시간에 등록
        
        Args:
            update_func: 마스터 데이터 업데이트 함수
        """
        for time_str in self.master_update_times:
            self.add_daily_job(time_str, update_func)
        
        self.logger.info(f"마스터 데이터 업데이트 작업 {len(self.master_update_times)}개 등록 완료")
    
    def _run_async_job(self, job_func: Callable, *args, **kwargs) -> None:
        """
        비동기 작업을 동기 환경에서 실행
        
        Args:
            job_func: 실행할 함수
            *args: 함수 인자
            **kwargs: 함수 키워드 인자
        """
        try:
            self.logger.info(f"스케줄된 작업 실행 시작: {job_func.__name__}")
            
            # 비동기 함수인지 확인
            if asyncio.iscoroutinefunction(job_func):
                # 현재 실행 중인 이벤트 루프가 있는지 확인
                try:
                    # 현재 루프가 있으면 새 스레드에서 실행
                    current_loop = asyncio.get_running_loop()
                    if current_loop.is_running():
                        import concurrent.futures
                        import threading
                        
                        def run_in_thread():
                            new_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(new_loop)
                            try:
                                return new_loop.run_until_complete(job_func(*args, **kwargs))
                            finally:
                                new_loop.close()
                        
                        with concurrent.futures.ThreadPoolExecutor() as executor:
                            future = executor.submit(run_in_thread)
                            future.result(timeout=300)  # 5분 타임아웃
                    else:
                        current_loop.run_until_complete(job_func(*args, **kwargs))
                except RuntimeError:
                    # 실행 중인 루프가 없으면 새 루프 생성
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(job_func(*args, **kwargs))
                    finally:
                        loop.close()
            else:
                # 동기 함수 실행
                job_func(*args, **kwargs)
            
            self.logger.info(f"스케줄된 작업 실행 완료: {job_func.__name__}")
            
        except Exception as e:
            self.logger.error(f"스케줄된 작업 실행 오류 ({job_func.__name__}): {e}")
    
    def start(self) -> None:
        """
        스케줄러 시작
        """
        if self.is_running:
            self.logger.warning("스케줄러가 이미 실행 중입니다")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # 스케줄러 스레드 시작
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info(f"스케줄러 시작 - 등록된 작업 수: {len(self.scheduled_jobs)}")
        
        # 등록된 작업들 로그 출력
        for job_info in self.scheduled_jobs:
            self.logger.info(f"  - {job_info['time']}: {job_info['function']}")
    
    def stop(self) -> None:
        """
        스케줄러 중지
        """
        if not self.is_running:
            self.logger.warning("스케줄러가 실행 중이 아닙니다")
            return
        
        self.is_running = False
        self.stop_event.set()
        
        # 스케줄러 스레드 종료 대기
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # 모든 스케줄 작업 취소
        schedule.clear()
        self.scheduled_jobs.clear()
        
        self.logger.info("스케줄러 중지 완료")
    
    def _run_scheduler(self) -> None:
        """
        스케줄러 메인 루프
        """
        while not self.stop_event.is_set():
            try:
                # 대기 중인 작업 실행
                schedule.run_pending()
                
                # 1초 대기
                self.stop_event.wait(1)
                
            except Exception as e:
                self.logger.error(f"스케줄러 실행 오류: {e}")
                self.stop_event.wait(5)  # 오류 시 5초 대기
    
    def get_next_run_time(self) -> Optional[str]:
        """
        다음 실행 예정 시간 조회
        
        Returns:
            다음 실행 예정 시간 (문자열) 또는 None
        """
        if not self.scheduled_jobs:
            return None
        
        try:
            next_run = schedule.next_run()
            if next_run:
                return next_run.strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            self.logger.error(f"다음 실행 시간 조회 오류: {e}")
        
        return None
    
    def get_job_status(self) -> Dict[str, Any]:
        """
        스케줄러 상태 정보 조회
        
        Returns:
            스케줄러 상태 정보
        """
        return {
            'is_running': self.is_running,
            'job_count': len(self.scheduled_jobs),
            'next_run_time': self.get_next_run_time(),
            'scheduled_jobs': [
                {
                    'time': job_info['time'],
                    'function': job_info['function']
                }
                for job_info in self.scheduled_jobs
            ]
        }
    
    def run_job_now(self, job_name: str) -> bool:
        """
        지정된 작업을 즉시 실행
        
        Args:
            job_name: 실행할 작업 함수명
            
        Returns:
            실행 성공 여부
        """
        for job_info in self.scheduled_jobs:
            if job_info['function'] == job_name:
                try:
                    job_info['job'].run()
                    self.logger.info(f"작업 즉시 실행 완료: {job_name}")
                    return True
                except Exception as e:
                    self.logger.error(f"작업 즉시 실행 오류 ({job_name}): {e}")
                    return False
        
        self.logger.warning(f"작업을 찾을 수 없음: {job_name}")
        return False


class MasterDataScheduler(TaskScheduler):
    """
    마스터 데이터 전용 스케줄러
    
    주식 마스터 데이터 자동 업데이트를 위한 특화된 스케줄러
    """
    
    def __init__(self, config_manager: ConfigManager, stock_info_source):
        """마스터 데이터 스케줄러 초기화
        
        Args:
            config_manager: 설정 관리자
            stock_info_source: 주식 정보 소스 인스턴스
        """
        super().__init__(config_manager)
        self.stock_info_source = stock_info_source
        
        # 마스터 데이터 업데이트 작업 등록
        self.setup_master_update_jobs()
    
    def setup_master_update_jobs(self) -> None:
        """
        마스터 데이터 업데이트 작업 설정
        """
        # 주식 마스터 데이터 업데이트 작업 등록
        self.add_master_update_jobs(self._update_master_data)
        
        self.logger.info("마스터 데이터 자동 업데이트 스케줄 설정 완료")
    
    async def _update_master_data(self) -> None:
        """
        마스터 데이터 업데이트 실행
        """
        try:
            self.logger.info("스케줄된 마스터 데이터 업데이트 시작")
            
            # StockInfoSource의 업데이트 메서드 호출
            if hasattr(self.stock_info_source, 'update_stock_info'):
                await self.stock_info_source.update_stock_info()
            else:
                self.logger.warning("StockInfoSource에 update_stock_info 메서드가 없습니다")
            
            self.logger.info("스케줄된 마스터 데이터 업데이트 완료")
            
        except Exception as e:
            self.logger.error(f"스케줄된 마스터 데이터 업데이트 오류: {e}")