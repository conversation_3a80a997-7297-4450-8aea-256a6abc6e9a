# -*- coding: utf-8 -*-
"""
로깅 유틸리티

고성능 로깅 시스템 구현
"""

import logging
import sys
import gzip
import shutil
from pathlib import Path
from logging.handlers import RotatingFileHand<PERSON>, TimedRotatingFileHandler
from datetime import datetime
from typing import Optional, Dict, Any
import time
import functools

try:
    from colorama import Fore, Back, Style, init
    init(autoreset=True)  # 자동으로 색상 리셋
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False
    # colorama가 없을 때 더미 클래스
    class Fore:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ''
    class Back:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ''
    class Style:
        DIM = NORMAL = BRIGHT = RESET_ALL = ''

class ColoredFormatter(logging.Formatter):
    """
    색상이 적용된 로그 포매터
    """
    
    # 로그 레벨별 색상 매핑
    COLORS = {
        'DEBUG': Fore.BLUE,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Style.BRIGHT,
    }
    
    # 특별한 키워드별 색상
    KEYWORD_COLORS = {
        '📋': Fore.BLUE,      # 주문
        '✅': Fore.GREEN,     # 체결/완료
        '💰': Fore.GREEN + Style.BRIGHT,    # 수익
        '💸': Fore.RED + Style.BRIGHT,      # 손실
        '🤖': Fore.MAGENTA,   # AI 판단
        '⚠️': Fore.YELLOW + Style.BRIGHT,   # 경고
        '🚨': Fore.RED + Back.YELLOW + Style.BRIGHT,  # 긴급
        'API': Fore.CYAN,
        'DB': Fore.BLUE,
        'KIS': Fore.MAGENTA,
        'OpenAI': Fore.GREEN,
        'Telegram': Fore.BLUE,
        'RSS': Fore.YELLOW,
        'WEB': Fore.YELLOW,
    }
    
    def format(self, record):
        # 기본 포맷팅
        formatted = super().format(record)
        
        if not COLORAMA_AVAILABLE:
            return formatted
        
        # 로그 레벨 색상 적용
        level_color = self.COLORS.get(record.levelname, '')
        
        # 메시지에서 특별한 키워드 색상 적용
        message = record.getMessage()
        for keyword, color in self.KEYWORD_COLORS.items():
            if keyword in message:
                message = message.replace(keyword, f"{color}{keyword}{Style.RESET_ALL}")
        
        # 전체 로그에 레벨 색상 적용
        if level_color:
            # 타임스탬프는 기본 색상, 레벨과 메시지에만 색상 적용
            parts = formatted.split(' | ')
            if len(parts) >= 4:
                timestamp = parts[0]
                level = f"{level_color}{parts[1]}{Style.RESET_ALL}"
                location = f"{Fore.CYAN}{parts[2]}{Style.RESET_ALL}"
                message_part = f"{level_color}{' | '.join(parts[3:])}{Style.RESET_ALL}"
                
                # 키워드 색상이 적용된 메시지로 교체
                if any(keyword in message for keyword in self.KEYWORD_COLORS.keys()):
                    message_part = f"{level_color}{message}{Style.RESET_ALL}"
                
                formatted = f"{timestamp} | {level} | {location} | {message_part}"
        
        return formatted

def setup_logger(
    name: str = "stockbot",
    level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: str = "10MB",
    backup_count: int = 5,
    config_manager=None
) -> logging.Logger:
    """
    로거 설정
    
    Args:
        name: 로거 이름
        level: 로그 레벨
        log_file: 로그 파일 경로
        max_file_size: 최대 파일 크기
        backup_count: 백업 파일 개수
        config_manager: 설정 관리자
    
    Returns:
        설정된 로거
    """
    
    # config에서 로깅 설정 가져오기
    if config_manager:
        logging_config = config_manager.get('logging', {})
        level = logging_config.get('level', level)
        
        # 추가 설정
        console_config = logging_config.get('console', {})
        file_config = logging_config.get('file', {})
        error_file_config = logging_config.get('error_file', {})
        performance_config = logging_config.get('performance', {})
        
        # file_config가 문자열인 경우 처리 (하위 호환성)
        if isinstance(file_config, str):
            file_config = {'enabled': True, 'path': file_config}
        
        console_enabled = console_config.get('enabled', True)
        file_enabled = file_config.get('enabled', True)
        error_file_enabled = error_file_config.get('enabled', False)
        performance_enabled = performance_config.get('enabled', False)
        
        colored_output = console_config.get('show_colors', logging_config.get('colored_output', True))
        format_string = logging_config.get('format', '%(asctime)s | %(levelname)8s | %(name)s:%(lineno)d | %(message)s')
        date_format = logging_config.get('date_format', '%Y-%m-%d %H:%M:%S')
        
        # 각 핸들러별 레벨 설정
        console_level = console_config.get('level', level)
        file_level = file_config.get('level', level)
        
        # 파일 설정
        log_file = file_config.get('path', log_file)
        max_file_size = file_config.get('max_size', max_file_size)
        backup_count = file_config.get('backup_count', backup_count)
        rotation = file_config.get('rotation', 'size')  # size, daily, weekly, monthly
        compress_backups = file_config.get('compress', False)
    else:
        console_enabled = True
        file_enabled = True
        error_file_enabled = False
        performance_enabled = False
        colored_output = True
        format_string = '%(asctime)s | %(levelname)8s | %(name)s:%(lineno)d | %(message)s'
        date_format = '%Y-%m-%d %H:%M:%S'
        console_level = 'INFO'
        file_level = level
        rotation = 'size'
        compress_backups = False
    
    # 로거 생성
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 기존 핸들러 제거
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 중복 로그 방지: 상위 로거로 전파하지 않음
    logger.propagate = False
    
    # 포매터 생성
    if colored_output and COLORAMA_AVAILABLE:
        colored_formatter = ColoredFormatter(
            fmt=format_string,
            datefmt=date_format
        )
    else:
        colored_formatter = logging.Formatter(
            fmt=format_string,
            datefmt=date_format
        )
    
    # 파일용 일반 포매터 (색상 없음)
    file_formatter = logging.Formatter(
        fmt=format_string,
        datefmt=date_format
    )
    
    # 콘솔 핸들러
    if console_enabled:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, console_level.upper()))
        console_handler.setFormatter(colored_formatter)
        logger.addHandler(console_handler)
    
    # 파일 핸들러 (옵션)
    if file_enabled and log_file:
        file_handler = _create_file_handler(
            log_file, file_level, rotation, max_file_size, 
            backup_count, compress_backups, file_formatter
        )
        if file_handler:
            logger.addHandler(file_handler)
    
    # 에러 전용 파일 핸들러
    if config_manager and error_file_enabled:
        error_config = logging_config.get('error_file', {})
        error_file = error_config.get('path', 'logs/errors.log')
        error_level = error_config.get('level', 'ERROR')
        error_max_size = error_config.get('max_size', '5MB')
        error_backup_count = error_config.get('backup_count', 3)
        
        error_handler = _create_file_handler(
            error_file, error_level, 'size', error_max_size,
            error_backup_count, compress_backups, file_formatter
        )
        if error_handler:
            logger.addHandler(error_handler)
    
    # 성능 로그 핸들러
    if config_manager and performance_enabled:
        perf_config = logging_config.get('performance', {})
        perf_file = perf_config.get('path', 'logs/performance.log')
        perf_level = perf_config.get('level', 'INFO')
        perf_max_size = perf_config.get('max_size', '5MB')
        perf_backup_count = perf_config.get('backup_count', 3)
        
        perf_handler = _create_file_handler(
            perf_file, perf_level, 'size', perf_max_size,
            perf_backup_count, compress_backups, file_formatter
        )
        if perf_handler:
            # 성능 로그는 특정 로거에만 적용
            perf_logger = logging.getLogger(f"{name}.performance")
            perf_logger.addHandler(perf_handler)
    
    # 모듈별 로그 레벨 설정
    if config_manager:
        module_loggers = logging_config.get('loggers', {})
        for module_name, module_level in module_loggers.items():
            module_logger = logging.getLogger(module_name)
            module_logger.setLevel(getattr(logging, module_level.upper()))
    
    return logger

def _parse_file_size(size_str: str) -> int:
    """
    파일 크기 문자열을 바이트로 변환
    
    Args:
        size_str: 크기 문자열 (예: '10MB', '1GB')
    
    Returns:
        바이트 단위 크기
    """
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)

def _create_file_handler(
    log_file: str,
    level: str,
    rotation: str,
    max_size: str,
    backup_count: int,
    compress: bool,
    formatter: logging.Formatter
) -> Optional[logging.Handler]:
    """
    파일 핸들러 생성
    
    Args:
        log_file: 로그 파일 경로
        level: 로그 레벨
        rotation: 회전 방식 (size, daily, weekly, monthly)
        max_size: 최대 파일 크기
        backup_count: 백업 파일 개수
        compress: 백업 파일 압축 여부
        formatter: 로그 포매터
    
    Returns:
        생성된 파일 핸들러
    """
    try:
        # 로그 디렉토리 생성
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        if rotation == 'size':
            # 크기 기반 회전
            size_bytes = _parse_file_size(max_size)
            handler = RotatingFileHandler(
                filename=log_file,
                maxBytes=size_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
        elif rotation in ['daily', 'weekly', 'monthly']:
            # 시간 기반 회전
            when_map = {
                'daily': 'D',
                'weekly': 'W0',  # 월요일마다
                'monthly': 'midnight'  # 매일 자정 (월별은 별도 처리 필요)
            }
            handler = TimedRotatingFileHandler(
                filename=log_file,
                when=when_map.get(rotation, 'D'),
                interval=1,
                backupCount=backup_count,
                encoding='utf-8'
            )
        else:
            # 기본값: 크기 기반
            size_bytes = _parse_file_size(max_size)
            handler = RotatingFileHandler(
                filename=log_file,
                maxBytes=size_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
        
        # 압축 설정
        if compress and hasattr(handler, 'rotator'):
            handler.rotator = _compress_rotator
        
        handler.setLevel(getattr(logging, level.upper()))
        handler.setFormatter(formatter)
        
        return handler
        
    except Exception as e:
        print(f"파일 핸들러 생성 실패: {e}")
        return None

def _compress_rotator(source: str, dest: str):
    """
    로그 파일 압축 회전자
    
    Args:
        source: 원본 파일 경로
        dest: 대상 파일 경로
    """
    try:
        with open(source, 'rb') as f_in:
            with gzip.open(f"{dest}.gz", 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        Path(source).unlink()  # 원본 파일 삭제
    except Exception as e:
        print(f"로그 파일 압축 실패: {e}")
        # 압축 실패 시 일반 이동
        shutil.move(source, dest)

def log_performance(logger_name: str = "stockbot.performance"):
    """
    함수 실행 시간을 측정하고 로깅하는 데코레이터
    
    Args:
        logger_name: 성능 로그를 기록할 로거 이름
    
    Returns:
        데코레이터 함수
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = logging.getLogger(logger_name)
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 실행 시간 로깅
                logger.info(
                    f"⏱️ {func.__name__} 실행완료 | "
                    f"시간: {execution_time:.3f}초 | "
                    f"모듈: {func.__module__}"
                )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"❌ {func.__name__} 실행실패 | "
                    f"시간: {execution_time:.3f}초 | "
                    f"에러: {str(e)} | "
                    f"모듈: {func.__module__}"
                )
                raise
                
        return wrapper
    return decorator

def log_slow_query(threshold: float = 1.0, logger_name: str = "stockbot.performance"):
    """
    느린 쿼리/작업을 감지하고 로깅하는 데코레이터
    
    Args:
        threshold: 느린 작업으로 판단할 임계값 (초)
        logger_name: 로그를 기록할 로거 이름
    
    Returns:
        데코레이터 함수
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = logging.getLogger(logger_name)
            start_time = time.time()
            
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            if execution_time > threshold:
                logger.warning(
                    f"🐌 느린 작업 감지 | "
                    f"함수: {func.__name__} | "
                    f"실행시간: {execution_time:.3f}초 | "
                    f"임계값: {threshold}초 | "
                    f"모듈: {func.__module__}"
                )
            
            return result
            
        return wrapper
    return decorator

class PerformanceLogger:
    """
    성능 측정 로거 - 향상된 기능
    """
    
    def __init__(self, logger: logging.Logger, config_manager=None):
        """성능 로거 초기화
        
        Args:
            logger: 기본 로거
            config_manager: 설정 관리자
        """
        self.logger = logger
        self.start_times = {}
        self.operation_stats = {}  # 작업별 통계
        
        # 설정에서 임계값 가져오기
        if config_manager:
            perf_config = config_manager.get('logging', {}).get('performance', {})
            self.default_threshold = perf_config.get('slow_query_threshold', 1.0)
            self.log_slow_queries = perf_config.get('log_slow_queries', True)
        else:
            self.default_threshold = 1.0
            self.log_slow_queries = True
    
    def start_timer(self, operation: str):
        """타이머 시작
        
        Args:
            operation: 작업 이름
        """
        self.start_times[operation] = time.time()
    
    def end_timer(self, operation: str, threshold_sec: Optional[float] = None):
        """타이머 종료 및 로깅
        
        Args:
            operation: 작업 이름
            threshold_sec: 경고 임계값 (초, None이면 기본값 사용)
        
        Returns:
            실행 시간 (초)
        """
        if operation not in self.start_times:
            self.logger.warning(f"⚠️ 타이머가 시작되지 않음: {operation}")
            return None
        
        start_time = self.start_times.pop(operation)
        elapsed = time.time() - start_time
        
        # 통계 업데이트
        if operation not in self.operation_stats:
            self.operation_stats[operation] = {'count': 0, 'total_time': 0, 'max_time': 0, 'min_time': float('inf')}
        
        stats = self.operation_stats[operation]
        stats['count'] += 1
        stats['total_time'] += elapsed
        stats['max_time'] = max(stats['max_time'], elapsed)
        stats['min_time'] = min(stats['min_time'], elapsed)
        
        # 임계값 확인
        threshold = threshold_sec if threshold_sec is not None else self.default_threshold
        
        if self.log_slow_queries and elapsed > threshold:
            avg_time = stats['total_time'] / stats['count']
            self.logger.warning(
                f"🐌 {operation} 지연 감지 | "
                f"실행시간: {elapsed:.3f}초 | "
                f"임계값: {threshold:.3f}초 | "
                f"평균: {avg_time:.3f}초 | "
                f"최대: {stats['max_time']:.3f}초 | "
                f"실행횟수: {stats['count']}"
            )
        else:
            self.logger.debug(f"⏱️ {operation} 완료: {elapsed:.3f}초")
        
        return elapsed
    
    def get_stats(self, operation: str = None) -> Dict[str, Any]:
        """작업 통계 조회
        
        Args:
            operation: 특정 작업 이름 (None이면 전체)
        
        Returns:
            통계 정보
        """
        if operation:
            return self.operation_stats.get(operation, {})
        return self.operation_stats.copy()
    
    def log_stats_summary(self):
        """통계 요약 로깅"""
        if not self.operation_stats:
            return
        
        self.logger.info("📊 성능 통계 요약:")
        for operation, stats in self.operation_stats.items():
            avg_time = stats['total_time'] / stats['count']
            self.logger.info(
                f"  {operation}: "
                f"평균 {avg_time:.3f}초, "
                f"최대 {stats['max_time']:.3f}초, "
                f"최소 {stats['min_time']:.3f}초, "
                f"총 {stats['count']}회 실행"
            )

class TradingLogger:
    """
    매매 전용 로거
    """
    
    def __init__(self, logger: logging.Logger):
        """매매 로거 초기화
        
        Args:
            logger: 기본 로거
        """
        self.logger = logger
    
    def log_order(self, action: str, symbol: str, quantity: int, price: float, order_type: str):
        """주문 로깅
        
        Args:
            action: 매수/매도
            symbol: 종목코드
            quantity: 수량
            price: 가격
            order_type: 주문 유형
        """
        self.logger.info(
            f"📋 주문: {action} | {symbol} | {quantity:,}주 | {price:,.0f}원 | {order_type}"
        )
    
    def log_execution(self, symbol: str, quantity: int, price: float, amount: float):
        """체결 로깅
        
        Args:
            symbol: 종목코드
            quantity: 체결 수량
            price: 체결 가격
            amount: 체결 금액
        """
        self.logger.info(
            f"✅ 체결: {symbol} | {quantity:,}주 | {price:,.0f}원 | {amount:,.0f}원"
        )
    
    def log_profit_loss(self, symbol: str, pnl: float, pnl_ratio: float):
        """손익 로깅
        
        Args:
            symbol: 종목코드
            pnl: 손익 금액
            pnl_ratio: 손익률
        """
        emoji = "💰" if pnl > 0 else "💸" if pnl < 0 else "➖"
        self.logger.info(
            f"{emoji} 손익: {symbol} | {pnl:+,.0f}원 | {pnl_ratio:+.2f}%"
        )
    
    def log_ai_decision(self, symbol: str, decision: str, confidence: float, reason: str):
        """AI 판단 로깅
        
        Args:
            symbol: 종목코드
            decision: 판단 결과
            confidence: 신뢰도
            reason: 판단 근거
        """
        self.logger.info(
            f"🤖 AI판단: {symbol} | {decision} | 신뢰도:{confidence:.1f}% | {reason}"
        )
    
    def log_risk_event(self, event_type: str, symbol: str, details: str):
        """리스크 이벤트 로깅
        
        Args:
            event_type: 이벤트 유형
            symbol: 종목코드
            details: 상세 내용
        """
        self.logger.warning(
            f"⚠️  리스크: {event_type} | {symbol} | {details}"
        )
    
    def log_emergency_action(self, action: str, reason: str):
        """긴급 조치 로깅
        
        Args:
            action: 조치 내용
            reason: 조치 사유
        """
        self.logger.critical(
            f"🚨 긴급조치: {action} | 사유: {reason}"
        )

# 전역 로거 인스턴스
_main_logger = None
_performance_logger = None
_trading_logger = None

def get_logger() -> logging.Logger:
    """메인 로거 반환"""
    global _main_logger
    if _main_logger is None:
        _main_logger = setup_logger()
    return _main_logger

def get_performance_logger() -> PerformanceLogger:
    """성능 로거 반환"""
    global _performance_logger
    if _performance_logger is None:
        _performance_logger = PerformanceLogger(get_logger())
    return _performance_logger

def get_trading_logger() -> TradingLogger:
    """매매 로거 반환"""
    global _trading_logger
    if _trading_logger is None:
        _trading_logger = TradingLogger(get_logger())
    return _trading_logger

def setup_logging(level: str = "INFO", log_file: Optional[str] = None, config_manager=None):
    """로깅 시스템 초기화
    
    Args:
        level: 로그 레벨
        log_file: 로그 파일 경로
        config_manager: 설정 관리자
    """
    global _main_logger
    if log_file is None:
        log_file = "logs/stockbot.log"
    _main_logger = setup_logger(level=level, log_file=log_file, config_manager=config_manager)