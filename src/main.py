# -*- coding: utf-8 -*-
"""
메인 애플리케이션

StockBot 메인 실행 모듈
"""

import asyncio
import signal
import sys
from datetime import datetime
from typing import Optional

from .utils.logger import get_logger, setup_logging
from .utils.config_manager import ConfigManager
from .utils.database_manager import DatabaseManager
from .utils.scheduler import MasterDataScheduler

from .data_collection import NewsCollector, KISApi, MarketDataManager
from .ai_analysis import DecisionEngine, PromptManager, RiskAnalyzer
from .trading import OrderManager, PositionManager, ExecutionEngine
from .monitoring import PerformanceMonitor, TelegramBot, AlertManager
from .monitoring.alert_manager import AlertType, AlertSeverity

class StockBot:
    """
    StockBot 메인 클래스
    
    모든 모듈을 통합하여 자동 매매 시스템을 운영
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """StockBot 초기화
        
        Args:
            config_path: 설정 파일 경로
        """
        # 설정 관리자
        self.config = ConfigManager(config_path)
        
        # 로깅 설정
        setup_logging(config_manager=self.config)
        self.logger = get_logger()
        
        # 데이터베이스 관리자
        db_path = self.config.get('database', {}).get('path', 'data/stockbot.db')
        self.db = DatabaseManager(db_path, self.config)
        
        # AI 분석 카운터 (테스트용 - 처음 몇 개 뉴스만 분석)
        self.news_analysis_count = 0
        self.max_news_analysis = 0
        
        # 테스트용: 시작 시 뉴스 테이블 초기화
        #asyncio.create_task(self._clear_news_table_for_test())
        
        # 한국투자증권 API
        self.kis_api = KISApi(self.config)
        
        # 시장 데이터 관리자
        self.market_data_manager = MarketDataManager(self.config, self.db, self.kis_api)
        
        # 뉴스 수집기
        self.news_collector = NewsCollector(self.config, self.db)
        
        # AI 분석 모듈
        self.prompt_manager = PromptManager(self.config)
        self.risk_analyzer = RiskAnalyzer(self.config, self.db)
        self.decision_engine = DecisionEngine(
            self.config, self.db, self.risk_analyzer
        )
        
        # 거래 모듈
        self.order_manager = OrderManager(self.config, self.db, self.kis_api, self.risk_analyzer)
        self.position_manager = PositionManager(self.config, self.db, self.kis_api)
        self.execution_engine = ExecutionEngine(
            self.config, self.db, self.kis_api, self.order_manager, self.position_manager
        )
        
        # 모니터링 모듈
        self.performance_monitor = PerformanceMonitor(self.config, self.db)
        self.telegram_bot = TelegramBot(self.config, self.db)
        self.alert_manager = AlertManager(self.config, self.db)
        
        # 스케줄러 (마스터 데이터 자동 업데이트용)
        self.master_scheduler = MasterDataScheduler(self.config, self.market_data_manager.stock_info_source)
        
        # 시스템 상태
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 메인 루프 태스크
        self.main_task: Optional[asyncio.Task] = None
        self.decision_task: Optional[asyncio.Task] = None
        
    
    async def initialize(self):
        """StockBot 초기화"""
        try:
            self.logger.info("StockBot 초기화 시작")
            
            # 데이터베이스 초기화
            await self.db.initialize()
            
            # 한국투자증권 API 초기화
            await self.kis_api.initialize()
            
            # 알림 관리자 초기화 (가장 먼저)
            await self.alert_manager.initialize()
            
            # 텔레그램 봇 초기화
            await self.telegram_bot.initialize()
            
            # 알림 콜백 연결
            self.alert_manager.add_alert_callback(self._handle_alert)
            
            # 시장 데이터 관리자 초기화
            await self.market_data_manager.initialize()
            
            # 뉴스 수집기 초기화
            await self.news_collector.initialize()
            
            # 메모리 캐시 초기화
            await self.news_collector._cleanup_memory_cache()
            
            # 거래 모듈 초기화
            await self.order_manager.initialize()
            await self.position_manager.initialize()
            await self.execution_engine.initialize()
            
            # AI 분석 모듈 초기화
            await self.decision_engine.initialize()
            
            # 성능 모니터 초기화
            await self.performance_monitor.initialize()
            
            # 데이터 콜백 연결
            self._setup_data_callbacks()
            
            self.logger.info("StockBot 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"StockBot 초기화 오류: {e}")
            raise
    
    def _setup_data_callbacks(self):
        """데이터 콜백 설정"""
        try:
            # 시장 데이터 콜백
            self.market_data_manager.add_data_callback(self._handle_market_data)
            
            # 뉴스 데이터 콜백
            self.news_collector.add_news_callback(self._handle_news_data)
            
            # 주문 체결 콜백
            self.order_manager.add_fill_callback(self._handle_order_fill)
            
            # 포지션 업데이트 콜백
            self.position_manager.add_position_callback(self._handle_position_update)
            
            self.logger.info("데이터 콜백 설정 완료")
        
        except Exception as e:
            self.logger.error(f"데이터 콜백 설정 오류: {e}")
    
    async def _handle_alert(self, alert):
        """알림 처리
        
        Args:
            alert: 알림 객체
        """
        try:
            # 텔레그램으로 알림 전송
            alert_data = {
                'severity': alert.severity.value,
                'message': f"{alert.title}: {alert.message}"
            }
            
            await self.telegram_bot.send_alert(alert_data)
            
            # 중요한 알림의 경우 추가 처리
            if alert.severity.value in ['high', 'critical']:
                # 긴급 상황 분석
                emergency_analysis = await self.decision_engine.analyze_emergency_situation({
                    'alert_type': alert.type.value,
                    'severity': alert.severity.value,
                    'message': alert.message,
                    'metadata': alert.metadata
                })
                
                if emergency_analysis.get('action') == 'liquidate_all':
                    # 전체 포지션 청산
                    await self.execution_engine.liquidate_all_positions("emergency")
        
        except Exception as e:
            self.logger.error(f"알림 처리 오류: {e}")
    
    async def _handle_market_data(self, data_type: str, data):
        """시장 데이터 처리
        
        Args:
            data_type: 데이터 타입
            data: 시장 데이터
        """
        try:
            # 급격한 가격 변동 감지
            symbol = data.get('symbol')
            price_change_rate = data.get('price_change_rate', 0)
            
            if abs(price_change_rate) > 5.0:  # 5% 이상 변동
                await self.alert_manager.create_alert(
                    alert_type=AlertType.MARKET,
                    severity=AlertSeverity.HIGH,
                    title=f"{symbol} 급격한 가격 변동",
                    message=f"{symbol} 가격이 {price_change_rate:+.2f}% 변동했습니다",
                    source="market_data",
                    metadata={'symbol': symbol, 'price_change_rate': price_change_rate}
                )
        
        except Exception as e:
            self.logger.error(f"시장 데이터 처리 오류: {e}")
    
    async def _clear_news_table_for_test(self):
        """테스트용: 뉴스 테이블 데이터 초기화"""
        try:
            await asyncio.sleep(1)  # 초기화 완료 후 실행
            await self.db.execute("DELETE FROM news_data")
            self.logger.info("테스트용: news_data 테이블 데이터 초기화 완료")
        except Exception as e:
            self.logger.error(f"news_data 테이블 초기화 오류: {e}")
    
    async def _handle_news_data(self, news):
        """뉴스 데이터 처리 및 AI 분석
        
        Args:
            news: 뉴스 데이터
        """
        try:
            # 첫 번째 뉴스만 AI 분석 (테스트용)
            if self.news_analysis_count >= self.max_news_analysis:
                self.logger.debug(f"뉴스 AI 분석 제한 도달 ({self.max_news_analysis}개), 건너뜀: {news.get('title', '')[:50]}...")
                return
           

            self.news_analysis_count += 1
            
            # AI 분석 수행
            self.logger.info(f"뉴스 AI 분석 시작 ({self.news_analysis_count}/{self.max_news_analysis}): {news.get('title', '')[:50]}...")
            
            analysis_result = await self.decision_engine.analyze_news_impact(news)
            
            # 분석 결과 확인 및 로깅
            if analysis_result:
                self.logger.debug(f"AI 분석 결과 수신: {analysis_result}")
                title = news.get('title', '')
                
                # 뉴스 분석 결과를 데이터베이스 형식에 맞게 변환
                # analyze_news_impact는 뉴스 감정 분석 결과를 반환하므로 매매 관련 필드는 None으로 설정
                # affected_stock이 있으면 해당 종목을 symbol로 사용, 없으면 NEWS_ANALYSIS 사용
                affected_stock = analysis_result.get('affected_stock', '')
                symbol_to_use = affected_stock if affected_stock else 'NEWS_ANALYSIS'
                
                db_data = {
                    'symbol': symbol_to_use,  # 영향받는 종목이 있으면 해당 종목, 없으면 뉴스 분석 표시
                    'decision': analysis_result.get('decision', '중립'),
                    'confidence': float(analysis_result.get('confidence', 0)),  # 0-1 범위 그대로 사용
                    'reasoning': analysis_result.get('reasoning', ''),
                    'source_type': 'news',    # 데이터 소스 타입
                    'source_id': news.get('id'),  # 뉴스 ID
                    # 뉴스 분석 결과를 별도 컬럼에 저장
                    'sentiment_score': analysis_result.get('sentiment_score', 0),
                    'market_impact': analysis_result.get('market_impact', 'medium'),
                    'affected_sector': analysis_result.get('affected_sector', ''),
                    'affected_stock': analysis_result.get('affected_stock', ''),
                    'time_horizon': analysis_result.get('time_horizon', 'medium'),
                    'input_data': {
                        'news_url': news.get('url'),
                        'news_title': news.get('title'),
                        'news_source': news.get('source'),
                        'news_content': news.get('content', '')[:1000],  # 내용은 1000자로 제한
                        'analysis_timestamp': datetime.now().isoformat()
                    }
                }
                
                # 데이터 검증 및 데이터베이스에 저장
                try:
                    # 필수 필드 검증
                    if not db_data.get('symbol'):
                        db_data['symbol'] = 'NEWS_ANALYSIS'
                    if not db_data.get('decision'):
                        db_data['decision'] = 'neutral'
                    if db_data.get('confidence') is None:
                        db_data['confidence'] = 0.0
                    
                    # confidence 값이 0-1 범위인지 확인
                    confidence_val = float(db_data['confidence'])
                    if confidence_val > 1.0:
                        confidence_val = confidence_val / 100.0  # 100 스케일을 1 스케일로 변환
                    db_data['confidence'] = max(0.0, min(1.0, confidence_val))
                    
                    await self.db.insert_ai_decision(db_data)
                    self.logger.info(f"뉴스 AI 분석 및 DB 저장 완료: {title[:50]}... (신뢰도: {db_data['confidence']:.3f})")
                    self.logger.debug(f"저장된 데이터: symbol={db_data['symbol']}, decision={db_data['decision']}, confidence={db_data['confidence']:.3f}, reasoning={db_data['reasoning'][:100]}...")
                except Exception as db_error:
                    import traceback
                    self.logger.error(f"뉴스 분석 결과 DB 저장 실패: {db_error}")
                    self.logger.error(f"스택 트레이스: {traceback.format_exc()}")
                    self.logger.error(f"저장하려던 데이터: {db_data}")
                    # DB 저장 실패해도 분석은 완료되었으므로 계속 진행
                    self.logger.info(f"뉴스 AI 분석 완료 (DB 저장 실패): {title[:50]}... (신뢰도: {analysis_result.get('confidence', 0):.3f})")
            
                # 중요한 뉴스인 경우 알림
                importance = news.get('importance', 0)
                if importance is None:
                    importance = 0
                
                if float(importance) > 0.8:  # 중요도 80% 이상
                    await self.alert_manager.create_alert(
                        alert_type=AlertType.MARKET,
                        severity=AlertSeverity.MEDIUM,
                        title="중요 뉴스 발생",
                        message=f"{news.get('title', '')[:100]}...",
                        source="news_collector",
                        metadata={
                            'url': news.get('url'),
                            'importance': importance,
                            'related_stocks': news.get('related_stocks', [])
                        }
                    )
            else:
                # AI 분석 결과가 None인 경우 로깅
                title = news.get('title', 'N/A')
                self.logger.warning(f"뉴스 AI 분석 결과가 None: {title[:50]}... (URL: {news.get('url', 'N/A')})")
                self.logger.debug(f"분석 실패한 뉴스 상세정보: source={news.get('source')}, published_at={news.get('published_at')}, content_length={len(news.get('content', ''))}자")
        
            self.news_analysis_count -= 1
            
        except Exception as e:
            import traceback
            self.logger.error(f"뉴스 데이터 처리 오류: {e}")
            self.logger.error(f"오류 상세 정보: {traceback.format_exc()}")
            # 뉴스 정보도 함께 로깅하여 디버깅에 도움
            self.logger.error(f"처리 중이던 뉴스: {news.get('title', 'N/A')[:100]}...")
            self.news_analysis_count -= 1
    
    
    async def _handle_order_fill(self, order):
        """주문 체결 처리
        
        Args:
            order: 체결된 주문
        """
        try:
            # 포지션 관리자에 체결 정보 전달
            await self.position_manager.update_from_order(order)
            
            # 성능 모니터에 거래 정보 전달
            await self.performance_monitor.record_trade(order)
            
            # 텔레그램 알림
            trade_data = {
                'symbol': order.get('symbol'),
                'side': order.get('side'),
                'quantity': order.get('quantity'),
                'price': order.get('filled_price'),
                'pnl': order.get('pnl', 0),
                'confidence': order.get('confidence', 0)
            }
            
            await self.telegram_bot.send_trade_notification(trade_data)
        
        except Exception as e:
            self.logger.error(f"주문 체결 처리 오류: {e}")
    
    async def _handle_position_update(self, position):
        """포지션 업데이트 처리
        
        Args:
            position: 업데이트된 포지션
        """
        try:
            # 성능 모니터에 포지션 정보 전달
            await self.performance_monitor.update_position(position)
            
            # 큰 손실 포지션 감지
            unrealized_pnl_rate = position.get('unrealized_pnl_rate', 0)
            
            if unrealized_pnl_rate < -10.0:  # -10% 이상 손실
                await self.alert_manager.create_alert(
                    alert_type=AlertType.TRADING,
                    severity=AlertSeverity.HIGH,
                    title=f"{position.get('symbol')} 큰 손실",
                    message=f"{position.get('symbol')} 포지션에서 {unrealized_pnl_rate:.2f}% 손실 발생",
                    source="position_manager",
                    metadata={
                        'symbol': position.get('symbol'),
                        'unrealized_pnl': position.get('unrealized_pnl'),
                        'unrealized_pnl_rate': unrealized_pnl_rate
                    }
                )
        
        except Exception as e:
            self.logger.error(f"포지션 업데이트 처리 오류: {e}")
    
    async def start(self):
        """StockBot 시작"""
        try:
            if self.is_running:
                self.logger.warning("StockBot이 이미 실행 중입니다")
                return
            
            self.logger.info("StockBot 시작")
            self.start_time = datetime.now()
            self.is_running = True
            
            # 시장 데이터 관리자 시작
            await self.market_data_manager.start_collection()
            
            # 뉴스 수집 시작
            await self.news_collector.start_collection()
            
            # 성능 모니터링 시작
            await self.performance_monitor.start_monitoring()
            
            # 메인 루프 시작
            self.main_task = asyncio.create_task(self._main_loop())
            
            # AI 의사결정 루프 시작
            self.decision_task = asyncio.create_task(self._decision_loop())
            
            # 마스터 데이터 자동 업데이트 스케줄러 시작
            self.master_scheduler.start()
            self.logger.info("마스터 데이터 자동 업데이트 스케줄러 시작")
            
            # 시작 알림
            await self.alert_manager.create_alert(
                alert_type=AlertType.SYSTEM,
                severity=AlertSeverity.LOW,
                title="StockBot 시작",
                message=f"StockBot이 {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}에 시작되었습니다",
                source="main"
            )
            
            self.logger.info("StockBot 시작 완료")
        
        except Exception as e:
            self.logger.error(f"StockBot 시작 오류: {e}")
            await self.stop()
            raise
    
    async def _main_loop(self):
        """메인 루프"""
        while self.is_running:
            try:
                # 시스템 상태 확인
                await self._check_system_health()
                
                # 시장 시간 확인
                if not await self._is_market_open():
                    await asyncio.sleep(60)  # 1분 대기
                    continue
                
                # 리스크 체크
                await self._check_risk_limits()
                
                # 포지션 모니터링
                await self._monitor_positions()
                
                # 주문 상태 확인
                await self._check_order_status()
                
                # 10초 대기
                await asyncio.sleep(10)
            
            except Exception as e:
                self.logger.error(f"메인 루프 오류: {e}")
                await asyncio.sleep(30)
    
    async def _decision_loop(self):
        """AI 의사결정 루프"""
        while self.is_running:
            try:
                # 시장 시간 확인
                if not await self._is_market_open():
                    await asyncio.sleep(60)
                    continue
                
                # AI 의사결정 실행
                decision = await self.decision_engine.make_decision()
                
                if decision and decision.action != 'hold':
                    # 의사결정 실행
                    await self.execution_engine.execute_decision(decision)
                
                # 의사결정 간격 (설정에서 조회)
                decision_interval = self.config.get('ai.decision_interval', 30)
                
                # 대기
                await asyncio.sleep(decision_interval)
            
            except Exception as e:
                self.logger.error(f"의사결정 루프 오류: {e}")
                await asyncio.sleep(60)
    
    async def _check_system_health(self):
        """시스템 상태 확인"""
        try:
            # CPU, 메모리 사용률 확인
            import psutil
            
            cpu_usage = psutil.cpu_percent(interval=1)
            memory_usage = psutil.virtual_memory().percent
            
            # 임계값 확인
            await self.alert_manager.check_metric_threshold('high_cpu_usage', cpu_usage)
            await self.alert_manager.check_metric_threshold('high_memory_usage', memory_usage)
            
            # API 연결 상태 확인
            if not self.kis_api.is_connected():
                await self.alert_manager.create_alert(
                    alert_type=AlertType.SYSTEM,
                    severity=AlertSeverity.HIGH,
                    title="API 연결 끊김",
                    message="한국투자증권 API 연결이 끊어졌습니다",
                    source="system_health"
                )
        
        except Exception as e:
            self.logger.error(f"시스템 상태 확인 오류: {e}")
    
    async def _is_market_open(self) -> bool:
        """시장 개장 여부 확인
        
        Returns:
            시장 개장 여부
        """
        try:
            # 한국투자증권 API를 통해 시장 상태 확인
            market_status = await self.kis_api.get_market_status()
            return market_status.get('is_open', False)
        
        except Exception as e:
            self.logger.error(f"시장 상태 확인 오류: {e}")
            # 기본적으로 평일 9:00-15:30을 시장 시간으로 가정
            now = datetime.now()
            if now.weekday() >= 5:  # 주말
                return False
            
            market_open = now.replace(hour=9, minute=0, second=0).replace(microsecond=0)
            market_close = now.replace(hour=15, minute=30, second=0).replace(microsecond=0)
            
            return market_open <= now <= market_close
    
    async def _check_risk_limits(self):
        """리스크 한도 확인"""
        try:
            # 일일 손익 확인
            daily_pnl = await self.performance_monitor.get_daily_pnl()
            
            # 일일 손실 한도 확인
            daily_loss_limit = self.config.get('risk.daily_loss_limit', -500000)
            
            if daily_pnl < daily_loss_limit:
                await self.alert_manager.create_alert(
                    alert_type=AlertType.RISK,
                    severity=AlertSeverity.CRITICAL,
                    title="일일 손실 한도 초과",
                    message=f"일일 손실이 {daily_pnl:,.0f}원으로 한도({daily_loss_limit:,.0f}원)를 초과했습니다",
                    source="risk_check",
                    metadata={'daily_pnl': daily_pnl, 'limit': daily_loss_limit}
                )
                
                # 모든 포지션 청산
                await self.execution_engine.liquidate_all_positions("daily_loss_limit")
            
            # 포트폴리오 리스크 분석
            risk_analysis = await self.risk_analyzer.analyze_portfolio_risk()
            
            if risk_analysis.get('risk_level') == 'high':
                await self.alert_manager.create_alert(
                    alert_type=AlertType.RISK,
                    severity=AlertSeverity.HIGH,
                    title="높은 포트폴리오 리스크",
                    message=f"포트폴리오 리스크가 높습니다: {risk_analysis.get('risk_score', 0):.2f}",
                    source="risk_analyzer",
                    metadata=risk_analysis
                )
        
        except Exception as e:
            self.logger.error(f"리스크 한도 확인 오류: {e}")
    
    async def _monitor_positions(self):
        """포지션 모니터링"""
        try:
            # 모든 포지션 조회
            positions = await self.position_manager.get_all_positions()
            
            for position in positions:
                if position.get('status') != 'open':
                    continue
                
                # 손절/익절 조건 확인
                symbol = position.get('symbol')
                current_price = await self.market_data_manager.get_latest_price(symbol)
                
                if current_price:
                    # 포지션 업데이트
                    await self.position_manager.update_position_price(symbol, current_price['price'])
                    
                    # 손절/익절 확인
                    should_close, reason = await self.position_manager.check_stop_conditions(symbol)
                    
                    if should_close:
                        # 포지션 청산 주문
                        await self.execution_engine.close_position(symbol, reason)
        
        except Exception as e:
            self.logger.error(f"포지션 모니터링 오류: {e}")
    
    async def _check_order_status(self):
        """주문 상태 확인"""
        try:
            # 미체결 주문 확인
            pending_orders = await self.order_manager.get_pending_orders()
            
            for order in pending_orders:
                # 주문 상태 업데이트
                await self.order_manager.update_order_status(order['id'])
                
                # 오래된 미체결 주문 취소
                order_age = (datetime.now() - order['created_at']).total_seconds()
                max_order_age = self.config.get('trading.max_order_age', 300)  # 5분
                
                if order_age > max_order_age:
                    await self.order_manager.cancel_order(order['id'], "timeout")
        
        except Exception as e:
            self.logger.error(f"주문 상태 확인 오류: {e}")
    
    async def stop(self):
        """StockBot 중지"""
        try:
            if not self.is_running:
                return
            
            self.logger.info("StockBot 중지 시작")
            self.is_running = False
            
            # 메인 태스크 종료
            if self.main_task:
                self.main_task.cancel()
                try:
                    await self.main_task
                except asyncio.CancelledError:
                    pass
            
            if self.decision_task:
                self.decision_task.cancel()
                try:
                    await self.decision_task
                except asyncio.CancelledError:
                    pass
            
            # 모든 포지션 청산 (옵션)
            if self.config.get('trading.close_positions_on_shutdown', True):
                try:
                    await self.position_manager.close_all_positions()
                    self.logger.info("모든 포지션 청산 완료")
                except Exception as e:
                    self.logger.warning(f"포지션 청산 중 오류 (무시됨): {e}")
            
            # 시장 데이터 관리자 중지
            await self.market_data_manager.close()
            
            # 뉴스 수집 중지
            await self.news_collector.stop_collection()
            
            # 성능 모니터링 중지
            await self.performance_monitor.stop_monitoring()
            
            # 마스터 데이터 자동 업데이트 스케줄러 중지
            self.master_scheduler.stop()
            self.logger.info("마스터 데이터 자동 업데이트 스케줄러 중지")
            
            # 모듈 종료 (각각 예외 처리)
            modules_to_close = [
                ("실행 엔진", self.execution_engine),
                ("주문 관리자", self.order_manager),
                ("포지션 관리자", self.position_manager),
                ("의사결정 엔진", self.decision_engine),
                ("뉴스 수집기", self.news_collector),
                ("KIS API", self.kis_api),
                ("성능 모니터", self.performance_monitor)
            ]
            
            for module_name, module in modules_to_close:
                try:
                    if hasattr(module, 'close'):
                        await module.close()
                        self.logger.debug(f"{module_name} 종료 완료")
                except Exception as e:
                    self.logger.warning(f"{module_name} 종료 중 오류 (무시됨): {e}")
            
            # 종료 알림
            await self.alert_manager.create_alert(
                alert_type=AlertType.SYSTEM,
                severity=AlertSeverity.LOW,
                title="StockBot 종료",
                message=f"StockBot이 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}에 종료되었습니다",
                source="main"
            )
            
            # 알림 관리자와 텔레그램 봇 종료 (마지막)
            await self.telegram_bot.close()
            await self.alert_manager.close()
            
            # 데이터베이스 연결 종료
            await self.db.close()
            
            self.logger.info("StockBot 중지 완료")
        
        except Exception as e:
            self.logger.error(f"StockBot 중지 오류: {e}")
    
    async def get_status(self) -> dict:
        """시스템 상태 조회
        
        Returns:
            시스템 상태 정보
        """
        try:
            uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
            
            # 포지션 정보
            positions = await self.position_manager.get_all_positions()
            active_positions = [p for p in positions if p.get('status') == 'open']
            
            # 성능 정보
            performance = await self.performance_monitor.get_current_metrics()
            
            # 알림 정보
            alert_stats = self.alert_manager.get_alert_stats()
            
            return {
                'is_running': self.is_running,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'uptime': uptime,
                'positions': {
                    'total': len(positions),
                    'active': len(active_positions),
                    'total_value': sum(p.get('market_value', 0) for p in active_positions)
                },
                'performance': performance,
                'alerts': alert_stats,
                'modules': {
                    'market_data_manager': self.market_data_manager.is_collecting(),
                    'news_collector': self.news_collector.is_collecting(),
                    'kis_api': self.kis_api.is_connected(),
                    'telegram_bot': self.telegram_bot.is_connected()
                },
                'scheduler': self.master_scheduler.get_job_status()
            }
        
        except Exception as e:
            self.logger.error(f"상태 조회 오류: {e}")
            return {'error': str(e)}
    
    async def emergency_stop(self):
        """긴급 중지"""
        try:
            self.logger.warning("긴급 중지 실행")
            
            # 모든 포지션 즉시 청산
            try:
                await self.position_manager.close_all_positions()
                self.logger.info("긴급 포지션 청산 완료")
            except Exception as e:
                self.logger.warning(f"긴급 포지션 청산 중 오류 (무시됨): {e}")
            
            # 모든 미체결 주문 취소
            await self.order_manager.cancel_all_orders("emergency")
            
            # 긴급 알림
            await self.alert_manager.create_alert(
                alert_type=AlertType.SYSTEM,
                severity=AlertSeverity.CRITICAL,
                title="긴급 중지",
                message="시스템 긴급 중지가 실행되었습니다",
                source="emergency"
            )
            
            # 시스템 중지
            await self.stop()
        
        except Exception as e:
            self.logger.error(f"긴급 중지 오류: {e}")

async def main():
    """메인 함수"""
    stockbot = None
    
    try:
        # StockBot 생성 및 초기화
        stockbot = StockBot()
        await stockbot.initialize()
        
        # StockBot 시작
        await stockbot.start()
        
        # 무한 대기 (Ctrl+C로 종료)
        while stockbot.is_running:
            await asyncio.sleep(1)
        
    except KeyboardInterrupt:
        print("\n사용자에 의한 중단 - 정상 종료 중...")
        
    except Exception as e:
        print(f"오류 발생: {e}")
        
    finally:
        if stockbot:
            try:
                print("시스템 종료 중...")
                await stockbot.stop()
                
                # 모든 비동기 작업이 완료될 때까지 잠시 대기
                await asyncio.sleep(0.5)
                
                print("시스템 종료 완료")
            except Exception as e:
                print(f"종료 중 오류: {e}")
            finally:
                # 남은 태스크들을 정리
                try:
                    # 현재 태스크를 제외한 남은 태스크들만 가져오기
                    current_task = asyncio.current_task()
                    pending_tasks = [
                        task for task in asyncio.all_tasks() 
                        if not task.done() and task != current_task
                    ]
                    
                    if pending_tasks:
                        print(f"남은 태스크 {len(pending_tasks)}개 취소 중...")
                        # 태스크들을 개별적으로 취소
                        for task in pending_tasks:
                            try:
                                task.cancel()
                            except Exception:
                                pass  # 이미 완료된 태스크는 무시
                        
                        # 짧은 대기 후 강제 종료
                        try:
                            await asyncio.wait_for(
                                asyncio.gather(*pending_tasks, return_exceptions=True),
                                timeout=1.0
                            )
                        except asyncio.TimeoutError:
                            print("일부 태스크가 시간 내에 종료되지 않았습니다 (무시됨)")
                        except Exception:
                            pass  # 기타 오류는 무시
                            
                except Exception as e:
                    print(f"태스크 정리 중 오류 (무시됨): {e}")

if __name__ == "__main__":
    # 이벤트 루프 실행
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n프로그램이 정상적으로 종료되었습니다.")
    except Exception as e:
        print(f"실행 오류: {e}")
        sys.exit(1)
    else:
        print("프로그램이 정상적으로 종료되었습니다.")