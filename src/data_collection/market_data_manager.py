# -*- coding: utf-8 -*-
"""
시장 데이터 통합 관리자

모든 시장 데이터 소스들을 통합 관리하는 단일 진입점
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable

from .kis_api import KISApi
from .market_sources import RealtimeSource, StockInfoSource, OrderbookSource
from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager

class MarketDataManager:
    """
    시장 데이터 통합 관리자
    
    실시간 시세, 주식 정보, 호가 데이터 등 모든 시장 데이터 소스를 통합 관리
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, kis_api: KISApi = None):
        """시장 데이터 관리자 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
            kis_api: KIS API 인스턴스 (선택사항)
        """
        self.config = config_manager
        self.db = db_manager
        self.logger = get_logger()
        
        # KIS API 초기화
        self.kis_api = kis_api if kis_api is not None else KISApi(config_manager)
        
        # 데이터 소스들 초기화
        self.realtime_source = RealtimeSource(config_manager, db_manager, self.kis_api)
        self.stock_info_source = StockInfoSource(config_manager, db_manager)
        self.orderbook_source = OrderbookSource(config_manager, db_manager, self.kis_api)
        
        # 콜백 관리
        self.data_callbacks = []
        
        # 수집 상태
        self.is_collecting = False
        
        # 성능 메트릭
        self.metrics = {
            'total_data_points': 0,
            'last_update_time': None,
            'active_sources': [],
            'errors_count': 0
        }
        
        # 소스별 콜백 설정
        self._setup_source_callbacks()
    
    def _setup_source_callbacks(self):
        """각 데이터 소스의 콜백 설정"""
        # 실시간 데이터 콜백
        self.realtime_source.add_data_callback(self._handle_realtime_data)
        
        # 주식 정보 업데이트 콜백
        self.stock_info_source.add_update_callback(self._handle_stock_info_update)
        
        # 호가 데이터 콜백
        self.orderbook_source.add_data_callback(self._handle_orderbook_data)
    
    async def _handle_realtime_data(self, data_type: str, data: Dict[str, Any]):
        """실시간 데이터 처리
        
        Args:
            data_type: 데이터 타입
            data: 데이터
        """
        # 외부 콜백 호출
        for callback in self.data_callbacks:
            try:
                await callback('realtime_data', data)
            except Exception as e:
                self.logger.error(f"실시간 데이터 콜백 오류: {e}")
        
        # 메트릭 업데이트
        self.metrics['total_data_points'] += 1
        self.metrics['last_update_time'] = datetime.now()
    
    async def _handle_stock_info_update(self, update_type: str, result: Dict[str, Any]):
        """주식 정보 업데이트 처리
        
        Args:
            update_type: 업데이트 타입
            result: 업데이트 결과
        """
        # 외부 콜백 호출
        for callback in self.data_callbacks:
            try:
                await callback('stock_info_updated', {
                    'update_type': update_type,
                    'result': result
                })
            except Exception as e:
                self.logger.error(f"주식 정보 업데이트 콜백 오류: {e}")
        
        # 메트릭 업데이트
        self.metrics['last_update_time'] = datetime.now()
    
    async def _handle_orderbook_data(self, data_type: str, data: Dict[str, Any]):
        """호가 데이터 처리
        
        Args:
            data_type: 데이터 타입
            data: 데이터
        """
        # 외부 콜백 호출
        for callback in self.data_callbacks:
            try:
                await callback('orderbook_data', data)
            except Exception as e:
                self.logger.error(f"호가 데이터 콜백 오류: {e}")
        
        # 메트릭 업데이트
        self.metrics['total_data_points'] += 1
        self.metrics['last_update_time'] = datetime.now()
    
    async def initialize(self):
        """시장 데이터 관리자 초기화"""
        self.logger.info("시장 데이터 관리자 초기화 시작")
        
        try:
            # KIS API 초기화
            if not self.kis_api.connected:
                await self.kis_api.initialize()
            
            # 각 데이터 소스 초기화
            await self.realtime_source.initialize()
            await self.stock_info_source.initialize()
            await self.orderbook_source.initialize()
            
            self.logger.info("시장 데이터 관리자 초기화 완료")
            
        except Exception as e:
            self.logger.error(f"시장 데이터 관리자 초기화 오류: {e}")
            raise
    
    def add_data_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """데이터 수신 콜백 추가
        
        Args:
            callback: 데이터 수신 시 호출될 콜백 함수
        """
        self.data_callbacks.append(callback)
    
    async def start_collection(self, realtime: bool = True, stock_info: bool = True, orderbook: bool = True):
        """데이터 수집 시작
        
        Args:
            realtime: 실시간 데이터 수집 여부
            stock_info: 주식 정보 수집 여부
            orderbook: 호가 데이터 수집 여부
        """
        if self.is_collecting:
            self.logger.warning("데이터 수집이 이미 진행 중입니다")
            return
        
        self.logger.info("시장 데이터 수집 시작")
        self.is_collecting = True
        
        try:
            active_sources = []
            
            # 실시간 데이터 수집 시작
            if realtime:
                await self.realtime_source.start_collection()
                active_sources.append('realtime')
            
            # 주식 정보 업데이트 시작
            if stock_info:
                self.logger.info("주식 정보 업데이트 시작")
                await self.stock_info_source.update_stock_info()
                active_sources.append('stock_info')
            
            # 호가 데이터 수집 시작
            if orderbook:
                await self.orderbook_source.start_collection()
                active_sources.append('orderbook')
            
            self.metrics['active_sources'] = active_sources
            
        except Exception as e:
            self.logger.error(f"데이터 수집 시작 오류: {e}")
            self.is_collecting = False
            raise
    
    async def stop_collection(self):
        """데이터 수집 중지"""
        if not self.is_collecting:
            return
        
        self.logger.info("시장 데이터 수집 중지")
        self.is_collecting = False
        
        # 각 소스의 수집 중지
        await self.realtime_source.stop_collection()
        await self.orderbook_source.stop_collection()
        
        self.metrics['active_sources'] = []
    
    # === 실시간 데이터 관련 메서드 ===
    
    async def add_symbol(self, symbol: str):
        """실시간 모니터링 종목 추가
        
        Args:
            symbol: 종목 코드
        """
        await self.realtime_source.add_symbol(symbol)
        await self.orderbook_source.add_symbol(symbol)
    
    async def remove_symbol(self, symbol: str):
        """실시간 모니터링 종목 제거
        
        Args:
            symbol: 종목 코드
        """
        await self.realtime_source.remove_symbol(symbol)
        await self.orderbook_source.remove_symbol(symbol)
    
    async def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """최신 가격 정보 조회
        
        Args:
            symbol: 종목 코드
            
        Returns:
            최신 가격 정보 또는 None
        """
        return await self.realtime_source.get_latest_price(symbol)
    
    async def get_latest_orderbook(self, symbol: str) -> Optional[Dict[str, Any]]:
        """최신 호가 정보 조회
        
        Args:
            symbol: 종목 코드
            
        Returns:
            최신 호가 정보 또는 None
        """
        return await self.orderbook_source.get_latest_orderbook(symbol)
    
    async def get_price_history(self, symbol: str, hours: int = 24) -> List[Dict[str, Any]]:
        """가격 히스토리 조회
        
        Args:
            symbol: 종목 코드
            hours: 조회할 시간 (시간)
            
        Returns:
            가격 히스토리 리스트
        """
        return await self.realtime_source.get_price_history(symbol, hours)
    
    # === 주식 정보 관련 메서드 ===
    
    async def update_stock_basic_info(self, symbols: List[str] = None) -> Dict[str, Any]:
        """주식 기본 정보 업데이트
        
        Args:
            symbols: 업데이트할 종목 리스트 (None이면 전체)
            
        Returns:
            업데이트 결과
        """
        return await self.stock_info_source.update_basic_info(symbols)
    
    async def update_daily_stock_info(self, symbols: List[str] = None, days: int = 30) -> Dict[str, Any]:
        """일별 주가 정보 업데이트
        
        Args:
            symbols: 업데이트할 종목 리스트 (None이면 전체)
            days: 조회할 일수
            
        Returns:
            업데이트 결과
        """
        return await self.stock_info_source.update_daily_info(symbols, days)
    
    async def get_stock_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """종목 정보 조회
        
        Args:
            symbol: 종목 코드
            
        Returns:
            종목 정보 또는 None
        """
        return await self.stock_info_source.get_stock_info(symbol)
    
    async def get_stock_update_status(self) -> Dict[str, Any]:
        """주식 정보 업데이트 상태 조회
        
        Returns:
            업데이트 상태 정보
        """
        return await self.stock_info_source.get_update_status()
    
    # === 통합 상태 및 메트릭 ===
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """시장 데이터 요약 정보 조회
        
        Returns:
            시장 데이터 요약
        """
        try:
            # 각 소스의 메트릭 수집
            realtime_metrics = self.realtime_source.get_metrics()
            stock_info_metrics = self.stock_info_source.get_metrics()
            orderbook_metrics = self.orderbook_source.get_metrics()
            
            return {
                'is_collecting': self.is_collecting,
                'active_sources': self.metrics['active_sources'],
                'total_data_points': self.metrics['total_data_points'],
                'last_update_time': self.metrics['last_update_time'].isoformat() if self.metrics['last_update_time'] else None,
                'realtime': {
                    'data_count': realtime_metrics.get('realtime_data_count', 0),
                    'active_symbols': realtime_metrics.get('active_symbols_count', 0),
                    'last_data_time': realtime_metrics.get('last_data_time').isoformat() if realtime_metrics.get('last_data_time') else None
                },
                'stock_info': {
                    'updates_count': stock_info_metrics.get('stock_info_updates', 0),
                    'last_update_time': stock_info_metrics.get('last_update_time').isoformat() if stock_info_metrics.get('last_update_time') else None,
                    'total_symbols_processed': stock_info_metrics.get('total_symbols_processed', 0)
                },
                'orderbook': {
                    'updates_count': orderbook_metrics.get('orderbook_updates', 0),
                    'active_symbols': orderbook_metrics.get('active_symbols_count', 0),
                    'last_update_time': orderbook_metrics.get('last_update_time').isoformat() if orderbook_metrics.get('last_update_time') else None
                },
                'errors': {
                    'total': self.metrics['errors_count'],
                    'realtime': realtime_metrics.get('errors_count', 0),
                    'stock_info': stock_info_metrics.get('errors_count', 0),
                    'orderbook': orderbook_metrics.get('errors_count', 0)
                }
            }
            
        except Exception as e:
            self.logger.error(f"시장 데이터 요약 조회 오류: {e}")
            return {'error': str(e)}
    
    def is_collecting_data(self) -> bool:
        """데이터 수집 상태 확인
        
        Returns:
            수집 중 여부
        """
        return self.is_collecting
    
    async def close(self):
        """리소스 정리"""
        self.logger.info("시장 데이터 관리자 종료")
        
        # 데이터 수집 중지
        await self.stop_collection()
        
        # 각 소스 정리
        await self.realtime_source.close()
        await self.stock_info_source.close()
        await self.orderbook_source.close()
        
        # 콜백 정리
        self.data_callbacks.clear()