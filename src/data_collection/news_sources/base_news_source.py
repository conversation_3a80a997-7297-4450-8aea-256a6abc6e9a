# -*- coding: utf-8 -*-
"""
기본 뉴스 소스 인터페이스

모든 뉴스 소스가 구현해야 하는 기본 인터페이스를 정의합니다.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from venv import logger
import aiohttp
import asyncio
import feedparser
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs

from ...utils.logger import get_logger
from ...utils.config_manager import ConfigManager

class BaseNewsSource(ABC):
    """
    뉴스 소스 기본 클래스
    
    모든 뉴스 소스는 이 클래스를 상속받아 구현해야 합니다.
    """
    
    def __init__(self, config_manager: ConfigManager, source_config: Dict[str, Any]):
        """
        뉴스 소스 초기화
        
        Args:
            config_manager: 설정 관리자
            source_config: 소스별 설정 정보
        """
        self.config = config_manager
        self.source_config = source_config
        self.logger = get_logger()
        
        # 소스 기본 정보
        self.name = source_config.get('name', 'Unknown')
        self.url = source_config.get('url', '')
        self.source_type = source_config.get('type', 'unknown')
        
        # 수집 설정
        self.max_articles = source_config.get('max_articles', 50)
        self.timeout = source_config.get('timeout', 30)
        self.encoding = source_config.get('encoding', 'utf-8')
        self.use_summary = source_config.get('use_summary', True)
        self.detail_fetch = source_config.get('detail_fetch', False)
        self.semaphore_limit = source_config.get('semaphore_limit', 15)
        
        # HTTP 세션 (외부에서 주입)
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 처리된 URL 캐시 (외부에서 주입)
        self.processed_urls: Optional[set] = None
        
        # 커스텀 파서 함수들 (서브클래스에서 설정)
        self.custom_url_converter: Optional[Callable] = None
        self.custom_item_parser: Optional[Callable] = None
        self.custom_content_fetcher: Optional[Callable] = None
        
        # 모바일 User-Agent 설정
        self.mobile_user_agent = (
            "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) "
            "AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 "
            "Mobile/15E148 Safari/604.1"
        )
        
        # HTTP 헤더 설정
        self.headers = {
            'User-Agent': self.mobile_user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ko-KR,ko;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def set_session(self, session: aiohttp.ClientSession):
        """
        HTTP 세션 설정
        
        Args:
            session: aiohttp 세션
        """
        self.session = session
    
    def set_processed_urls(self, processed_urls: set):
        """
        처리된 URL 캐시 설정
        
        Args:
            processed_urls: 처리된 URL 집합
        """
        self.processed_urls = processed_urls
    
    @abstractmethod
    async def collect_news(self) -> List[Dict[str, Any]]:
        """
        뉴스 수집 (추상 메서드)
        
        각 소스별로 구현해야 하는 메서드입니다.
        
        Returns:
            수집된 뉴스 기사 리스트 (공통 포맷)
            [
                {
                    'title': str,           # 제목
                    'url': str,             # URL
                    'published': datetime,  # 발행일시
                    'content': str,         # 내용 (요약)
                    'source': self.name      # 소스
                },
                ...
            ]
        """
        pass
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """
        날짜 문자열을 datetime 객체로 변환 (한국시간으로 변환)
        
        Args:
            date_str: 날짜 문자열
        
        Returns:
            한국시간으로 변환된 datetime 객체 또는 None
        """
        if not date_str:
            # 빈 문자열이면 None 반환
            return None
        
        # 다양한 날짜 형식 지원
        date_formats = [
            '%a, %d %b %Y %H:%M:%S %z',  # RFC 2822 (timezone 포함)
            '%Y-%m-%d %H:%M:%S',         # 일반적인 형식
            '%Y-%m-%dT%H:%M:%S',         # ISO 형식
            '%Y-%m-%dT%H:%M:%SZ',        # ISO UTC
            '%Y.%m.%d %H:%M',            # 한국 형식
            '%Y-%m-%d',                  # 날짜만
        ]
        
        from datetime import timezone, timedelta
        kst = timezone(timedelta(hours=9))  # 한국시간 (UTC+9)
        
        for fmt in date_formats:
            try:
                parsed_dt = datetime.strptime(date_str, fmt)
                
                # timezone 정보가 있는 경우 한국시간으로 변환 후 naive datetime으로 반환
                if parsed_dt.tzinfo is not None:
                    # timezone 정보가 있으면 한국시간으로 변환 후 timezone 정보 제거
                    kst_dt = parsed_dt.astimezone(kst)
                    return kst_dt.replace(tzinfo=None)
                else:
                    # timezone 정보가 없으면 그대로 반환 (한국시간으로 간주)
                    return parsed_dt
                    
            except ValueError:
                continue
        
        # 파싱 실패시 None 반환
        self.logger.debug(f"날짜 파싱 실패: {date_str}")
        return None
    
    async def _fetch_article_content(self, url: str) -> str:
        """
        기사 URL에서 본문 내용을 스크래핑
        
        Args:
            url: 기사 URL
            
        Returns:
            기사 본문 내용
        """
        try:
            if not self.session:
                async with aiohttp.ClientSession() as session:
                    return await self._scrape_content(session, url)
            else:
                return await self._scrape_content(self.session, url)
        except Exception as e:
            self.logger.debug(f"본문 스크래핑 오류 ({url}): {e}")
            return ""
    
    async def _scrape_content(self, session: aiohttp.ClientSession, url: str) -> str:
        """
        실제 웹 스크래핑 수행
        
        Args:
            session: HTTP 세션
            url: 기사 URL
            
        Returns:
            스크래핑된 본문 내용
        """
        try:
            async with session.get(url, headers=self.headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    html = await response.text()
                    
                    # BeautifulSoup으로 HTML 파싱
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # 일반적인 기사 본문 선택자들 시도
                    content_selectors = [
                        'article',
                        '.article-content',
                        '.news-content', 
                        '.content',
                        '.article-body',
                        '.news-body',
                        '#article-content',
                        '#news-content',
                        'div[class*="content"]',
                        'div[class*="article"]',
                        'p'
                    ]
                    
                    content = ""
                    for selector in content_selectors:
                        elements = soup.select(selector)
                        if elements:
                            # 텍스트 추출 및 정리
                            texts = []
                            for elem in elements:
                                text = elem.get_text(strip=True)
                                if text and len(text) > 50:  # 의미있는 길이의 텍스트만
                                    texts.append(text)
                            
                            if texts:
                                content = ' '.join(texts)
                                break
                    
                    return self._clean_text(content) if content else ""
                    
        except Exception as e:
            self.logger.debug(f"스크래핑 오류 ({url}): {e}")
            return ""
    
    def _clean_text(self, text: str) -> str:
        """
        텍스트 정리
        
        Args:
            text: 원본 텍스트
        
        Returns:
            정리된 텍스트
        """
        if not text:
            return ''
        
        # HTML 태그 제거, 공백 정리
        import re
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
    

    
    def _is_valid_article(self, article: Dict[str, Any]) -> bool:
        """
        유효한 기사인지 확인
        
        Args:
            article: 기사 정보
        
        Returns:
            유효성 여부
        """
        # 필수 필드 확인
        required_fields = ['title', 'url']
        for field in required_fields:
            if not article.get(field):
                return False
        
        # URL 키 기반 중복 확인
        if self.processed_urls:
            if article['url'] in self.processed_urls:
                self.logger.debug(f"중복 기사 발견: {article['url']}")
                return False
        
        # 제목 길이 확인
        if len(article['title']) < 5:
            return False
        
        return True
    
    async def collect_rss_web_news(self, parse_function: Callable = None) -> List[Dict[str, Any]]:
        """
        뉴스 수집 공통 로직 (RSS와 웹 스크래핑 모두 지원, 파싱 함수를 파라미터로 받음)
        
        Args:
            parse_function: 파싱 함수 (기본값: RSS는 self._parse_rss, 웹은 필수)
            
        Returns:
            수집된 뉴스 기사 리스트
        """
        # 파싱 함수가 필수로 전달되어야 함
        if parse_function is None:
            raise ValueError(f"parse_function이 필수입니다 ({self.name})")
            
        articles = []
        
        try:
            async with self.session.get(self.url, headers=self.headers, timeout=self.timeout) as response:
                if response.status != 200:
                    self.logger.warning(f"{self.source_type.upper()} 응답 오류 ({self.name}): {response.status}")
                    return articles
                
                content = await response.text(encoding=self.encoding if self.source_type == 'RSS' else None)

                parsed_articles = []
                
                if self.source_type == 'RSS':
                    # RSS 파싱
                    feed = feedparser.parse(content)

                    # 뉴스 기사 파싱 - entries 리스트를 파싱 함수에 전달
                    parsed_articles = await parse_function(feed.entries)
                
                elif self.source_type == 'WEB':
                    # 웹 스크래핑
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    # 뉴스 기사 파싱
                    parsed_articles = await parse_function(soup)

                
                # 파싱된 기사들에 대해 유효성 검증 수행
                for article in parsed_articles:
                    if article and self._is_valid_article(article):
                        articles.append(article)
                    
                        
        except Exception as e:
            self.logger.error(f"{self.source_type.upper()} 뉴스 수집 오류 ({self.name}): {e}")
        
        return articles
    

    async def _parse_rss(self, entries) -> List[Dict[str, Any]]:
        """
        RSS entries 리스트를 공통 포맷으로 변환
        
        Args:
            entries: feedparser entries 리스트
            
        Returns:
            List[Dict[str, Any]]: 공통 포맷의 뉴스 데이터 리스트
        """
        articles = []
        
        # entries가 리스트가 아닌 경우 (단일 entry) 리스트로 변환
        if not isinstance(entries, list):
            entries = [entries]
        
        for entry in entries:
            try:
                # RSS 피드의 published 날짜를 datetime 객체로 파싱
                published_date_str = entry.published
                published_date = self._parse_date(published_date_str)
                
                # 기본 기사 정보 구성
                article = {
                    'title': self._clean_text(entry.title) if hasattr(entry, 'title') else '',
                    'url': entry.link if hasattr(entry, 'link') else '',
                    'published': published_date,
                    'content': entry.description if hasattr(entry, 'description') else '',
                    'source': self.name,
                }
                
                # 요약 또는 본문 내용 설정 (RSS 기본 내용만)
                if self.use_summary and hasattr(entry, 'summary'):
                    article['content'] = self._clean_text(entry.summary)
                elif hasattr(entry, 'description'):
                    article['content'] = self._clean_text(entry.description)
                
                # 파싱된 기사를 리스트에 추가 (유효성 검증은 호출하는 쪽에서 처리)
                if article:
                    articles.append(article)
                    
            except Exception as e:
                self.logger.info(f"RSS 엔트리 파싱 오류 ({self.name}): {e}")
                continue
        
        return articles
    
    async def _fetch_article_detail(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """
        기사 상세 내용 수집 (중복 확인 후에만 실행)
        
        Args:
            article: 기사 정보
            
        Returns:
            상세 내용이 추가된 기사 정보
        """
        try:
            url = article.get('url', '')
            if not url:
                return article
            
            # 커스텀 URL 변환기가 있으면 사용
            if self.custom_url_converter:
                url = self.custom_url_converter(url)
            
            # 상세 내용 수집 (RSS 소스의 경우 커스텀 페처 우선 사용)
            if self.custom_content_fetcher:
                content = await self.custom_content_fetcher(url)
            else:
                content = await self._fetch_article_content(url)
            
            if content:
                article['content'] = content
            
            return article
            
        except Exception as e:
            self.logger.debug(f"기사 상세 내용 수집 오류: {e}")
            return article
    
    async def fetch_detailed_content_for_new_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        새로운 기사들에 대해서만 상세 내용 수집 (성능 최적화)
        
        Args:
            articles: 기사 리스트
            
        Returns:
            상세 내용이 추가된 기사 리스트
        """
        if not self.custom_content_fetcher or not articles:
            return articles

        self.logger.info(f"상세 내용 수집 시작 ({self.name}): {len(articles)}개")
        
        try:
            # 세마포어로 동시성 제어
            semaphore = asyncio.Semaphore(self.semaphore_limit)
            
            async def fetch_with_semaphore(article):
                async with semaphore:
                    return await self._fetch_article_detail(article)
            
            # 병렬로 상세 내용 수집
            tasks = [fetch_with_semaphore(article) for article in articles]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 성공한 결과만 반환
            detailed_articles = []
            for result in results:
                if isinstance(result, dict):
                    detailed_articles.append(result)
                elif isinstance(result, Exception):
                    self.logger.debug(f"상세 내용 수집 오류: {result}")
            
            return detailed_articles
            
        except Exception as e:
            self.logger.error(f"상세 내용 수집 배치 처리 오류: {e}")
            return articles
    
    async def _fetch_content_by_selector(self, url: str, selectors: List[Dict[str, Any]]) -> str:
        """
        특정 선택자를 사용하여 기사 본문 수집 (범용 content fetcher)
        
        Args:
            url: 기사 URL
            selectors: 우선순위별 선택자 리스트
                      [{'type': 'id', 'value': 'articletxt'}, 
                       {'type': 'attr', 'name': 'itemprop', 'value': 'articleBody'}, 
                       {'type': 'class', 'value': 'article-content'}]
            
        Returns:
            기사 본문 내용
        """
        # 대체 선택자들
        fallback_selectors = [
            '.article-content',
            '.news-content', 
            '.content-body',
            '[itemprop="articleBody"]',
            '.article-body'
        ]

        try:
            # 기존 세션이 있으면 사용, 없으면 임시 세션 생성
            session_to_use = self.session
            if not session_to_use:
                session_to_use = aiohttp.ClientSession()
            
            try:
                async with session_to_use.get(url, headers=self.headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        # 우선순위별 선택자 시도
                        for selector in selectors:
                            content_elem = self._find_element_by_selector(soup, selector)
                            if content_elem:
                                content = self._extract_clean_content(content_elem)
                                if content and len(content) > 50:
                                    return content
                        
                        # 기본 선택자들이 실패한 경우 fallback 선택자들 시도
                        for selector in fallback_selectors:
                            content_elem = soup.select_one(selector)
                            if content_elem:
                                content = self._extract_clean_content(content_elem)
                                if content and len(content) > 50:
                                    return content
                        
                        return ""
            finally:
                # 임시 세션이었다면 닫기
                if not self.session and session_to_use:
                    await session_to_use.close()
                        
        except Exception as e:
            self.logger.debug(f"범용 본문 스크래핑 오류 ({url}): {e}")
            return ""
    
    def _find_element_by_selector(self, soup: BeautifulSoup, selector: Dict[str, Any]):
        """선택자 타입에 따라 요소 찾기
        
        Args:
            soup: BeautifulSoup 객체
            selector: 선택자 정보
            
        Returns:
            찾은 요소 또는 None
        """
        if selector['type'] == 'id':
            return soup.find(id=selector['value'])
        elif selector['type'] == 'attr':
            return soup.find(attrs={selector['name']: selector['value']})
        elif selector['type'] == 'class':
            return soup.find(class_=selector['value'])
        elif selector['type'] == 'css':
            return soup.select_one(selector['value'])
        return None
    
    def _extract_clean_content(self, content_elem) -> str:
        """요소에서 깨끗한 텍스트 추출
        
        Args:
            content_elem: BeautifulSoup 요소
            
        Returns:
            정리된 텍스트
        """
        # 불필요한 요소들 제거 (스크립트, 스타일, 광고 등)
        for unwanted in content_elem.find_all(['script', 'style', 'iframe', 'ins', 'noscript']):
            unwanted.decompose()
        
        # 태그 제거하고 텍스트만 추출
        content = content_elem.get_text(separator=' ', strip=True)
        # 연속된 공백 제거
        content = re.sub(r'\s+', ' ', content)
        # 앞뒤 공백 제거
        content = content.strip()
        
        return self._clean_text(content)