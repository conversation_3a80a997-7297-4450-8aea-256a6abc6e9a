#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
한국경제 RSS 뉴스 소스
"""

from typing import Dict, List, Any
from .base_news_source import BaseNewsSource

class HankyungNewsSource(BaseNewsSource):
    """
    한국경제 RSS 뉴스 소스
    """
    
    def __init__(self, config_manager):
        # 한국경제 RSS 소스 설정 - 공통 설정 활용
        source_config = {
            'name': '한국경제',
            'url': 'https://www.hankyung.com/feed/all-news',
            'type': 'RSS',
            'encoding': 'utf-8',
            'use_summary': True,
            'max_articles': 50,
            'timeout': 8,
            'semaphore_limit': 15
        }
        
        super().__init__(config_manager, source_config)
        
        # 한국경제 특화 선택자 설정
        self._hankyung_selectors = [
            {'type': 'id', 'value': 'articletxt'},
            {'type': 'class', 'value': 'article-content'},
            {'type': 'css', 'value': '.news-content'}
        ]
        
        # 커스텀 컨텐츠 페처 설정
        self.custom_content_fetcher = self._fetch_hankyung_article_content
    
    async def collect_news(self) -> List[Dict[str, Any]]:
        """
        한국경제 RSS 뉴스 수집 - BaseNewsSource의 공통 로직 사용
        """
        return await self.collect_rss_web_news(self._parse_rss)
    
    async def _fetch_hankyung_article_content(self, url: str) -> str:
        """
        한국경제 기사 본문 수집 - 최적화된 선택자 사용
        
        Args:
            url: 기사 URL
            
        Returns:
            기사 본문 내용
        """
        return await self._fetch_content_by_selector(url, self._hankyung_selectors)