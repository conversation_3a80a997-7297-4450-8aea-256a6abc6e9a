# -*- coding: utf-8 -*-
"""
네이버 뉴스 소스

네이버 증권 뉴스에서 뉴스를 수집하는 클래스입니다.
"""

from typing import Dict, List, Any
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs
import re

from .base_news_source import BaseNewsSource

class NaverNewsSource(BaseNewsSource):
    """
    네이버 증권 뉴스 소스 클래스
    
    네이버 증권 뉴스 페이지에서 뉴스를 수집합니다.
    """
    
    def __init__(self, config_manager):
        """
        네이버 뉴스 소스 초기화
        
        Args:
            config_manager: 설정 관리자
        """
        # 네이버 증권 뉴스 기본 설정 정의
        source_config = {
            'name': '네이버 증권',
            'url': 'https://finance.naver.com/news/news_list.naver',
            'type': 'WEB',
            'detail_fetch': True,  # 상세 본문 수집 활성화
            'max_articles': 50,
            'timeout': 30,
            'semaphore_limit': 20
        }
        
        super().__init__(config_manager, source_config)
        
        # 네이버 특화 설정
        self.base_url = 'https://finance.naver.com'
        
        # 커스텀 함수들 설정
        self.custom_url_converter = self._convert_to_actual_news_url
        self.custom_content_fetcher = self._fetch_naver_article_content
    
    async def collect_news(self) -> List[Dict[str, Any]]:
        """
        네이버 증권 뉴스 수집 - BaseNewsSource의 공통 로직 사용
        
        Returns:
            수집된 뉴스 기사 리스트
        """
        return await self.collect_rss_web_news(self._parse_naver_news)
    
    def _convert_to_actual_news_url(self, url: str) -> str:
        """
        네이버 증권 뉴스 URL을 실제 뉴스 URL로 변환
        
        Args:
            url: 네이버 증권 뉴스 URL
            
        Returns:
            실제 뉴스 URL
        """
        try:
            # 네이버 증권 뉴스 URL 패턴 확인
            if 'finance.naver.com/news/news_read.naver' in url:
                # URL에서 article_id와 office_id 추출
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                
                article_id = params.get('article_id', [None])[0]
                office_id = params.get('office_id', [None])[0]
                
                if article_id and office_id:
                    # 실제 네이버 뉴스 URL로 변환
                    # 예: https://n.news.naver.com/mnews/article/277/0005631087
                    actual_url = f"https://n.news.naver.com/mnews/article/{office_id}/{article_id}"
                    self.logger.debug(f"URL 변환: {url} -> {actual_url}")
                    return actual_url
            
            # 변환할 수 없는 경우 원본 URL 반환
            return url
            
        except Exception as e:
            self.logger.debug(f"URL 변환 오류: {e}")
            return url
    
    async def _parse_naver_news(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        네이버 뉴스 HTML 파싱
        
        Args:
            soup: BeautifulSoup 객체
        
        Returns:
            파싱된 뉴스 기사 리스트
        """
        articles = []
        
        try:
            # 네이버 증권 뉴스 리스트 파싱 (li.newsList 구조)
            newslist_items = soup.find_all('li', class_='newsList')
            self.logger.debug(f"네이버 증권 li.newsList 발견: {len(newslist_items)}개")
            
            # 각 뉴스 아이템 파싱
            for i, item in enumerate(newslist_items):
                try:
                    self.logger.debug(f"네이버 증권 newslist {i+1} 파싱 시작")
                    parsed_articles = self._parse_newslist_item(item)
                    if parsed_articles:
                        articles.extend(parsed_articles)
                        self.logger.debug(f"네이버 증권 newslist {i+1}에서 {len(parsed_articles)}개 뉴스 파싱 성공")
                    else:
                        self.logger.warning(f"네이버 증권 newslist {i+1} 파싱 실패")
                except Exception as item_error:
                    self.logger.warning(f"네이버 증권 newslist 아이템 파싱 오류: {item_error}", exc_info=True)
                    continue
        
        except Exception as e:
            self.logger.error(f"네이버 뉴스 파싱 오류: {e}")
        
        self.logger.debug(f"네이버 증권 뉴스 파싱 완료: {len(articles)}개")
        return articles
    
    def _parse_newslist_item(self, item) -> List[Dict[str, Any]]:
        """
        li.newsList 구조의 뉴스 아이템들 파싱 (dl 안의 여러 dt/dd 쌍)
        
        Args:
            item: BeautifulSoup li.newsList 요소
        
        Returns:
            파싱된 기사 정보 리스트
        """
        articles = []
        try:
            # dl 요소 찾기
            dl_element = item.find('dl')
            if not dl_element:
                self.logger.info("dl 요소를 찾을 수 없음")
                return articles

            # dt와 dd 요소들을 순서대로 찾기
            dt_elements = dl_element.find_all('dt', class_='thumb')
            dd_subject_elements = dl_element.find_all('dd', class_='articleSubject')
            dd_summary_elements = dl_element.find_all('dd', class_='articleSummary')
            
            # 각 뉴스 아이템 파싱 (dt, dd_subject, dd_summary가 세트)
            min_count = min(len(dd_subject_elements), len(dd_summary_elements))
            self.logger.debug(f"네이버 증권 뉴스 아이템 {min_count}개 발견")
            
            for i in range(min_count):
                try:
                    dd_subject = dd_subject_elements[i]
                    dd_summary = dd_summary_elements[i]
                    
                    # 제목과 링크 추출
                    title_link = dd_subject.find('a')
                    if not title_link:
                        self.logger.info(f"뉴스 아이템 {i+1}: 링크를 찾을 수 없음")
                        continue

                    # 제목 추출
                    title = self._clean_text(title_link.get_text())
                    if not title:
                        self.logger.info(f"뉴스 아이템 {i+1}: 제목이 비어있음")
                        continue

                    self.logger.debug(f"네이버 증권 뉴스 아이템 {i+1} 제목: {title[:50]}...")

                    # URL 추출 및 절대 URL 변환
                    url = title_link.get('href', '')
                    if url.startswith('/'):
                        url = urljoin(self.base_url, url)
                    elif not url.startswith('http'):
                        self.logger.debug(f"뉴스 아이템 {i+1}: 유효하지 않은 URL: {url}")
                        continue

                    # 네이버 증권 뉴스 URL을 실제 뉴스 URL로 변환
                    url = self._convert_to_actual_news_url(url)

                    # 날짜 추출
                    published = None
                    wdate_elem = dd_summary.find('span', class_='wdate')
                    if wdate_elem:
                        date_text = self._clean_text(wdate_elem.get_text())
                        self.logger.debug(f"네이버 증권 뉴스 아이템 {i+1} 날짜: '{date_text}'")
                        if date_text:
                            published = self._parse_date(date_text)
                            if published:
                                self.logger.debug(f"네이버 증권 날짜 파싱 성공: {date_text} -> {published}")
                            else:
                                self.logger.warning(f"네이버 증권 날짜 파싱 실패: {date_text}")
                    
                    # 날짜를 찾지 못한 경우 현재 시간 사용
                    if not published:
                        published = datetime.now()
                        self.logger.warning(f"네이버 증권에서 날짜를 찾을 수 없어 현재 시간 사용: {title[:50]}...")
                    
                    # 요약 추출
                    summary = ""
                    if dd_summary:
                        summary = self._clean_text(dd_summary.get_text())[:200]  # 200자로 제한
                    
                    self.logger.debug(f"네이버 증권 뉴스 파싱 완료: {title[:30]}... | {published}")
                    
                    article = {
                        'title': title,
                        'url': url,
                        'published': published,
                        'content': summary,
                        'source': self.name
                    }

                    
                    articles.append(article)
                    
                except Exception as item_error:
                    self.logger.warning(f"뉴스 아이템 {i+1} 파싱 오류: {item_error}")
                    import traceback
                    self.logger.warning(f"상세 오류: {traceback.format_exc()}")
                    continue
            
        except Exception as e:
            self.logger.warning(f"newslist 전체 파싱 오류: {e}")
            import traceback
            self.logger.warning(f"상세 오류: {traceback.format_exc()}")
            
        return articles
    
    def _parse_ul_news_item(self, li_item, link) -> Dict[str, Any]:
        """
        ul 구조의 뉴스 아이템 파싱
        
        Args:
            li_item: BeautifulSoup li 요소
            link: BeautifulSoup a 요소 (뉴스 링크)
        
        Returns:
            파싱된 기사 정보
        """
        try:
            # 제목 추출
            title = self._clean_text(link.get_text())
            if not title:
                self.logger.info("ul 뉴스: 제목이 비어있음")
                return None

            self.logger.info(f"네이버 증권 ul 뉴스 제목: {title[:50]}...")

            # URL 추출 및 절대 URL 변환
            url = link.get('href', '')
            if url.startswith('/'):
                url = urljoin(self.base_url, url)
            elif not url.startswith('http'):
                self.logger.debug(f"ul 뉴스: 유효하지 않은 URL: {url}")
                return None

            # 네이버 증권 뉴스 URL을 실제 뉴스 URL로 변환
            url = self._convert_to_actual_news_url(url)

            # ul 구조에서는 날짜 정보가 제한적이므로 현재 시간 사용
            published = datetime.now()
            self.logger.info(f"ul 뉴스: 날짜 정보 없음, 현재 시간 사용: {published}")
            
            # 요약은 제목을 기반으로 생성
            summary = title[:200]  # 200자로 제한
            
            self.logger.debug(f"네이버 증권 ul 뉴스 파싱 완료: {title[:30]}... | {published}")
            
            return {
                'title': title,
                'url': url,
                'published': published,
                'summary': summary,
                'source': self.name
            }
            
        except Exception as e:
            self.logger.warning(f"ul 뉴스 아이템 파싱 오류: {e}")
            import traceback
            self.logger.warning(f"상세 오류: {traceback.format_exc()}")
            return None
    
    def _parse_date(self, date_text: str) -> datetime:
        """
        날짜 텍스트를 datetime 객체로 변환
        
        Args:
            date_text: 날짜 텍스트
        
        Returns:
            파싱된 datetime 객체
        """
        try:
            # 네이버 증권 날짜 형식: "2025-08-03 22:13"
            if re.match(r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}', date_text):
                return datetime.strptime(date_text, '%Y-%m-%d %H:%M')
            
            # 시간만 있는 경우: "22:13" (오늘 날짜로 가정)
            if re.match(r'\d{2}:\d{2}', date_text):
                today = datetime.now().date()
                time_obj = datetime.strptime(date_text, '%H:%M').time()
                return datetime.combine(today, time_obj)
            
            # 기타 형식들
            date_formats = [
                '%Y.%m.%d %H:%M',
                '%Y-%m-%d',
                '%m.%d %H:%M',
                '%m-%d %H:%M'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(date_text, fmt)
                except ValueError:
                    continue
            
            self.logger.debug(f"지원되지 않는 날짜 형식: {date_text}")
            return None
            
        except Exception as e:
            self.logger.debug(f"날짜 파싱 오류: {e}")
            return None
    
    async def _fetch_naver_article_content(self, url: str) -> str:
        """
        네이버 뉴스 기사 본문 수집
        
        Args:
            url: 기사 URL
            
        Returns:
            기사 본문 내용
        """
        # 네이버 뉴스 특화 선택자 설정 (다양한 언론사 지원)
        selectors = [
            {'type': 'id', 'value': 'dic_area'},  # 네이버 뉴스 기본
            {'type': 'class', 'value': 'go_trans'},  # 네이버 뉴스 번역 영역
            {'type': 'attr', 'name': 'itemprop', 'value': 'articleBody'},  # 구조화된 데이터
            {'type': 'class', 'value': '_article_body_contents'},  # 일부 언론사
            {'type': 'id', 'value': 'articleBodyContents'}  # 일부 언론사
        ]
        
        return await self._fetch_content_by_selector(url, selectors)