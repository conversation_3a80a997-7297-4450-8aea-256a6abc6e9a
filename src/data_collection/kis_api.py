# -*- coding: utf-8 -*-
"""
한국투자증권 Open API 연동 모듈

실시간 시세 데이터 수집 및 주문 처리
"""

import asyncio
import aiohttp
import websockets
import json
import hashlib
import hmac
import base64
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from urllib.parse import urlencode

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager

class KISApi:
    """
    한국투자증권 Open API 클래스
    """
    
    def __init__(self, config_manager: ConfigManager):
        """KIS API 초기화
        
        Args:
            config_manager: 설정 관리자
        """
        self.config = config_manager
        self.logger = get_logger()
        
        # API 설정
        self.app_key = self.config.get_kis_app_key()
        self.app_secret = self.config.get_kis_app_secret()
        self.account_no = self.config.get_kis_account_no()
        self.account_product_cd = self.config.get_kis_account_product_cd()
        
        # API URL
        self.base_url = self.config.get_kis_api_url()
        self.ws_url = self.config.get_kis_websocket_url()
        
        # 환경 설정
        self.environment = self.config.get('trading.environment', 'demo')
        
        # 토큰 관리
        self.access_token = None
        self.token_expires_at = None
        self.approval_key = None
        self.approval_key_expires_at = None
        
        # 토큰 캐시 파일 경로 (프로젝트 폴더 내)
        project_root = Path(__file__).parent.parent.parent  # autoTraderBot 루트 디렉토리
        self.token_cache_dir = project_root / 'cache'
        # APP_KEY로 토큰 파일 구분
        app_key_suffix = self.app_key[-8:] if len(self.app_key) >= 8 else self.app_key
        self.token_cache_file = self.token_cache_dir / f'kis_token_{self.environment}_{app_key_suffix}.json'
        self.approval_cache_file = self.token_cache_dir / f'kis_websocket_{self.environment}_{app_key_suffix}.json'
        
        # 캐시 디렉토리 생성
        self.token_cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 저장된 토큰 및 접속키 로드
        self._load_cached_token()
        self._load_cached_approval_key()
        
        # 웹소켓 연결
        self.ws_connection = None
        self.ws_callbacks = {}
        
        # 세션
        self.session = None
        
        # 연결 상태
        self.connected = False
    
    def _get_tr_id(self, base_tr_id: str) -> str:
        """
        환경에 따라 TR_ID를 설정하는 공통 함수
        
        Args:
            base_tr_id: 기본 TR_ID (TTTC로 시작하는 실제투자용)
        
        Returns:
            환경에 맞는 TR_ID (데모: VTTC, 실제: TTTC)
        """
        if self.environment == 'demo':
            # 실제투자용 TR_ID를 모의투자용으로 변환 (TTTC -> VTTC)
            return base_tr_id.replace('TTTC', 'VTTC', 1)
        else:
            # 실제투자용 TR_ID 그대로 사용
            return base_tr_id
    
    def is_connected(self) -> bool:
        """연결 상태 확인
        
        Returns:
            연결 상태
        """
        return self.connected and self.session is not None and not self.session.closed
    
    async def get_market_status(self) -> Dict[str, Any]:
        """시장 상태 조회
        
        Returns:
            시장 상태 정보
        """
        if not self.is_connected():
            return {
                'market_open': False,
                'status': 'disconnected',
                'message': 'API 연결되지 않음'
            }
        
        try:
            # 현재 시간 기준으로 시장 상태 판단 (간단한 구현)
            now = datetime.now()
            weekday = now.weekday()  # 0=월요일, 6=일요일
            hour = now.hour
            minute = now.minute
            
            # 주말 체크
            if weekday >= 5:  # 토요일, 일요일
                return {
                    'market_open': False,
                    'status': 'weekend',
                    'message': '주말 - 시장 휴장'
                }
            
            # 평일 장 시간 체크 (9:00 ~ 15:30)
            market_open_time = 9 * 60  # 9:00을 분으로 변환
            market_close_time = 15 * 60 + 30  # 15:30을 분으로 변환
            current_time = hour * 60 + minute
            
            if market_open_time <= current_time <= market_close_time:
                return {
                    'market_open': True,
                    'status': 'open',
                    'message': '정규장 시간'
                }
            else:
                return {
                    'market_open': False,
                    'status': 'closed',
                    'message': '장 마감 시간'
                }
        
        except Exception as e:
            self.logger.error(f"시장 상태 조회 오류: {e}")
            return {
                'market_open': False,
                'status': 'error',
                'message': f'조회 오류: {str(e)}'
            }
    
    async def initialize(self):
        """API 초기화"""
        self.logger.info("KIS API 초기화 시작")
        
        try:
            # HTTP 세션 생성
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            # 접근 토큰 발급
            token = await self.get_access_token()
            
            # 웹소켓 접속키 발급
            approval = await self.get_approval_key()
            
            # 연결 상태 업데이트
            self.connected = (token is not None or approval is not None)
            
            self.logger.info("KIS API 초기화 완료")
        
        except Exception as e:
            self.logger.error(f"KIS API 초기화 오류: {e}")
            self.connected = False
    
    def _save_token_cache(self) -> None:
        """토큰을 캐시 파일에 저장
        
        토큰과 만료 시간을 JSON 파일로 저장하여
        프로그램 재시작 시에도 유효한 토큰을 재사용할 수 있도록 함
        """
        try:
            if self.access_token and self.token_expires_at:
                cache_data = {
                    'access_token': self.access_token,
                    'token_expires_at': self.token_expires_at.isoformat(),
                    'environment': self.environment,
                    'saved_at': datetime.now().isoformat()
                }
                
                with open(self.token_cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, indent=2)
                
                self.logger.debug(f"토큰 캐시 저장 완료: {self.token_cache_file}")
        except Exception as e:
            self.logger.warning(f"토큰 캐시 저장 실패: {e}")
    
    def _load_cached_token(self) -> None:
        """캐시된 토큰을 로드
        
        저장된 토큰이 유효하면 메모리에 로드하여 재사용
        """
        try:
            if self.token_cache_file.exists():
                with open(self.token_cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # 환경이 일치하는지 확인
                if cache_data.get('environment') != self.environment:
                    self.logger.debug("캐시된 토큰의 환경이 다름 - 무시")
                    return
                
                # 만료 시간 파싱
                expires_at = datetime.fromisoformat(cache_data['token_expires_at'])
                
                # 토큰이 아직 유효한지 확인 (만료 5분 전까지)
                if datetime.now() < expires_at - timedelta(minutes=5):
                    self.access_token = cache_data['access_token']
                    self.token_expires_at = expires_at
                    self.logger.info(f"캐시된 토큰 로드 성공 (만료: {expires_at})")
                else:
                    self.logger.debug("캐시된 토큰이 만료됨 - 새로 발급 필요")
                    # 만료된 캐시 파일 삭제
                    self.token_cache_file.unlink(missing_ok=True)
        except Exception as e:
            self.logger.warning(f"토큰 캐시 로드 실패: {e}")
            # 손상된 캐시 파일 삭제
            self.token_cache_file.unlink(missing_ok=True)
    
    def _delete_token_cache(self) -> None:
        """토큰 캐시 파일 삭제
        
        토큰 폐기 시 캐시 파일도 함께 삭제
        """
        try:
            if self.token_cache_file.exists():
                self.token_cache_file.unlink()
                self.logger.debug(f"토큰 캐시 파일 삭제 완료: {self.token_cache_file}")
        except Exception as e:
            self.logger.warning(f"토큰 캐시 파일 삭제 실패: {e}")
    
    def _save_approval_key_cache(self) -> None:
        """웹소켓 접속키를 캐시 파일에 저장
        
        접속키와 만료 시간을 JSON 파일로 저장하여
        프로그램 재시작 시에도 유효한 접속키를 재사용할 수 있도록 함
        """
        try:
            if self.approval_key and self.approval_key_expires_at:
                cache_data = {
                    'approval_key': self.approval_key,
                    'approval_key_expires_at': self.approval_key_expires_at.isoformat(),
                    'environment': self.environment,
                    'saved_at': datetime.now().isoformat()
                }
                
                with open(self.approval_cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, indent=2)
                
                self.logger.debug(f"웹소켓 접속키 캐시 저장 완료: {self.approval_cache_file}")
        except Exception as e:
            self.logger.warning(f"웹소켓 접속키 캐시 저장 실패: {e}")
    
    def _load_cached_approval_key(self) -> None:
        """캐시된 웹소켓 접속키를 로드
        
        저장된 접속키가 유효하면 메모리에 로드하여 재사용
        """
        try:
            if self.approval_cache_file.exists():
                with open(self.approval_cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # 환경이 일치하는지 확인
                if cache_data.get('environment') != self.environment:
                    self.logger.debug("캐시된 웹소켓 접속키의 환경이 다름 - 무시")
                    return
                
                # 만료 시간 파싱
                expires_at = datetime.fromisoformat(cache_data['approval_key_expires_at'])
                
                # 접속키가 아직 유효한지 확인 (만료 1시간 전까지)
                if datetime.now() < expires_at - timedelta(hours=1):
                    self.approval_key = cache_data['approval_key']
                    self.approval_key_expires_at = expires_at
                    self.logger.info(f"캐시된 웹소켓 접속키 로드 성공 (만료: {expires_at})")
                else:
                    self.logger.debug("캐시된 웹소켓 접속키가 만료됨 - 새로 발급 필요")
                    # 만료된 캐시 파일 삭제
                    self.approval_cache_file.unlink(missing_ok=True)
        except Exception as e:
            self.logger.warning(f"웹소켓 접속키 캐시 로드 실패: {e}")
            # 손상된 캐시 파일 삭제
            self.approval_cache_file.unlink(missing_ok=True)
    
    def _delete_approval_key_cache(self) -> None:
        """웹소켓 접속키 캐시 파일 삭제
        
        접속키 폐기 시 캐시 파일도 함께 삭제
        """
        try:
            if self.approval_cache_file.exists():
                self.approval_cache_file.unlink()
                self.logger.debug(f"웹소켓 접속키 캐시 파일 삭제 완료: {self.approval_cache_file}")
        except Exception as e:
            self.logger.warning(f"웹소켓 접속키 캐시 파일 삭제 실패: {e}")
    
    def _is_approval_key_valid(self) -> bool:
        """웹소켓 접속키 유효성 검사
        
        KIS API 정책에 따른 접속키 유효성 검사:
        - 접속키가 존재하고 만료 시간이 설정되어 있어야 함
        - 현재 시간이 만료 시간보다 1시간 이상 여유가 있어야 함
        - 이는 접속키 갱신을 위한 안전 마진
        
        Returns:
            접속키가 유효하면 True, 그렇지 않으면 False
        """
        if not self.approval_key or not self.approval_key_expires_at:
            return False
        
        # 만료 1시간 전까지를 유효한 것으로 간주
        return datetime.now() < self.approval_key_expires_at - timedelta(hours=1)
    
    def _is_token_valid(self) -> bool:
        """토큰 유효성 검사
        
        KIS API 정책에 따른 토큰 유효성 검사:
        - 토큰이 존재하고 만료 시간이 설정되어 있어야 함
        - 현재 시간이 만료 시간보다 5분 이상 여유가 있어야 함
        - 이는 토큰 갱신을 위한 안전 마진
        
        Returns:
            토큰이 유효하면 True, 그렇지 않으면 False
        """
        if not self.access_token or not self.token_expires_at:
            return False
        
        # 만료 5분 전까지를 유효한 것으로 간주
        return datetime.now() < self.token_expires_at - timedelta(minutes=5)
    
    async def get_access_token(self) -> str:
        """접근 토큰 발급 및 관리
        
        KIS API 정책:
        - 유효기간: 24시간 (1일 1회 발급 원칙)
        - 갱신 주기: 6시간 (6시간 이내는 기존 토큰 재사용)
        - 토큰 만료 5분 전에 미리 갱신
        
        Returns:
            접근 토큰
        """
        # 토큰이 유효하면 재사용
        if self._is_token_valid():
            self.logger.debug(f"기존 토큰 재사용 (만료: {self.token_expires_at})")
            return self.access_token
        
        # 기존 토큰이 있으면 폐기
        if self.access_token:
            self.logger.info("접근 토큰 갱신 시작 - 기존 토큰 폐기")
            await self._revoke_access_token()
        else:
            self.logger.info("접근 토큰 최초 발급 시작")
        
        url = f"{self.base_url}/oauth2/tokenP"
        data = {
            "grant_type": "client_credentials",
            "appkey": self.app_key,
            "appsecret": self.app_secret
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    self.access_token = result['access_token']
                    
                    # KIS API에서 제공하는 정확한 만료 시간 사용
                    if 'access_token_token_expired' in result:
                        # ISO 형식의 만료 시간을 파싱
                        expired_str = result['access_token_token_expired']
                        try:
                            # 예: "2024-01-01 23:59:59" 형식
                            self.token_expires_at = datetime.strptime(expired_str, "%Y-%m-%d %H:%M:%S")
                        except ValueError:
                            # 파싱 실패 시 기본값 사용
                            expires_in = result.get('expires_in', 86400)  # 기본 24시간
                            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                    else:
                        # expires_in 사용 (초 단위)
                        expires_in = result.get('expires_in', 86400)  # 기본 24시간
                        self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                    
                    self.logger.info(f"접근 토큰 발급 성공 (만료: {self.token_expires_at})")
                    
                    # 토큰을 캐시에 저장
                    self._save_token_cache()
                    
                    return self.access_token
                else:
                    error_text = await response.text()
                    self.logger.warning(f"접근 토큰 발급 실패: {response.status} - {error_text}")
                    
                    # 환경에 따른 적절한 오류 메시지 출력
                    environment = self.config.get('trading.environment', 'demo')
                    if environment == 'demo':
                        self.logger.warning("데모 API 키가 유효하지 않습니다. API 키를 확인해주세요.")
                    else:
                        self.logger.warning("실제 API 키가 유효하지 않습니다. API 키를 확인해주세요.")
                    return None
        
        except Exception as e:
            self.logger.warning(f"접근 토큰 발급 중 오류: {e}")
            
            # 환경에 따른 적절한 오류 메시지 출력
            environment = self.config.get('trading.environment', 'demo')
            if environment == 'demo':
                self.logger.warning("데모 API 연결에 실패했습니다. 네트워크 또는 API 키를 확인해주세요.")
            else:
                self.logger.warning("실제 API 연결에 실패했습니다. 네트워크 또는 API 키를 확인해주세요.")
            return None
    
    async def get_approval_key(self) -> str:
        """웹소켓 접속키 발급 및 관리
        
        KIS API 정책:
        - 유효기간: 24시간
        - 세션 연결 시 초기 1회만 사용
        - 접속키 인증 후에는 세션종료되지 않는 이상 365일 내내 사용 가능
        - 접속키 만료 1시간 전에 미리 갱신
        
        Returns:
            웹소켓 접속키
        """
        # 접속키가 유효하면 재사용
        if self._is_approval_key_valid():
            self.logger.debug(f"기존 웹소켓 접속키 재사용 (만료: {self.approval_key_expires_at})")
            return self.approval_key
        
        # 기존 접속키가 있으면 폐기
        if self.approval_key:
            self.logger.info("웹소켓 접속키 갱신 시작 - 기존 접속키 폐기")
            await self._revoke_approval_key()
        else:
            self.logger.info("웹소켓 접속키 최초 발급 시작")
        
        url = f"{self.base_url}/oauth2/Approval"
        data = {
            "grant_type": "client_credentials",
            "appkey": self.app_key,
            "secretkey": self.app_secret
        }
        
        try:
            async with self.session.post(url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    self.approval_key = result['approval_key']
                    
                    # 웹소켓 접속키는 24시간 유효
                    self.approval_key_expires_at = datetime.now() + timedelta(hours=24)
                    
                    self.logger.info(f"웹소켓 접속키 발급 성공 (만료: {self.approval_key_expires_at})")
                    
                    # 접속키를 캐시에 저장
                    self._save_approval_key_cache()
                    
                    return self.approval_key
                else:
                    error_text = await response.text()
                    self.logger.warning(f"접속키 발급 실패: {response.status} - {error_text}")
                    
                    # 환경에 따른 적절한 오류 메시지 출력
                    environment = self.config.get('trading.environment', 'demo')
                    if environment == 'demo':
                        self.logger.warning("데모 웹소켓 접속키 발급에 실패했습니다. API 키를 확인해주세요.")
                    else:
                        self.logger.warning("실제 웹소켓 접속키 발급에 실패했습니다. API 키를 확인해주세요.")
                    return None
        
        except Exception as e:
            self.logger.warning(f"접속키 발급 중 오류: {e}")
            
            # 환경에 따른 적절한 오류 메시지 출력
            environment = self.config.get('trading.environment', 'demo')
            if environment == 'demo':
                self.logger.warning("데모 웹소켓 연결에 실패했습니다. 네트워크 또는 API 키를 확인해주세요.")
            else:
                self.logger.warning("실제 웹소켓 연결에 실패했습니다. 네트워크 또는 API 키를 확인해주세요.")
            return None
    
    async def _revoke_access_token(self) -> bool:
        """접근 토큰 폐기
        
        기존 토큰을 안전하게 폐기합니다.
        
        Returns:
            폐기 성공 여부
        """
        if not self.access_token:
            self.logger.debug("폐기할 토큰이 없습니다")
            return True
        
        url = f"{self.base_url}/oauth2/tokenP"
        headers = {
            "content-type": "application/json; charset=utf-8"
        }
        
        data = {
            "grant_type": "client_credentials",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "token": self.access_token
        }
        
        try:
            async with self.session.post(url, headers=headers, json=data) as response:
                result_text = await response.text()
                self.logger.debug(f"토큰 폐기 응답: {response.status} - {result_text}")
                
                if response.status == 200:
                    try:
                        result = await response.json()
                        if result.get('rt_cd') == '0' or result.get('code') == 200:
                            self.logger.info("접근 토큰 폐기 성공")
                            # 토큰 정보 초기화
                            self.access_token = None
                            self.token_expires_at = None
                            # 캐시 파일도 삭제
                            self._delete_token_cache()
                            return True
                        else:
                            self.logger.warning(f"토큰 폐기 실패: {result.get('msg1', result.get('message', '알 수 없는 오류'))}")
                            return False
                    except Exception as e:
                        # JSON 파싱 실패 시 텍스트 응답 확인
                        if "성공" in result_text or "success" in result_text.lower():
                            self.logger.info("접근 토큰 폐기 성공")
                            self.access_token = None
                            self.token_expires_at = None
                            self._delete_token_cache()
                            return True
                        else:
                            self.logger.warning(f"토큰 폐기 응답 파싱 오류: {e} - {result_text}")
                            return False
                else:
                    self.logger.warning(f"토큰 폐기 API 오류: {response.status} - {result_text}")
                    return False
        
        except Exception as e:
            self.logger.warning(f"토큰 폐기 중 오류: {e}")
            return False
    
    async def _revoke_approval_key(self) -> bool:
        """웹소켓 접속키 폐기
        
        기존 접속키를 안전하게 폐기합니다.
        
        Returns:
            폐기 성공 여부
        """
        if not self.approval_key:
            self.logger.debug("폐기할 웹소켓 접속키가 없습니다")
            return True
        
        # KIS API에는 웹소켓 접속키 폐기 API가 별도로 없으므로
        # 로컬에서만 정리하고 캐시 파일을 삭제
        try:
            self.logger.info("웹소켓 접속키 폐기 시작")
            
            # 접속키 정보 초기화
            self.approval_key = None
            self.approval_key_expires_at = None
            
            # 캐시 파일도 삭제
            self._delete_approval_key_cache()
            
            self.logger.info("웹소켓 접속키 폐기 완료")
            return True
            
        except Exception as e:
            self.logger.warning(f"웹소켓 접속키 폐기 중 오류: {e}")
            return False
    
    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """현재가 조회
        
        Args:
            symbol: 종목코드
        
        Returns:
            현재가 정보
        """
        await self.get_access_token()
        
        url = f"{self.base_url}/uapi/domestic-stock/v1/quotations/inquire-price"
        headers = {
            "authorization": f"Bearer {self.access_token}",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "tr_id": "FHKST01010100"
        }
        
        params = {
            "fid_cond_mrkt_div_code": "J",
            "fid_input_iscd": symbol
        }
        
        try:
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['rt_cd'] == '0':
                        output = result['output']
                        return {
                            'symbol': symbol,
                            'price': float(output['stck_prpr']),
                            'change_rate': float(output['prdy_ctrt']),
                            'volume': int(output['acml_vol']),
                            'bid_price': float(output.get('stck_bidp', 0)),
                            'ask_price': float(output.get('stck_askp', 0)),
                            'timestamp': datetime.now()
                        }
                    else:
                        self.logger.error(f"현재가 조회 실패: {result['msg1']}")
                        return None
                else:
                    error_text = await response.text()
                    self.logger.error(f"현재가 조회 API 오류: {response.status} - {error_text}")
                    return None
        
        except Exception as e:
            self.logger.error(f"현재가 조회 중 오류: {e}")
            return None
    
    async def get_orderbook(self, symbol: str) -> Dict[str, Any]:
        """호가 정보 조회
        
        Args:
            symbol: 종목코드
        
        Returns:
            호가 정보
        """
        await self.get_access_token()
        
        url = f"{self.base_url}/uapi/domestic-stock/v1/quotations/inquire-asking-price-exp-ccn"
        headers = {
            "authorization": f"Bearer {self.access_token}",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "tr_id": "FHKST01010200"
        }
        
        params = {
            "fid_cond_mrkt_div_code": "J",
            "fid_input_iscd": symbol
        }
        
        try:
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['rt_cd'] == '0':
                        output = result['output1']
                        return {
                            'symbol': symbol,
                            'bids': [
                                {
                                    'price': float(output[f'bidp{i}']),
                                    'volume': int(output[f'bidp_rsqn{i}'])
                                }
                                for i in range(1, 11) if output.get(f'bidp{i}')
                            ],
                            'asks': [
                                {
                                    'price': float(output[f'askp{i}']),
                                    'volume': int(output[f'askp_rsqn{i}'])
                                }
                                for i in range(1, 11) if output.get(f'askp{i}')
                            ],
                            'timestamp': datetime.now()
                        }
                    else:
                        self.logger.error(f"호가 조회 실패: {result['msg1']}")
                        return None
                else:
                    error_text = await response.text()
                    self.logger.error(f"호가 조회 API 오류: {response.status} - {error_text}")
                    return None
        
        except Exception as e:
            self.logger.error(f"호가 조회 중 오류: {e}")
            return None
    
    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """종목 정보 조회
        
        Args:
            symbol: 종목코드
        
        Returns:
            종목 정보
        """
        await self.get_access_token()
        
        url = f"{self.base_url}/uapi/domestic-stock/v1/quotations/inquire-price"
        headers = {
            "authorization": f"Bearer {self.access_token}",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "tr_id": "FHKST01010100"
        }
        
        params = {
            "fid_cond_mrkt_div_code": "J",
            "fid_input_iscd": symbol
        }
        
        try:
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['rt_cd'] == '0':
                        output = result['output']
                        return {
                            'symbol': symbol,
                            'name': output.get('hts_kor_isnm', ''),  # 종목명
                            'current_price': float(output.get('stck_prpr', 0)),  # 현재가
                            'change_rate': float(output.get('prdy_ctrt', 0)),  # 전일대비율
                            'change_amount': float(output.get('prdy_vrss', 0)),  # 전일대비
                            'volume': int(output.get('acml_vol', 0)),  # 누적거래량
                            'market_cap': float(output.get('hts_avls', 0)) if output.get('hts_avls') else 0,  # 시가총액
                            'high_price': float(output.get('stck_hgpr', 0)),  # 고가
                            'low_price': float(output.get('stck_lwpr', 0)),  # 저가
                            'open_price': float(output.get('stck_oprc', 0)),  # 시가
                            'prev_close': float(output.get('stck_sdpr', 0)),  # 전일종가
                            'timestamp': datetime.now()
                        }
                    else:
                        self.logger.error(f"종목 정보 조회 실패: {result['msg1']}")
                        return None
                else:
                    error_text = await response.text()
                    self.logger.error(f"종목 정보 조회 API 오류: {response.status} - {error_text}")
                    return None
        
        except Exception as e:
            self.logger.error(f"종목 정보 조회 중 오류: {e}")
            return None
    
    async def place_order(self, symbol: str, side: str, quantity: int, 
                         price: Optional[float] = None, order_type: str = "limit") -> Dict[str, Any]:
        """주문 실행
        
        Args:
            symbol: 종목코드
            side: 매매구분 (buy/sell)
            quantity: 수량
            price: 가격 (지정가 주문시)
            order_type: 주문유형 (market/limit)
        
        Returns:
            주문 결과
        """
        await self.get_access_token()
        
        # 주문구분 코드 매핑
        order_div_map = {
            ('buy', 'market'): '01',   # 시장가 매수
            ('buy', 'limit'): '00',    # 지정가 매수
            ('sell', 'market'): '01',  # 시장가 매도
            ('sell', 'limit'): '00'    # 지정가 매도
        }
        
        order_div = order_div_map.get((side, order_type))
        if not order_div:
            raise ValueError(f"지원하지 않는 주문 유형: {side}, {order_type}")
        
        # 환경에 따른 TR_ID 설정 (모의투자: V, 실제투자: T)
        base_tr_id = "TTTC0802U" if side == "buy" else "TTTC0801U"
        tr_id = self._get_tr_id(base_tr_id)
        
        url = f"{self.base_url}/uapi/domestic-stock/v1/trading/order-cash"
        headers = {
            "authorization": f"Bearer {self.access_token}",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "tr_id": tr_id
        }
        
        data = {
            "CANO": self.account_no,
            "ACNT_PRDT_CD": self.account_product_cd,
            "PDNO": symbol,
            "ORD_DVSN": order_div,
            "ORD_QTY": str(quantity),
            "ORD_UNPR": str(int(price)) if price else "0"
        }
        
        try:
            async with self.session.post(url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['rt_cd'] == '0':
                        output = result['output']
                        return {
                            'order_id': output['KRX_FWDG_ORD_ORGNO'] + output['ODNO'],
                            'symbol': symbol,
                            'side': side,
                            'order_type': order_type,
                            'quantity': quantity,
                            'price': price,
                            'status': 'pending',
                            'message': result['msg1']
                        }
                    else:
                        self.logger.error(f"주문 실행 실패: {result['msg1']}")
                        return {
                            'error': result['msg1'],
                            'status': 'failed'
                        }
                else:
                    error_text = await response.text()
                    self.logger.error(f"주문 API 오류: {response.status} - {error_text}")
                    return {
                        'error': f"API 오류: {response.status}",
                        'status': 'failed'
                    }
        
        except Exception as e:
            self.logger.error(f"주문 실행 중 오류: {e}")
            return {
                'error': str(e),
                'status': 'failed'
            }
    
    async def cancel_order(self, order_id: str, symbol: str, quantity: int, price: float) -> Dict[str, Any]:
        """주문 취소
        
        Args:
            order_id: 주문번호
            symbol: 종목코드
            quantity: 수량
            price: 가격
        
        Returns:
            취소 결과
        """
        await self.get_access_token()
        
        # 주문번호에서 원주문기관번호와 주문번호 분리
        org_no = order_id[:5]
        odno = order_id[5:]
        
        url = f"{self.base_url}/uapi/domestic-stock/v1/trading/order-rvsecncl"
        headers = {
            "authorization": f"Bearer {self.access_token}",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "tr_id": self._get_tr_id("TTTC0803U")
        }
        
        data = {
            "CANO": self.account_no,
            "ACNT_PRDT_CD": self.account_product_cd,
            "KRX_FWDG_ORD_ORGNO": org_no,
            "ORGN_ODNO": odno,
            "ORD_DVSN": "00",
            "RVSE_CNCL_DVSN_CD": "02",  # 취소
            "ORD_QTY": str(quantity),
            "ORD_UNPR": str(int(price)),
            "QTY_ALL_ORD_YN": "Y"
        }
        
        try:
            async with self.session.post(url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['rt_cd'] == '0':
                        return {
                            'order_id': order_id,
                            'status': 'cancelled',
                            'message': result['msg1']
                        }
                    else:
                        self.logger.error(f"주문 취소 실패: {result['msg1']}")
                        return {
                            'error': result['msg1'],
                            'status': 'failed'
                        }
                else:
                    error_text = await response.text()
                    self.logger.error(f"주문 취소 API 오류: {response.status} - {error_text}")
                    return {
                        'error': f"API 오류: {response.status}",
                        'status': 'failed'
                    }
        
        except Exception as e:
            self.logger.error(f"주문 취소 중 오류: {e}")
            return {
                'error': str(e),
                'status': 'failed'
            }
    
    async def get_balance(self) -> Dict[str, Any]:
        """계좌 잔고 조회
        
        Returns:
            계좌 잔고 정보
        """
        await self.get_access_token()
        
        # 환경에 따른 TR_ID 설정 (모의투자: V, 실제투자: T)
        tr_id = self._get_tr_id("TTTC8434R")
        
        url = f"{self.base_url}/uapi/domestic-stock/v1/trading/inquire-balance"
        headers = {
            "authorization": f"Bearer {self.access_token}",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "tr_id": tr_id
        }
        
        params = {
            "CANO": self.account_no,
            "ACNT_PRDT_CD": self.account_product_cd,
            "AFHR_FLPR_YN": "N",
            "OFL_YN": "",
            "INQR_DVSN": "02",
            "UNPR_DVSN": "01",
            "FUND_STTL_ICLD_YN": "N",
            "FNCG_AMT_AUTO_RDPT_YN": "N",
            "PRCS_DVSN": "01",
            "CTX_AREA_FK100": "",
            "CTX_AREA_NK100": ""
        }
        
        try:
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['rt_cd'] == '0':
                        output1 = result['output1']
                        output2 = result['output2']
                        
                        return {
                            'cash_balance': float(output2[0]['dnca_tot_amt']),
                            'total_balance': float(output2[0]['tot_evlu_amt']),
                            'positions': [
                                {
                                    'symbol': item['pdno'],
                                    'quantity': int(item['hldg_qty']),
                                    'avg_price': float(item['pchs_avg_pric']),
                                    'current_price': float(item['prpr']),
                                    'eval_amount': float(item['evlu_amt']),
                                    'profit_loss': float(item['evlu_pfls_amt'])
                                }
                                for item in output1 if int(item['hldg_qty']) > 0
                            ]
                        }
                    else:
                        self.logger.error(f"잔고 조회 실패: {result['msg1']}")
                        return None
                else:
                    error_text = await response.text()
                    self.logger.error(f"잔고 조회 API 오류: {response.status} - {error_text}")
                    return None
        
        except Exception as e:
            self.logger.error(f"잔고 조회 중 오류: {e}")
            return None
    
    async def start_websocket(self, symbols: List[str], callback: Callable):
        """웹소켓 실시간 데이터 수신 시작
        
        Args:
            symbols: 구독할 종목 리스트
            callback: 데이터 수신 콜백 함수
        """
        await self.get_approval_key()
        
        try:
            self.ws_connection = await websockets.connect(
                self.ws_url,
                ping_interval=30,
                ping_timeout=10
            )
            
            # 구독 요청
            for symbol in symbols:
                subscribe_data = {
                    "header": {
                        "approval_key": self.approval_key,
                        "custtype": "P",
                        "tr_type": "1",
                        "content-type": "utf-8"
                    },
                    "body": {
                        "input": {
                            "tr_id": "H0STCNT0",
                            "tr_key": symbol
                        }
                    }
                }
                
                await self.ws_connection.send(json.dumps(subscribe_data))
                self.logger.info(f"웹소켓 구독 요청: {symbol}")
            
            # 데이터 수신 루프
            async for message in self.ws_connection:
                try:
                    data = json.loads(message)
                    await callback(data)
                except Exception as e:
                    self.logger.error(f"웹소켓 메시지 처리 오류: {e}")
        
        except Exception as e:
            self.logger.error(f"웹소켓 연결 오류: {e}")
            raise
    
    async def close(self):
        """API 연결 종료"""
        try:
            # 웹소켓 연결 종료
            if self.ws_connection:
                try:
                    await self.ws_connection.close()
                except Exception as e:
                    self.logger.debug(f"웹소켓 종료 중 오류 (무시됨): {e}")
            
            # HTTP 세션 종료
            if self.session:
                try:
                    if not self.session.closed:
                        await self.session.close()
                        # 세션이 완전히 닫힐 때까지 잠시 대기
                        await asyncio.sleep(0.1)
                except Exception as e:
                    self.logger.debug(f"세션 종료 중 오류 (무시됨): {e}")
            
            # 토큰 및 웹소켓 접속키 폐기 (네트워크 오류 시 무시)
            try:
                if self.access_token:
                    await self._revoke_access_token()
            except Exception as e:
                self.logger.debug(f"토큰 폐기 중 오류 (무시됨): {e}")
            
            try:
                if self.approval_key:
                    await self._revoke_approval_key()
            except Exception as e:
                self.logger.debug(f"접속키 폐기 중 오류 (무시됨): {e}")
            
            self.logger.info("KIS API 연결 종료")
        except Exception as e:
            self.logger.error(f"KIS API 종료 오류: {e}")