# -*- coding: utf-8 -*-
"""
실시간 시세 데이터 소스

한국투자증권 API를 통한 실시간 시세 데이터 수집 및 처리
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Set
from collections import deque
import numpy as np

from ..kis_api import KISApi
from ...utils.logger import get_logger
from ...utils.config_manager import ConfigManager
from ...utils.database_manager import DatabaseManager

class RealtimeSource:
    """
    실시간 시세 데이터 수집 소스
    
    동적 종목 선택, 실시간 데이터 수집, 기술적 지표 계산 등을 담당
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, kis_api: KISApi):
        """실시간 데이터 소스 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
            kis_api: KIS API 인스턴스
        """
        self.config = config_manager
        self.db = db_manager
        self.kis_api = kis_api
        self.logger = get_logger()
        
        # 설정 로드
        self._load_config()
        
        # 데이터 구조 초기화
        self._initialize_data_structures()
        
        # 콜백 관리
        self.data_callbacks = []
        
        # 수집 상태
        self.is_collecting = False
        self.collection_tasks = []
        
        # 성능 메트릭
        self.metrics = {
            'realtime_data_count': 0,
            'active_symbols_count': 0,
            'last_data_time': None,
            'errors_count': 0
        }
    
    def _load_config(self):
        """설정 로드"""
        data_config = self.config.get('data_collection', {})
        
        # 동적 종목 선택 설정
        self.dynamic_config = data_config.get('dynamic_symbol_selection', {})
        self.dynamic_enabled = self.dynamic_config.get('enabled', True)
        self.min_market_cap = self.dynamic_config.get('min_market_cap', 1000) * 100000000
        self.min_daily_volume = self.dynamic_config.get('min_daily_volume', 100) * 100000000
        self.max_spread = self.dynamic_config.get('max_spread', 0.5)
        self.max_symbols = self.dynamic_config.get('max_symbols', 20)
        self.monitoring_duration = self.dynamic_config.get('monitoring_duration', 60)
        
        # 수집 설정
        self.collection_interval = data_config.get('market_data_interval', 1)
        self.price_update_interval = data_config.get('price_update_interval', 1)
        self.max_cache_size = data_config.get('price_cache_size', 1000)
        self.data_retention_days = data_config.get('data_retention_days', 30)
        
        # 이동평균 윈도우 크기
        self.ma_windows = [5, 10, 20, 60]
    
    def _initialize_data_structures(self):
        """데이터 구조 초기화"""
        # 활성 종목 관리
        self.active_symbols = set()
        self.symbol_add_time = {}
        
        # 데이터 캐시
        self.price_cache = {}
        self.volume_cache = {}
        
        # 기술적 지표용 윈도우
        self.price_windows = {}
        self.volume_windows = {}
    
    async def initialize(self):
        """실시간 데이터 소스 초기화"""
        self.logger.info("실시간 데이터 소스 초기화 시작")
        
        try:
            # 데이터베이스 테이블 생성
            await self._create_database_tables()
            
            # 데이터 정리 작업 스케줄링
            asyncio.create_task(self._schedule_data_cleanup())
            
            self.logger.info("실시간 데이터 소스 초기화 완료")
            
        except Exception as e:
            self.logger.error(f"실시간 데이터 소스 초기화 오류: {e}")
            raise
    
    async def _create_database_tables(self):
        """데이터베이스 테이블 생성"""
        # 실시간 가격 데이터 테이블
        await self.db.execute("""
            CREATE TABLE IF NOT EXISTS realtime_prices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                price REAL NOT NULL,
                volume INTEGER,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                change_rate REAL,
                bid_price REAL,
                ask_price REAL
            )
        """)
        
        # 인덱스 생성
        await self.db.execute("""
            CREATE INDEX IF NOT EXISTS idx_realtime_symbol_timestamp 
            ON realtime_prices (symbol, timestamp)
        """)
        
        # 기술적 지표 테이블
        await self.db.execute("""
            CREATE TABLE IF NOT EXISTS technical_indicators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                indicator_type TEXT NOT NULL,
                value REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                window_size INTEGER
            )
        """)
        
        # 인덱스 생성
        await self.db.execute("""
            CREATE INDEX IF NOT EXISTS idx_technical_symbol_indicator_timestamp 
            ON technical_indicators (symbol, indicator_type, timestamp)
        """)
    
    def add_data_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """데이터 수신 콜백 추가
        
        Args:
            callback: 데이터 수신 시 호출될 콜백 함수
        """
        self.data_callbacks.append(callback)
    
    async def start_collection(self):
        """실시간 데이터 수집 시작"""
        if self.is_collecting:
            self.logger.warning("실시간 데이터 수집이 이미 진행 중입니다")
            return
        
        self.logger.info("실시간 데이터 수집 시작")
        self.is_collecting = True
        
        try:
            # 동적 종목 선택이 활성화된 경우
            if self.dynamic_enabled:
                # 초기 종목 선택
                await self._select_dynamic_symbols()
                
                # 주기적 종목 재선택 작업
                self.collection_tasks.append(
                    asyncio.create_task(self._periodic_symbol_reselection())
                )
            
            # 실시간 데이터 수집 작업
            self.collection_tasks.append(
                asyncio.create_task(self._collect_realtime_data())
            )
            
            # 기술적 지표 계산 작업
            self.collection_tasks.append(
                asyncio.create_task(self._calculate_technical_indicators())
            )
            
        except Exception as e:
            self.logger.error(f"실시간 데이터 수집 시작 오류: {e}")
            self.is_collecting = False
            raise
    
    async def stop_collection(self):
        """실시간 데이터 수집 중지"""
        if not self.is_collecting:
            return
        
        self.logger.info("실시간 데이터 수집 중지")
        self.is_collecting = False
        
        # 모든 수집 작업 취소
        for task in self.collection_tasks:
            if not task.done():
                task.cancel()
        
        # 작업 완료 대기
        if self.collection_tasks:
            await asyncio.gather(*self.collection_tasks, return_exceptions=True)
        
        self.collection_tasks.clear()
    
    async def add_symbol(self, symbol: str):
        """실시간 모니터링 종목 추가
        
        Args:
            symbol: 종목 코드
        """
        if symbol not in self.active_symbols:
            self.active_symbols.add(symbol)
            self.symbol_add_time[symbol] = time.time()
            
            # 데이터 윈도우 초기화
            self.price_windows[symbol] = {window: deque(maxlen=window) for window in self.ma_windows}
            self.volume_windows[symbol] = {window: deque(maxlen=window) for window in self.ma_windows}
            
            self.logger.info(f"실시간 모니터링 종목 추가: {symbol}")
    
    async def remove_symbol(self, symbol: str):
        """실시간 모니터링 종목 제거
        
        Args:
            symbol: 종목 코드
        """
        if symbol in self.active_symbols:
            self.active_symbols.discard(symbol)
            self.symbol_add_time.pop(symbol, None)
            
            # 캐시 정리
            self.price_cache.pop(symbol, None)
            self.volume_cache.pop(symbol, None)
            self.price_windows.pop(symbol, None)
            self.volume_windows.pop(symbol, None)
            
            self.logger.info(f"실시간 모니터링 종목 제거: {symbol}")
    
    async def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """최신 가격 정보 조회
        
        Args:
            symbol: 종목 코드
            
        Returns:
            최신 가격 정보 또는 None
        """
        return self.price_cache.get(symbol)
    
    async def get_price_history(self, symbol: str, hours: int = 24) -> List[Dict[str, Any]]:
        """가격 히스토리 조회
        
        Args:
            symbol: 종목 코드
            hours: 조회할 시간 (시간)
            
        Returns:
            가격 히스토리 리스트
        """
        since_time = datetime.now() - timedelta(hours=hours)
        
        query = """
            SELECT symbol, price, volume, timestamp, change_rate, bid_price, ask_price
            FROM realtime_prices
            WHERE symbol = ? AND timestamp >= ?
            ORDER BY timestamp DESC
        """
        
        async with self.db.get_connection() as db:
            cursor = await db.execute(query, (symbol, since_time))
            rows = await cursor.fetchall()
        
        return [
            {
                'symbol': row[0],
                'price': row[1],
                'volume': row[2],
                'timestamp': row[3],
                'change_rate': row[4],
                'bid_price': row[5],
                'ask_price': row[6]
            }
            for row in rows
        ]
    
    async def _select_dynamic_symbols(self):
        """동적 종목 선택"""
        try:
            self.logger.info("동적 종목 선택 시작")
            
            # 조건에 맞는 종목 찾기 (stock_master 테이블 사용)
            query = f"""
                SELECT symbol, market_cap, previous_volume as daily_volume, base_price as current_price
                FROM stock_master 
                WHERE market_cap >= {self.min_market_cap}
                  AND previous_volume >= {self.min_daily_volume}
                  AND base_price > 0
                  AND market_cap IS NOT NULL
                  AND previous_volume IS NOT NULL
                ORDER BY previous_volume DESC
                LIMIT {self.max_symbols}
            """
            
            async with self.db.get_connection() as db:
                cursor = await db.execute(query)
                rows = await cursor.fetchall()
            
            # 기존 종목 정리
            old_symbols = self.active_symbols.copy()
            new_symbols = {row[0] for row in rows}
            
            # 제거할 종목들
            for symbol in old_symbols - new_symbols:
                await self.remove_symbol(symbol)
            
            # 추가할 종목들
            for symbol in new_symbols - old_symbols:
                await self.add_symbol(symbol)
            
            self.logger.info(f"동적 종목 선택 완료: {len(new_symbols)}개 종목")
            
        except Exception as e:
            self.logger.error(f"동적 종목 선택 오류: {e}")
    
    async def _periodic_symbol_reselection(self):
        """주기적 종목 재선택"""
        while self.is_collecting:
            try:
                await asyncio.sleep(self.monitoring_duration * 60)  # 분 단위를 초로 변환
                
                if self.is_collecting:
                    await self._select_dynamic_symbols()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"주기적 종목 재선택 오류: {e}")
                await asyncio.sleep(60)  # 오류 시 1분 대기
    
    async def _collect_realtime_data(self):
        """실시간 데이터 수집"""
        while self.is_collecting:
            try:
                if not self.active_symbols:
                    await asyncio.sleep(self.collection_interval)
                    continue
                
                # 각 종목의 실시간 데이터 수집
                for symbol in list(self.active_symbols):
                    try:
                        # KIS API를 통한 실시간 데이터 조회
                        data = await self.kis_api.get_current_price(symbol)
                        
                        if data:
                            await self._process_realtime_data(symbol, data)
                            
                    except Exception as e:
                        self.logger.error(f"종목 {symbol} 실시간 데이터 수집 오류: {e}")
                        self.metrics['errors_count'] += 1
                
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"실시간 데이터 수집 오류: {e}")
                await asyncio.sleep(5)
    
    async def _process_realtime_data(self, symbol: str, data: Dict[str, Any]):
        """실시간 데이터 처리
        
        Args:
            symbol: 종목 코드
            data: 실시간 데이터
        """
        try:
            # 데이터 파싱
            price = float(data.get('stck_prpr', 0))
            volume = int(data.get('acml_vol', 0))
            change_rate = float(data.get('prdy_ctrt', 0))
            bid_price = float(data.get('bidp1', 0))
            ask_price = float(data.get('askp1', 0))
            
            if price <= 0:
                return
            
            # 캐시 업데이트
            price_data = {
                'symbol': symbol,
                'price': price,
                'volume': volume,
                'timestamp': datetime.now(),
                'change_rate': change_rate,
                'bid_price': bid_price,
                'ask_price': ask_price
            }
            
            self.price_cache[symbol] = price_data
            self.volume_cache[symbol] = volume
            
            # 윈도우 데이터 업데이트
            if symbol in self.price_windows:
                for window in self.ma_windows:
                    self.price_windows[symbol][window].append(price)
                    self.volume_windows[symbol][window].append(volume)
            
            # 데이터베이스 저장
            await self.db.execute(
                """
                INSERT INTO realtime_prices 
                (symbol, price, volume, timestamp, change_rate, bid_price, ask_price)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                (symbol, price, volume, datetime.now(), change_rate, bid_price, ask_price)
            )
            
            # 콜백 호출
            for callback in self.data_callbacks:
                try:
                    await callback('realtime_data', price_data)
                except Exception as e:
                    self.logger.error(f"실시간 데이터 콜백 오류: {e}")
            
            # 메트릭 업데이트
            self.metrics['realtime_data_count'] += 1
            self.metrics['last_data_time'] = datetime.now()
            self.metrics['active_symbols_count'] = len(self.active_symbols)
            
        except Exception as e:
            self.logger.error(f"실시간 데이터 처리 오류 ({symbol}): {e}")
    
    async def _calculate_technical_indicators(self):
        """기술적 지표 계산"""
        while self.is_collecting:
            try:
                for symbol in list(self.active_symbols):
                    if symbol in self.price_windows:
                        await self._calculate_moving_averages(symbol)
                
                await asyncio.sleep(60)  # 1분마다 계산
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"기술적 지표 계산 오류: {e}")
                await asyncio.sleep(60)
    
    async def _calculate_moving_averages(self, symbol: str):
        """이동평균 계산
        
        Args:
            symbol: 종목 코드
        """
        try:
            for window in self.ma_windows:
                if len(self.price_windows[symbol][window]) >= window:
                    prices = list(self.price_windows[symbol][window])
                    ma_value = np.mean(prices)
                    
                    # 데이터베이스 저장
                    await self.db.execute(
                        """
                        INSERT INTO technical_indicators 
                        (symbol, indicator_type, value, timestamp, window_size)
                        VALUES (?, ?, ?, ?, ?)
                        """,
                        (symbol, 'MA', ma_value, datetime.now(), window)
                    )
                    
        except Exception as e:
            self.logger.error(f"이동평균 계산 오류 ({symbol}): {e}")
    
    async def _schedule_data_cleanup(self):
        """데이터 정리 작업 스케줄링"""
        while True:
            try:
                await asyncio.sleep(3600)  # 1시간마다 실행
                await self._cleanup_old_data()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"데이터 정리 작업 오류: {e}")
    
    async def _cleanup_old_data(self):
        """오래된 데이터 정리"""
        try:
            cutoff_time = datetime.now() - timedelta(days=self.data_retention_days)
            
            # 오래된 실시간 가격 데이터 삭제
            await self.db.execute(
                "DELETE FROM realtime_prices WHERE timestamp < ?",
                (cutoff_time,)
            )
            
            # 오래된 기술적 지표 데이터 삭제
            await self.db.execute(
                "DELETE FROM technical_indicators WHERE timestamp < ?",
                (cutoff_time,)
            )
            
            self.logger.info(f"오래된 실시간 데이터 정리 완료 (기준: {cutoff_time})")
            
        except Exception as e:
            self.logger.error(f"데이터 정리 오류: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """성능 메트릭 조회
        
        Returns:
            성능 메트릭 딕셔너리
        """
        return self.metrics.copy()
    
    async def close(self):
        """리소스 정리"""
        self.logger.info("실시간 데이터 소스 종료")
        
        # 데이터 수집 중지
        await self.stop_collection()
        
        # 캐시 정리
        self.active_symbols.clear()
        self.price_cache.clear()
        self.volume_cache.clear()
        self.price_windows.clear()
        self.volume_windows.clear()