# -*- coding: utf-8 -*-
"""
주식 기본 정보 데이터 소스

한국투자증권 마스터 파일을 통한 주식 기본 정보 수집 및 DB 저장
"""

import asyncio
import os
import zipfile
import ssl
import urllib.request
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from ...utils.logger import get_logger
from ...utils.config_manager import ConfigManager
from ...utils.database_manager import DatabaseManager


class StockInfoSource:
    """
    주식 기본 정보 데이터 수집 소스
    
    KOSPI, KOSDAQ, 업종, 테마 정보를 수집하고 데이터베이스에 저장
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        """주식 정보 소스 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
        """
        self.config = config_manager
        self.db = db_manager
        self.logger = get_logger()
        
        # 설정 로드
        self._load_config()
        
        # 상태 초기화
        self.last_update = None
        self.update_in_progress = False
        
        # 콜백 함수들
        self.update_callbacks = []
        
    def _load_config(self):
        """설정 로드"""
        data_config = self.config.get('data_collection', {})
        
        # 마스터 파일 다운로드 URL 설정
        self.master_urls = {
            'kospi': 'https://new.real.download.dws.co.kr/common/master/kospi_code.mst.zip',
            'kospi_nxt': 'https://new.real.download.dws.co.kr/common/master/nxt_kospi_code.mst.zip',
            'kosdaq': 'https://new.real.download.dws.co.kr/common/master/kosdaq_code.mst.zip',
            'kosdaq_nxt': 'https://new.real.download.dws.co.kr/common/master/nxt_kosdaq_code.mst.zip',
            'sector': 'https://new.real.download.dws.co.kr/common/master/idxcode.mst.zip'
        }
        
        # 캐시 디렉토리 설정
        self.cache_dir = Path(data_config.get('cache_dir', './cache'))
        self.cache_dir.mkdir(exist_ok=True)
        
        # SSL 설정
        ssl._create_default_https_context = ssl._create_unverified_context
        
    async def initialize(self):
        """주식 정보 소스 초기화"""
        self.logger.info("주식 정보 소스 초기화 시작")
        
        try:
            # 데이터베이스 테이블 생성
            await self._create_database_tables()
            
            # 마지막 업데이트 시간 로드
            await self._load_last_update_time()
            
            self.logger.info("주식 정보 소스 초기화 완료")
            
        except Exception as e:
            self.logger.error(f"주식 정보 소스 초기화 오류: {e}")
            raise
    
    async def _create_database_tables(self):
        """데이터베이스 테이블 생성"""
        
        # 주식 기본 정보 테이블 (KOSPI/KOSDAQ 통합) - 업종 분류 정보 포함
        await self.db.execute("""
            CREATE TABLE IF NOT EXISTS stock_master (
                symbol TEXT PRIMARY KEY,                    -- 종목코드 (단축코드)
                standard_code TEXT,                         -- 표준코드 (12자리)
                name TEXT NOT NULL,                         -- 종목명 (한글)
                market TEXT NOT NULL,                       -- 시장구분 (KOSPI/KOSDAQ)
                base_price REAL,                           -- 기준가 (원)
                trading_unit INTEGER,                      -- 매매수량단위 (주)
                margin_rate REAL,                          -- 증거금비율 (%)
                previous_volume INTEGER,                   -- 전일거래량 (주)
                listing_date TEXT,                         -- 상장일자 (YYYYMMDD)
                listed_shares INTEGER,                     -- 상장주수 (천주)
                capital REAL,                              -- 자본금 (원)
                sales REAL,                                -- 매출액 (억원)
                operating_profit REAL,                     -- 영업이익 (억원)
                net_income REAL,                           -- 당기순이익 (억원)
                roe REAL,                                  -- ROE 자기자본이익률 (%)
                market_cap REAL,                           -- 시가총액 (억원)
                sector_large TEXT,                         -- 지수업종대분류
                sector_medium TEXT,                        -- 지수업종중분류
                sector_small TEXT,                         -- 지수업종소분류
                nxt INTEGER DEFAULT 0,                     -- NXT 종목 여부 (1: NXT 종목, 0: 일반 종목)
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 업데이트 시간
            )
        """)
        
        # 업종 정보 테이블
        await self.db.execute("""
            CREATE TABLE IF NOT EXISTS sector_master (
                sector_code TEXT PRIMARY KEY,              -- 업종코드
                sector_name TEXT NOT NULL,                 -- 업종명
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 업데이트 시간
            )
        """)
        
        # 테마 정보 테이블은 더 이상 사용하지 않음
        
        # 업데이트 로그 테이블
        await self.db.execute("""
            CREATE TABLE IF NOT EXISTS stock_info_update_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,      -- 로그 ID (자동증가)
                update_type TEXT NOT NULL,                 -- 업데이트 유형 (stock_master/sector/theme)
                total_count INTEGER,                       -- 총 처리 대상 건수
                success_count INTEGER,                     -- 성공 처리 건수
                error_count INTEGER,                       -- 오류 발생 건수
                start_time DATETIME,                       -- 시작 시간
                end_time DATETIME,                         -- 종료 시간
                status TEXT,                               -- 상태 (running/completed/failed)
                error_message TEXT                         -- 오류 메시지
            )
        """)
    
    async def _load_last_update_time(self):
        """마지막 업데이트 시간 로드"""
        try:
            async with self.db.get_connection() as db:
                cursor = await db.execute(
                    "SELECT MAX(end_time) FROM stock_info_update_log WHERE status = 'completed'"
                )
                result = await cursor.fetchone()
                if result and result[0]:
                    self.last_update = datetime.fromisoformat(result[0])
                else:
                    self.last_update = None
                    
        except Exception as e:
            self.logger.error(f"마지막 업데이트 시간 로드 오류: {e}")
            self.last_update = None
    
    async def update_stock_info(self) -> bool:
        """주식 정보 업데이트
        
        Returns:
            bool: 업데이트 성공 여부
        """
        if self.update_in_progress:
            self.logger.warning("주식 정보 업데이트가 이미 진행 중입니다")
            return False
            
        self.update_in_progress = True
        start_time = datetime.now()
        
        try:
            # 업데이트 로그 시작
            log_id = await self._start_update_log('stock_master', start_time)
            
            total_count = 0
            success_count = 0
            error_count = 0
            
            # 1. KOSPI 데이터 업데이트
            self.logger.info("KOSPI 데이터 업데이트 시작")
            kospi_result = await self._update_kospi_data()
            total_count += kospi_result['total']
            success_count += kospi_result['success']
            error_count += kospi_result['error']
            
            # 2. KOSDAQ 데이터 업데이트
            self.logger.info("KOSDAQ 데이터 업데이트 시작")
            kosdaq_result = await self._update_kosdaq_data()
            total_count += kosdaq_result['total']
            success_count += kosdaq_result['success']
            error_count += kosdaq_result['error']
            
            # 3. 업종 데이터 업데이트
            self.logger.info("업종 데이터 업데이트 시작")
            sector_result = await self._update_sector_data()
            total_count += sector_result['total']
            success_count += sector_result['success']
            error_count += sector_result['error']
            
            # 4. NXT 데이터 업데이트 (KOSPI NXT)
            self.logger.info("KOSPI NXT 데이터 업데이트 시작")
            kospi_nxt_result = await self._update_nxt_data('kospi_nxt')
            total_count += kospi_nxt_result['total']
            success_count += kospi_nxt_result['success']
            error_count += kospi_nxt_result['error']
            
            # 5. NXT 데이터 업데이트 (KOSDAQ NXT)
            self.logger.info("KOSDAQ NXT 데이터 업데이트 시작")
            kosdaq_nxt_result = await self._update_nxt_data('kosdaq_nxt')
            total_count += kosdaq_nxt_result['total']
            success_count += kosdaq_nxt_result['success']
            error_count += kosdaq_nxt_result['error']
            
            # 테마 데이터 업데이트는 더 이상 사용하지 않음
            
            # 업데이트 로그 완료
            end_time = datetime.now()
            await self._complete_update_log(
                log_id, total_count, success_count, error_count, end_time, 'completed'
            )
            
            self.last_update = end_time
            
            self.logger.info(
                f"주식 정보 업데이트 완료 - 총 {total_count}건, 성공 {success_count}건, 실패 {error_count}건"
            )
            
            # 업데이트 완료 콜백 호출
            for callback in self.update_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback('stock_info', {'status': 'completed'})
                    else:
                        callback('stock_info', {'status': 'completed'})
                except Exception as e:
                    self.logger.error(f"콜백 호출 중 오류: {e}")
            
            return True
            
        except Exception as e:
            error_msg = f"주식 정보 업데이트 오류: {e}"
            self.logger.error(error_msg)
            
            # 오류 로그 기록
            end_time = datetime.now()
            await self._complete_update_log(
                log_id, total_count, success_count, error_count, end_time, 'failed', error_msg
            )
            
            # 업데이트 실패 콜백 호출
            for callback in self.update_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback('stock_info', {'status': 'failed', 'error': str(e)})
                    else:
                        callback('stock_info', {'status': 'failed', 'error': str(e)})
                except Exception as cb_e:
                    self.logger.error(f"콜백 호출 중 오류: {cb_e}")
            
            return False
            
        finally:
            self.update_in_progress = False
    
    async def _update_kospi_data(self) -> Dict[str, int]:
        """KOSPI 데이터 업데이트"""
        result = {'total': 0, 'success': 0, 'error': 0}
        
        try:
            # KOSPI 마스터 파일 다운로드 및 파싱
            df = await self._download_and_parse_kospi()
            result['total'] = len(df)
            
            # 데이터베이스에 저장
            async with self.db.get_connection() as db:
                for _, row in df.iterrows():
                    try:
                        await self._insert_stock_master(db, row, 'KOSPI')
                        result['success'] += 1
                    except Exception as e:
                        self.logger.error(f"KOSPI 종목 {row.get('단축코드', 'Unknown')} 저장 오류: {e}")
                        result['error'] += 1
                        
                await db.commit()
                
        except Exception as e:
            self.logger.error(f"KOSPI 데이터 업데이트 오류: {e}")
            result['error'] = result['total']
            result['success'] = 0
            
        return result
    
    async def _update_kosdaq_data(self) -> Dict[str, int]:
        """KOSDAQ 데이터 업데이트"""
        result = {'total': 0, 'success': 0, 'error': 0}
        
        try:
            # KOSDAQ 마스터 파일 다운로드 및 파싱
            df = await self._download_and_parse_kosdaq()
            result['total'] = len(df)
            
            # 데이터베이스에 저장
            async with self.db.get_connection() as db:
                for _, row in df.iterrows():
                    try:
                        await self._insert_stock_master(db, row, 'KOSDAQ')
                        result['success'] += 1
                    except Exception as e:
                        self.logger.error(f"KOSDAQ 종목 {row.get('단축코드', 'Unknown')} 저장 오류: {e}")
                        result['error'] += 1
                        
                await db.commit()
                
        except Exception as e:
            self.logger.error(f"KOSDAQ 데이터 업데이트 오류: {e}")
            result['error'] = result['total']
            result['success'] = 0
            
        return result
    
    async def _update_sector_data(self) -> Dict[str, int]:
        """업종 데이터 업데이트"""
        result = {'total': 0, 'success': 0, 'error': 0}
        
        try:
            # 업종 마스터 파일 다운로드 및 파싱
            df = await self._download_and_parse_sector()
            result['total'] = len(df)
            
            # 데이터베이스에 저장
            async with self.db.get_connection() as db:
                # 기존 데이터 삭제
                await db.execute("DELETE FROM sector_master")
                
                for _, row in df.iterrows():
                    try:
                        await db.execute(
                            "INSERT OR REPLACE INTO sector_master (sector_code, sector_name) VALUES (?, ?)",
                            (row['업종코드'], row['업종명'])
                        )
                        result['success'] += 1
                    except Exception as e:
                        self.logger.error(f"업종 {row.get('업종코드', 'Unknown')} 저장 오류: {e}")
                        result['error'] += 1
                        
                await db.commit()
                
        except Exception as e:
            self.logger.error(f"업종 데이터 업데이트 오류: {e}")
            result['error'] = result['total']
            result['success'] = 0
            
        return result
    
    # 테마 데이터 업데이트 함수는 제거됨 (더 이상 사용하지 않음)
    
    async def _download_and_parse_kospi(self) -> pd.DataFrame:
        """KOSPI 마스터 파일 다운로드 및 파싱"""
        # 파일 다운로드
        zip_path = self.cache_dir / 'kospi_code.zip'
        urllib.request.urlretrieve(self.master_urls['kospi'], str(zip_path))
        
        # 압축 해제
        with zipfile.ZipFile(zip_path) as zip_file:
            zip_file.extractall(self.cache_dir)
        
        # 파일 파싱
        mst_path = self.cache_dir / 'kospi_code.mst'
        tmp_file1 = self.cache_dir / 'kospi_part1.tmp'
        tmp_file2 = self.cache_dir / 'kospi_part2.tmp'
        
        # 파일 분할 (바이너리 모드로 읽어서 인코딩 처리)
        with open(mst_path, 'rb') as f, \
             open(tmp_file1, 'w', encoding='utf-8') as wf1, \
             open(tmp_file2, 'w', encoding='utf-8') as wf2:
            
            for line in f:
                try:
                    # cp949로 디코딩 시도
                    row = line.decode('cp949')
                except UnicodeDecodeError:
                    try:
                        # euc-kr로 디코딩 시도
                        row = line.decode('euc-kr')
                    except UnicodeDecodeError:
                        # utf-8로 디코딩 시도
                        row = line.decode('utf-8', errors='ignore')
                
                # 첫 번째 부분 (기본 정보)
                rf1 = row[0:len(row) - 228]
                rf1_1 = rf1[0:9].rstrip()  # 단축코드
                rf1_2 = rf1[9:21].rstrip()  # 표준코드
                rf1_3 = rf1[21:].strip()  # 한글명
                wf1.write(f"{rf1_1},{rf1_2},{rf1_3}\n")
                
                # 두 번째 부분 (상세 정보)
                rf2 = row[-228:]
                wf2.write(rf2)
        
        # DataFrame 생성
        part1_columns = ['단축코드', '표준코드', '한글명']
        df1 = pd.read_csv(tmp_file1, header=None, names=part1_columns, encoding='utf-8')
        
        # KOSPI 필드 스펙 (샘플 코드 참조)
        field_specs = [2, 1, 4, 4, 4,
                       1, 1, 1, 1, 1,
                       1, 1, 1, 1, 1,
                       1, 1, 1, 1, 1,
                       1, 1, 1, 1, 1,
                       1, 1, 1, 1, 1,
                       1, 9, 5, 5, 1,
                       1, 1, 2, 1, 1,
                       1, 2, 2, 2, 3,
                       1, 3, 12, 12, 8,
                       15, 21, 2, 7, 1,
                       1, 1, 1, 1, 9,
                       9, 9, 5, 9, 8,
                       9, 3, 1, 1, 1
                       ]
        
        part2_columns = ['그룹코드', '시가총액규모', '지수업종대분류', '지수업종중분류', '지수업종소분류',
                        '제조업', '저유동성', '지배구조지수종목', 'KOSPI200섹터업종', 'KOSPI100',
                        'KOSPI50', 'KRX', 'ETP', 'ELW발행', 'KRX100',
                        'KRX자동차', 'KRX반도체', 'KRX바이오', 'KRX은행', 'SPAC',
                        'KRX에너지화학', 'KRX철강', '단기과열', 'KRX미디어통신', 'KRX건설',
                        'Non1', 'KRX증권', 'KRX선박', 'KRX섹터_보험', 'KRX섹터_운송',
                        'SRI', '기준가', '매매수량단위', '시간외수량단위', '거래정지',
                        '정리매매', '관리종목', '시장경고', '경고예고', '불성실공시',
                        '우회상장', '락구분', '액면변경', '증자구분', '증거금비율',
                        '신용가능', '신용기간', '전일거래량', '액면가', '상장일자',
                        '상장주수', '자본금', '결산월', '공모가', '우선주',
                        '공매도과열', '이상급등', 'KRX300', 'KOSPI', '매출액',
                        '영업이익', '경상이익', '당기순이익', 'ROE', '기준년월',
                        '시가총액', '그룹사코드', '회사신용한도초과', '담보대출가능', '대주가능'
                        ]
        
        df2 = pd.read_fwf(tmp_file2, widths=field_specs, names=part2_columns)
        
        # 데이터 병합
        df = pd.merge(df1, df2, how='outer', left_index=True, right_index=True)
        
        # 임시 파일 정리
        for temp_file in [zip_path, mst_path, tmp_file1, tmp_file2]:
            if temp_file.exists():
                temp_file.unlink()
        
        return df
    
    async def _download_and_parse_kosdaq(self) -> pd.DataFrame:
        """KOSDAQ 마스터 파일 다운로드 및 파싱"""
        # 파일 다운로드
        zip_path = self.cache_dir / 'kosdaq_code.zip'
        urllib.request.urlretrieve(self.master_urls['kosdaq'], str(zip_path))
        
        # 압축 해제
        with zipfile.ZipFile(zip_path) as zip_file:
            zip_file.extractall(self.cache_dir)
        
        # 파일 파싱
        mst_path = self.cache_dir / 'kosdaq_code.mst'
        tmp_file1 = self.cache_dir / 'kosdaq_part1.tmp'
        tmp_file2 = self.cache_dir / 'kosdaq_part2.tmp'
        
        # 파일 분할 (바이너리 모드로 읽어서 인코딩 처리)
        with open(mst_path, 'rb') as f, \
             open(tmp_file1, 'w', encoding='utf-8') as wf1, \
             open(tmp_file2, 'w', encoding='utf-8') as wf2:
            
            for line in f:
                try:
                    # cp949로 디코딩 시도
                    row = line.decode('cp949')
                except UnicodeDecodeError:
                    try:
                        # euc-kr로 디코딩 시도
                        row = line.decode('euc-kr')
                    except UnicodeDecodeError:
                        # utf-8로 디코딩 시도
                        row = line.decode('utf-8', errors='ignore')
                
                # 첫 번째 부분 (기본 정보)
                rf1 = row[0:len(row) - 222]
                rf1_1 = rf1[0:9].rstrip()  # 단축코드
                rf1_2 = rf1[9:21].rstrip()  # 표준코드
                rf1_3 = rf1[21:].strip()  # 한글종목명
                wf1.write(f"{rf1_1},{rf1_2},{rf1_3}\n")
                
                # 두 번째 부분 (상세 정보)
                rf2 = row[-222:]
                wf2.write(rf2)
        
        # DataFrame 생성
        part1_columns = ['단축코드', '표준코드', '한글종목명']
        df1 = pd.read_csv(tmp_file1, header=None, names=part1_columns, encoding='utf-8')
        
        # KOSDAQ 필드 스펙 (KIS API 샘플 코드 기준)
        field_specs = [2, 1,
                       4, 4, 4, 1, 1,
                       1, 1, 1, 1, 1,
                       1, 1, 1, 1, 1,
                       1, 1, 1, 1, 1,
                       1, 1, 1, 1, 9,
                       5, 5, 1, 1, 1,
                       2, 1, 1, 1, 2,
                       2, 2, 3, 1, 3,
                       12, 12, 8, 15, 21,
                       2, 7, 1, 1, 1,
                       1, 9, 9, 9, 5,
                       9, 8, 9, 3, 1,
                       1, 1
                       ]
        
        part2_columns = ['증권그룹구분코드','시가총액 규모 구분 코드 유가',
                         '지수업종 대분류 코드','지수 업종 중분류 코드','지수업종 소분류 코드','벤처기업 여부 (Y/N)',
                         '저유동성종목 여부','KRX 종목 여부','ETP 상품구분코드','KRX100 종목 여부 (Y/N)',
                         'KRX 자동차 여부','KRX 반도체 여부','KRX 바이오 여부','KRX 은행 여부','기업인수목적회사여부',
                         'KRX 에너지 화학 여부','KRX 철강 여부','단기과열종목구분코드','KRX 미디어 통신 여부',
                         'KRX 건설 여부','(코스닥)투자주의환기종목여부','KRX 증권 구분','KRX 선박 구분',
                         'KRX섹터지수 보험여부','KRX섹터지수 운송여부','KOSDAQ150지수여부 (Y,N)','주식 기준가',
                         '정규 시장 매매 수량 단위','시간외 시장 매매 수량 단위','거래정지 여부','정리매매 여부',
                         '관리 종목 여부','시장 경고 구분 코드','시장 경고위험 예고 여부','불성실 공시 여부',
                         '우회 상장 여부','락구분 코드','액면가 변경 구분 코드','증자 구분 코드','증거금 비율',
                         '신용주문 가능 여부','신용기간','전일 거래량','주식 액면가','주식 상장 일자','상장 주수(천)',
                         '자본금','결산 월','공모 가격','우선주 구분 코드','공매도과열종목여부','이상급등종목여부',
                         'KRX300 종목 여부 (Y/N)','매출액','영업이익','경상이익','단기순이익','ROE(자기자본이익률)',
                         '기준년월','전일기준 시가총액 (억)','그룹사 코드','회사신용한도초과여부','담보대출가능여부','대주가능여부'
                         ]
        
        df2 = pd.read_fwf(tmp_file2, widths=field_specs, names=part2_columns)
        
        # 데이터 병합
        df = pd.merge(df1, df2, how='outer', left_index=True, right_index=True)
        
        # 임시 파일 정리
        for temp_file in [zip_path, mst_path, tmp_file1, tmp_file2]:
            if temp_file.exists():
                temp_file.unlink()
        
        return df
    
    async def _download_and_parse_sector(self) -> pd.DataFrame:
        """업종 마스터 파일 다운로드 및 파싱"""
        # 파일 다운로드
        zip_path = self.cache_dir / 'idxcode.zip'
        urllib.request.urlretrieve(self.master_urls['sector'], str(zip_path))
        
        # 압축 해제
        with zipfile.ZipFile(zip_path) as zip_file:
            zip_file.extractall(self.cache_dir)
        
        # 파일 파싱
        mst_path = self.cache_dir / 'idxcode.mst'
        
        data = []
        with open(mst_path, 'r', encoding='cp949') as f:
            for row in f:
                sector_code = row[1:5]  # 업종코드 4자리 (맨 앞 1자리 제거)
                sector_name_raw = row[3:43].rstrip()  # 업종명 (코드 포함)
                
                # 업종명에서 앞의 코드 부분 제거 (예: "01종합" -> "종합")
                if len(sector_name_raw) > 2 and sector_name_raw[:2].isdigit():
                    sector_name = sector_name_raw[2:].strip()
                else:
                    sector_name = sector_name_raw.strip()
                
                # 빈 업종명 처리
                if not sector_name:
                    sector_name = f"업종{sector_code}"
                    
                data.append({'업종코드': sector_code, '업종명': sector_name})
        
        df = pd.DataFrame(data)
        
        # 임시 파일 정리
        for temp_file in [zip_path, mst_path]:
            if temp_file.exists():
                temp_file.unlink()
        
        return df
    
    # 테마 파싱 함수는 제거됨 (더 이상 사용하지 않음)
    
    async def _insert_stock_master(self, db, row, market: str):
        """주식 마스터 데이터 삽입"""
        # 숫자 필드 안전 변환 함수
        def safe_float(value, default=None):
            try:
                if pd.isna(value) or value == '' or value == ' ':
                    return default
                return float(str(value).strip())
            except (ValueError, TypeError):
                return default
        
        def safe_int(value, default=None):
            try:
                if pd.isna(value) or value == '' or value == ' ':
                    return default
                return int(float(str(value).strip()))
            except (ValueError, TypeError):
                return default
        
        def safe_str(value, default=''):
            try:
                if pd.isna(value):
                    return default
                return str(value).strip()
            except:
                return default
        
        # KOSPI와 KOSDAQ의 컬럼명이 다르므로 적절히 매핑
        if market == 'KOSPI':
            symbol = safe_str(row.get('단축코드'))
            standard_code = safe_str(row.get('표준코드'))
            name = safe_str(row.get('한글명'))
            base_price = safe_float(row.get('기준가'))
            trading_unit = safe_int(row.get('매매수량단위'))
            after_hours_unit = safe_int(row.get('시간외수량단위'))
            margin_rate = safe_float(row.get('증거금비율'))
            previous_volume = safe_int(row.get('전일거래량'))
            face_value = safe_float(row.get('액면가'))
            listing_date = safe_str(row.get('상장일자'))
            listed_shares = safe_int(row.get('상장주수'))
            # 자본금 필드 파싱 (전체 값 사용)
            capital_raw = safe_str(row.get('자본금'))
            capital = safe_float(capital_raw)
            settlement_month = safe_str(row.get('결산월'))
            public_price = safe_float(row.get('공모가'))
            sales = safe_float(row.get('매출액'))
            operating_profit = safe_float(row.get('영업이익'))
            ordinary_profit = safe_float(row.get('경상이익'))
            net_income = safe_float(row.get('당기순이익'))
            roe = safe_float(row.get('ROE'))
            base_date = safe_str(row.get('기준년월'))
            market_cap = safe_float(row.get('시가총액'))
            group_code = safe_str(row.get('그룹사코드'))
            # 업종 분류 정보 추출
            sector_large = safe_str(row.get('지수업종대분류'))
            sector_medium = safe_str(row.get('지수업종중분류'))
            sector_small = safe_str(row.get('지수업종소분류'))
        else:  # KOSDAQ
            symbol = safe_str(row.get('단축코드'))
            standard_code = safe_str(row.get('표준코드'))
            name = safe_str(row.get('한글종목명'))
            base_price = safe_float(row.get('주식 기준가'))
            trading_unit = safe_int(row.get('정규 시장 매매 수량 단위'))
            after_hours_unit = safe_int(row.get('시간외 시장 매매 수량 단위'))
            margin_rate = safe_float(row.get('증거금 비율'))
            previous_volume = safe_int(row.get('전일 거래량'))
            face_value = safe_float(row.get('주식 액면가'))
            listing_date = safe_str(row.get('주식 상장 일자'))
            listed_shares = safe_int(row.get('상장 주수(천)'))
            # 자본금 필드 파싱 (전체 값 사용)
            capital_raw = safe_str(row.get('자본금'))
            capital = safe_float(capital_raw)
            settlement_month = safe_str(row.get('결산 월'))
            public_price = safe_float(row.get('공모 가격'))
            sales = safe_float(row.get('매출액'))
            operating_profit = safe_float(row.get('영업이익'))
            ordinary_profit = safe_float(row.get('경상이익'))
            net_income = safe_float(row.get('단기순이익'))
            roe = safe_float(row.get('ROE(자기자본이익률)'))
            base_date = safe_str(row.get('기준년월'))
            market_cap = safe_float(row.get('전일기준 시가총액 (억)'))
            group_code = safe_str(row.get('그룹사 코드'))
            # 업종 분류 정보 추출 (KOSDAQ에는 해당 필드가 없을 수 있음)
            sector_large = safe_str(row.get('지수업종대분류'))
            sector_medium = safe_str(row.get('지수업종중분류'))
            sector_small = safe_str(row.get('지수업종소분류'))
        
        # 데이터베이스에 삽입 (업종 분류 정보 포함)
        await db.execute("""
            INSERT OR REPLACE INTO stock_master (
                symbol, standard_code, name, market,
                base_price, trading_unit, margin_rate, previous_volume,
                listing_date, listed_shares, capital,
                sales, operating_profit, net_income, roe, market_cap,
                sector_large, sector_medium, sector_small
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol, standard_code, name, market,
            base_price, trading_unit, margin_rate, previous_volume,
            listing_date, listed_shares, capital,
            sales, operating_profit, net_income, roe, market_cap,
            sector_large, sector_medium, sector_small
        ))
    
    async def _start_update_log(self, update_type: str, start_time: datetime) -> int:
        """업데이트 로그 시작"""
        async with self.db.get_connection() as db:
            cursor = await db.execute(
                "INSERT INTO stock_info_update_log (update_type, start_time, status) VALUES (?, ?, ?)",
                (update_type, start_time.isoformat(), 'running')
            )
            await db.commit()
            return cursor.lastrowid
    
    async def _complete_update_log(self, log_id: int, total_count: int, success_count: int, 
                                 error_count: int, end_time: datetime, status: str, 
                                 error_message: str = None):
        """업데이트 로그 완료"""
        async with self.db.get_connection() as db:
            await db.execute("""
                UPDATE stock_info_update_log 
                SET total_count = ?, success_count = ?, error_count = ?, 
                    end_time = ?, status = ?, error_message = ?
                WHERE id = ?
            """, (total_count, success_count, error_count, end_time.isoformat(), 
                  status, error_message, log_id))
            await db.commit()
    
    async def get_stock_list(self, market: str = None) -> List[Dict[str, Any]]:
        """주식 목록 조회
        
        Args:
            market: 시장 구분 ('KOSPI', 'KOSDAQ', None=전체)
            
        Returns:
            List[Dict]: 주식 목록
        """
        query = "SELECT * FROM stock_master"
        params = []
        
        if market:
            query += " WHERE market = ?"
            params.append(market)
        
        query += " ORDER BY symbol"
        
        async with self.db.get_connection() as db:
            cursor = await db.execute(query, params)
            rows = await cursor.fetchall()
            
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in rows]
    
    async def get_stock_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """특정 주식 정보 조회
        
        Args:
            symbol: 종목 코드
            
        Returns:
            Dict: 주식 정보 또는 None
        """
        async with self.db.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM stock_master WHERE symbol = ?", (symbol,)
            )
            row = await cursor.fetchone()
            
            if row:
                columns = [description[0] for description in cursor.description]
                stock_info = dict(zip(columns, row))
                
                # 업종 분류 코드를 업종명으로 변환
                stock_info['sector_large_name'] = self._get_sector_name(stock_info.get('sector_large'))
                stock_info['sector_medium_name'] = self._get_sector_name(stock_info.get('sector_medium'))
                stock_info['sector_small_name'] = self._get_sector_name(stock_info.get('sector_small'))
                
                return stock_info
            return None
    
    def _get_sector_name(self, sector_code: str) -> str:
        """업종 분류 코드를 업종명으로 변환
        
        Args:
            sector_code: 업종 분류 코드
            
        Returns:
            업종명
        """
        if not sector_code or sector_code == '0':
            return '기타'
            
        # 업종 분류 코드 매핑 (한국거래소 기준)
        sector_map = {
            # 대분류 코드
            '16': '유통업',
            '17': '전기가스업', 
            '18': '건설업',
            '19': '운수창고업',
            '20': '통신업',
            '21': '금융업',
            '22': '은행업',
            '23': '증권업',
            '24': '보험업',
            '25': '서비스업',
            '26': '제조업',
            '27': '기술업종',
            '28': '음식료품',
            '29': '통신서비스업',  # NAVER 등 IT서비스
            '30': '종이목재',
            '31': '화학',
            '32': '제약',
            '33': '비금속광물',
            '34': '철강금속',
            '35': '기계',
            '36': '전기전자',
            '37': '의료정밀',
            '38': '운송장비',
            # 중분류 코드들
            '5': '음식료품',
            '6': '섬유의복', 
            '7': '종이목재',
            '8': '화학',
            '9': '제약',
            '10': '비금속광물',
            '11': '철강금속',
            '12': '기계',
            '13': '전기전자',
            '14': '의료정밀',
            '15': '운송장비'
        }
        
        return sector_map.get(str(sector_code), f'업종코드{sector_code}')
    
    async def get_sector_list(self) -> List[Dict[str, Any]]:
        """업종 목록 조회"""
        async with self.db.get_connection() as db:
            cursor = await db.execute("SELECT * FROM sector_master ORDER BY sector_code")
            rows = await cursor.fetchall()
            
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in rows]
    
    # 테마 관련 조회 함수들은 제거됨 (더 이상 사용하지 않음)
    
    def add_update_callback(self, callback):
        """업데이트 콜백 함수 추가
        
        Args:
            callback: 업데이트 완료 시 호출될 콜백 함수
        """
        self.update_callbacks.append(callback)
    
    async def _update_nxt_data(self, nxt_type: str) -> dict:
        """NXT 데이터 업데이트
        
        Args:
            nxt_type: NXT 타입 ('kospi_nxt' 또는 'kosdaq_nxt')
            
        Returns:
            dict: 업데이트 결과 {'total': int, 'success': int, 'error': int}
        """
        result = {'total': 0, 'success': 0, 'error': 0}
        
        try:
            # NXT 마스터 파일 다운로드 및 파싱
            if nxt_type == 'kospi_nxt':
                nxt_df = await self._download_and_parse_kospi_nxt()
            elif nxt_type == 'kosdaq_nxt':
                nxt_df = await self._download_and_parse_kosdaq_nxt()
            else:
                raise ValueError(f"지원하지 않는 NXT 타입: {nxt_type}")
            
            if nxt_df is None or nxt_df.empty:
                self.logger.warning(f"{nxt_type} 데이터가 비어있습니다")
                return result
            
            result['total'] = len(nxt_df)
            
            # NXT 종목들의 symbol 리스트 추출
            nxt_symbols = nxt_df['symbol'].tolist()
            
            # 기존 stock_master 테이블에서 해당 종목들의 nxt 컬럼을 1로 업데이트
            async with self.db.get_connection() as db:
                for symbol in nxt_symbols:
                    try:
                        await db.execute(
                            "UPDATE stock_master SET nxt = 1 WHERE symbol = ?",
                            (symbol,)
                        )
                        result['success'] += 1
                        
                    except Exception as e:
                        self.logger.error(f"{nxt_type} 종목 {symbol} 업데이트 오류: {e}")
                        result['error'] += 1
                
                await db.commit()
            
            self.logger.info(f"{nxt_type} 데이터 업데이트 완료: {result['success']}건 성공, {result['error']}건 실패")
            
        except Exception as e:
            self.logger.error(f"{nxt_type} 데이터 업데이트 오류: {e}")
            result['error'] = result['total']
        
        return result
    
    async def _download_and_parse_kospi_nxt(self):
        """KOSPI NXT 마스터 파일 다운로드 및 파싱"""
        try:
            # KOSPI NXT 마스터 파일 다운로드
            zip_path = os.path.join(self.cache_dir, 'nxt_kospi_code.mst.zip')
            
            # SSL 컨텍스트 생성 (인증서 검증 비활성화)
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # 파일 다운로드
            urllib.request.urlretrieve(self.master_urls['kospi_nxt'], zip_path)
            
            # ZIP 파일 압축 해제
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.cache_dir)
            
            # 압축 해제된 파일 경로
            mst_path = os.path.join(self.cache_dir, 'nxt_kospi_code.mst')
            
            # 파일을 바이너리 모드로 읽고 인코딩 시도
            encodings = ['cp949', 'euc-kr', 'utf-8']
            content = None
            
            for encoding in encodings:
                try:
                    with open(mst_path, 'rb') as f:
                        raw_data = f.read()
                    content = raw_data.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                raise ValueError("파일 인코딩을 확인할 수 없습니다")
            
            # 임시 파일로 분할 (첫 번째 부분만 사용)
            lines = content.split('\n')
            tmp_file1 = os.path.join(self.cache_dir, 'nxt_kospi_tmp1.txt')
            
            with open(tmp_file1, 'w', encoding='utf-8') as f:
                for line in lines:
                    if len(line.strip()) > 0:
                        f.write(line + '\n')
            
            # NXT 파일은 고정폭 형식이므로 첫 번째 컬럼(종목코드)만 추출
            # 종목코드는 첫 6자리
            symbols = []
            with open(tmp_file1, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if len(line) >= 6:
                        symbol = line[:6].strip()
                        if symbol and symbol.isdigit():
                            symbols.append(symbol)
            
            # DataFrame 생성
            df1 = pd.DataFrame({'symbol': symbols})
            
            self.logger.info(f"KOSPI NXT 종목 수: {len(df1)}")
            self.logger.info(f"KOSPI NXT 샘플 종목: {df1['symbol'].head().tolist()}")
            
            # 임시 파일 삭제
            try:
                os.remove(tmp_file1)
                os.remove(zip_path)
                os.remove(mst_path)
            except:
                pass
            
            return df1
            
        except Exception as e:
            self.logger.error(f"KOSPI NXT 마스터 파일 파싱 오류: {e}")
            return None
    
    async def _download_and_parse_kosdaq_nxt(self):
        """KOSDAQ NXT 마스터 파일 다운로드 및 파싱"""
        try:
            # KOSDAQ NXT 마스터 파일 다운로드
            zip_path = os.path.join(self.cache_dir, 'nxt_kosdaq_code.mst.zip')
            
            # SSL 컨텍스트 생성 (인증서 검증 비활성화)
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # 파일 다운로드
            urllib.request.urlretrieve(self.master_urls['kosdaq_nxt'], zip_path)
            
            # ZIP 파일 압축 해제
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.cache_dir)
            
            # 압축 해제된 파일 경로
            mst_path = os.path.join(self.cache_dir, 'nxt_kosdaq_code.mst')
            
            # 파일을 바이너리 모드로 읽고 인코딩 시도
            encodings = ['cp949', 'euc-kr', 'utf-8']
            content = None
            
            for encoding in encodings:
                try:
                    with open(mst_path, 'rb') as f:
                        raw_data = f.read()
                    content = raw_data.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                raise ValueError("파일 인코딩을 확인할 수 없습니다")
            
            # 임시 파일로 분할 (첫 번째 부분만 사용)
            lines = content.split('\n')
            tmp_file1 = os.path.join(self.cache_dir, 'nxt_kosdaq_tmp1.txt')
            
            with open(tmp_file1, 'w', encoding='utf-8') as f:
                for line in lines:
                    if len(line.strip()) > 0:
                        f.write(line + '\n')
            
            # NXT 파일은 고정폭 형식이므로 첫 번째 컬럼(종목코드)만 추출
            # 종목코드는 첫 6자리
            symbols = []
            with open(tmp_file1, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if len(line) >= 6:
                        symbol = line[:6].strip()
                        if symbol and symbol.isdigit():
                            symbols.append(symbol)
            
            # DataFrame 생성
            df1 = pd.DataFrame({'symbol': symbols})
            
            self.logger.info(f"KOSDAQ NXT 종목 수: {len(df1)}")
            self.logger.info(f"KOSDAQ NXT 샘플 종목: {df1['symbol'].head().tolist()}")
            
            # 임시 파일 삭제
            try:
                os.remove(tmp_file1)
                os.remove(zip_path)
                os.remove(mst_path)
            except:
                pass
            
            return df1
            
        except Exception as e:
            self.logger.error(f"KOSDAQ NXT 마스터 파일 파싱 오류: {e}")
            return None
    
    async def cleanup(self):
        """리소스 정리"""
        self.logger.info("주식 정보 소스 정리 중...")
        
        # 캐시 디렉토리 정리
        if self.cache_dir.exists():
            for file in self.cache_dir.glob('*'):
                try:
                    file.unlink()
                except Exception as e:
                    self.logger.warning(f"캐시 파일 삭제 실패 {file}: {e}")
        
        self.logger.info("주식 정보 소스 정리 완료")