# -*- coding: utf-8 -*-
"""
호가 데이터 소스

한국투자증권 API를 통한 실시간 호가 데이터 수집 및 처리
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Set
from collections import deque

from ..kis_api import KISApi
from ...utils.logger import get_logger
from ...utils.config_manager import ConfigManager
from ...utils.database_manager import DatabaseManager

class OrderbookSource:
    """
    호가 데이터 수집 소스
    
    실시간 매수/매도 호가 정보 수집 및 분석
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, kis_api: KISApi):
        """호가 데이터 소스 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
            kis_api: KIS API 인스턴스
        """
        self.config = config_manager
        self.db = db_manager
        self.kis_api = kis_api
        self.logger = get_logger()
        
        # 설정 로드
        self._load_config()
        
        # 데이터 구조 초기화
        self._initialize_data_structures()
        
        # 콜백 관리
        self.data_callbacks = []
        
        # 수집 상태
        self.is_collecting = False
        self.collection_tasks = []
        
        # 성능 메트릭
        self.metrics = {
            'orderbook_updates': 0,
            'active_symbols_count': 0,
            'last_update_time': None,
            'errors_count': 0
        }
    
    def _load_config(self):
        """설정 로드"""
        data_config = self.config.get('data_collection', {})
        
        # 호가 데이터 설정
        self.orderbook_cache_size = data_config.get('orderbook_cache_size', 100)
        self.orderbook_retention_hours = data_config.get('orderbook_retention_hours', 24)
        self.collection_interval = data_config.get('orderbook_interval', 1)
        
        # 데이터 보관 설정
        self.data_retention_days = data_config.get('data_retention_days', 7)
    
    def _initialize_data_structures(self):
        """데이터 구조 초기화"""
        # 호가 데이터 캐시
        self.orderbook_cache = {}
        self.orderbook_snapshots = {}
        
        # 활성 종목 관리
        self.orderbook_symbols = set()
    
    async def initialize(self):
        """호가 데이터 소스 초기화"""
        self.logger.info("호가 데이터 소스 초기화 시작")
        
        try:
            # 데이터베이스 테이블 생성
            await self._create_database_tables()
            
            # 데이터 정리 작업 스케줄링
            asyncio.create_task(self._schedule_data_cleanup())
            
            self.logger.info("호가 데이터 소스 초기화 완료")
            
        except Exception as e:
            self.logger.error(f"호가 데이터 소스 초기화 오류: {e}")
            raise
    
    async def _create_database_tables(self):
        """데이터베이스 테이블 생성"""
        # 호가 데이터 테이블
        await self.db.execute("""
            CREATE TABLE IF NOT EXISTS orderbook_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                bid_price_1 REAL, bid_qty_1 INTEGER,
                bid_price_2 REAL, bid_qty_2 INTEGER,
                bid_price_3 REAL, bid_qty_3 INTEGER,
                bid_price_4 REAL, bid_qty_4 INTEGER,
                bid_price_5 REAL, bid_qty_5 INTEGER,
                ask_price_1 REAL, ask_qty_1 INTEGER,
                ask_price_2 REAL, ask_qty_2 INTEGER,
                ask_price_3 REAL, ask_qty_3 INTEGER,
                ask_price_4 REAL, ask_qty_4 INTEGER,
                ask_price_5 REAL, ask_qty_5 INTEGER,
                total_bid_qty INTEGER,
                total_ask_qty INTEGER,
                spread REAL,
                spread_rate REAL
            )
        """)
        
        # 인덱스 생성
        await self.db.execute("""
            CREATE INDEX IF NOT EXISTS idx_orderbook_symbol_timestamp 
            ON orderbook_data (symbol, timestamp)
        """)
        
        # 호가 스프레드 분석 테이블
        await self.db.execute("""
            CREATE TABLE IF NOT EXISTS orderbook_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                avg_spread REAL,
                min_spread REAL,
                max_spread REAL,
                bid_ask_ratio REAL,
                market_depth REAL,
                analysis_period INTEGER
            )
        """)
        
        # 인덱스 생성
        await self.db.execute("""
            CREATE INDEX IF NOT EXISTS idx_orderbook_analysis_symbol_timestamp 
            ON orderbook_analysis (symbol, timestamp)
        """)
    
    def add_data_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """데이터 수신 콜백 추가
        
        Args:
            callback: 데이터 수신 시 호출될 콜백 함수
        """
        self.data_callbacks.append(callback)
    
    async def start_collection(self, symbols: List[str] = None):
        """호가 데이터 수집 시작
        
        Args:
            symbols: 수집할 종목 리스트 (None이면 기본 종목들)
        """
        if self.is_collecting:
            self.logger.warning("호가 데이터 수집이 이미 진행 중입니다")
            return
        
        self.logger.info("호가 데이터 수집 시작")
        self.is_collecting = True
        
        try:
            # 종목 설정
            if symbols:
                for symbol in symbols:
                    await self.add_symbol(symbol)
            
            # 호가 데이터 수집 작업
            self.collection_tasks.append(
                asyncio.create_task(self._collect_orderbook_data())
            )
            
            # 호가 분석 작업
            self.collection_tasks.append(
                asyncio.create_task(self._analyze_orderbook_data())
            )
            
        except Exception as e:
            self.logger.error(f"호가 데이터 수집 시작 오류: {e}")
            self.is_collecting = False
            raise
    
    async def stop_collection(self):
        """호가 데이터 수집 중지"""
        if not self.is_collecting:
            return
        
        self.logger.info("호가 데이터 수집 중지")
        self.is_collecting = False
        
        # 모든 수집 작업 취소
        for task in self.collection_tasks:
            if not task.done():
                task.cancel()
        
        # 작업 완료 대기
        if self.collection_tasks:
            await asyncio.gather(*self.collection_tasks, return_exceptions=True)
        
        self.collection_tasks.clear()
    
    async def add_symbol(self, symbol: str):
        """호가 모니터링 종목 추가
        
        Args:
            symbol: 종목 코드
        """
        if symbol not in self.orderbook_symbols:
            self.orderbook_symbols.add(symbol)
            
            # 호가 데이터 캐시 초기화
            self.orderbook_cache[symbol] = deque(maxlen=self.orderbook_cache_size)
            self.orderbook_snapshots[symbol] = None
            
            self.logger.info(f"호가 모니터링 종목 추가: {symbol}")
    
    async def remove_symbol(self, symbol: str):
        """호가 모니터링 종목 제거
        
        Args:
            symbol: 종목 코드
        """
        if symbol in self.orderbook_symbols:
            self.orderbook_symbols.discard(symbol)
            
            # 캐시 정리
            self.orderbook_cache.pop(symbol, None)
            self.orderbook_snapshots.pop(symbol, None)
            
            self.logger.info(f"호가 모니터링 종목 제거: {symbol}")
    
    async def get_latest_orderbook(self, symbol: str) -> Optional[Dict[str, Any]]:
        """최신 호가 정보 조회
        
        Args:
            symbol: 종목 코드
            
        Returns:
            최신 호가 정보 또는 None
        """
        return self.orderbook_snapshots.get(symbol)
    
    async def get_orderbook_history(self, symbol: str, hours: int = 1) -> List[Dict[str, Any]]:
        """호가 히스토리 조회
        
        Args:
            symbol: 종목 코드
            hours: 조회할 시간 (시간)
            
        Returns:
            호가 히스토리 리스트
        """
        since_time = datetime.now() - timedelta(hours=hours)
        
        query = """
            SELECT symbol, timestamp, 
                   bid_price_1, bid_qty_1, bid_price_2, bid_qty_2, bid_price_3, bid_qty_3,
                   bid_price_4, bid_qty_4, bid_price_5, bid_qty_5,
                   ask_price_1, ask_qty_1, ask_price_2, ask_qty_2, ask_price_3, ask_qty_3,
                   ask_price_4, ask_qty_4, ask_price_5, ask_qty_5,
                   total_bid_qty, total_ask_qty, spread, spread_rate
            FROM orderbook_data
            WHERE symbol = ? AND timestamp >= ?
            ORDER BY timestamp DESC
        """
        
        async with self.db.get_connection() as db:
            cursor = await db.execute(query, (symbol, since_time))
            rows = await cursor.fetchall()
        
        return [
            {
                'symbol': row[0],
                'timestamp': row[1],
                'bids': [
                    {'price': row[2], 'quantity': row[3]},
                    {'price': row[4], 'quantity': row[5]},
                    {'price': row[6], 'quantity': row[7]},
                    {'price': row[8], 'quantity': row[9]},
                    {'price': row[10], 'quantity': row[11]}
                ],
                'asks': [
                    {'price': row[12], 'quantity': row[13]},
                    {'price': row[14], 'quantity': row[15]},
                    {'price': row[16], 'quantity': row[17]},
                    {'price': row[18], 'quantity': row[19]},
                    {'price': row[20], 'quantity': row[21]}
                ],
                'total_bid_qty': row[22],
                'total_ask_qty': row[23],
                'spread': row[24],
                'spread_rate': row[25]
            }
            for row in rows
        ]
    
    async def _collect_orderbook_data(self):
        """호가 데이터 수집"""
        while self.is_collecting:
            try:
                if not self.orderbook_symbols:
                    await asyncio.sleep(self.collection_interval)
                    continue
                
                # 각 종목의 호가 데이터 수집
                for symbol in list(self.orderbook_symbols):
                    try:
                        # KIS API를 통한 호가 데이터 조회
                        orderbook_data = await self.kis_api.get_orderbook(symbol)
                        
                        if orderbook_data:
                            await self._process_orderbook_data(symbol, orderbook_data)
                            
                    except Exception as e:
                        self.logger.error(f"종목 {symbol} 호가 데이터 수집 오류: {e}")
                        self.metrics['errors_count'] += 1
                
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"호가 데이터 수집 오류: {e}")
                await asyncio.sleep(5)
    
    async def _process_orderbook_data(self, symbol: str, data: Dict[str, Any]):
        """호가 데이터 처리
        
        Args:
            symbol: 종목 코드
            data: 호가 데이터
        """
        try:
            # 호가 데이터 파싱
            bids = []
            asks = []
            
            # 매수 호가 (5단계)
            for i in range(1, 6):
                bid_price = float(data.get(f'bidp{i}', 0))
                bid_qty = int(data.get(f'bidp_rsqn{i}', 0))
                bids.append({'price': bid_price, 'quantity': bid_qty})
            
            # 매도 호가 (5단계)
            for i in range(1, 6):
                ask_price = float(data.get(f'askp{i}', 0))
                ask_qty = int(data.get(f'askp_rsqn{i}', 0))
                asks.append({'price': ask_price, 'quantity': ask_qty})
            
            # 총 매수/매도 잔량
            total_bid_qty = sum(bid['quantity'] for bid in bids)
            total_ask_qty = sum(ask['quantity'] for ask in asks)
            
            # 스프레드 계산
            best_bid = bids[0]['price'] if bids[0]['price'] > 0 else 0
            best_ask = asks[0]['price'] if asks[0]['price'] > 0 else 0
            
            spread = best_ask - best_bid if best_bid > 0 and best_ask > 0 else 0
            spread_rate = (spread / best_ask * 100) if best_ask > 0 and spread > 0 else 0
            
            # 호가 데이터 구성
            orderbook_data = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'bids': bids,
                'asks': asks,
                'total_bid_qty': total_bid_qty,
                'total_ask_qty': total_ask_qty,
                'spread': spread,
                'spread_rate': spread_rate,
                'best_bid': best_bid,
                'best_ask': best_ask
            }
            
            # 캐시 업데이트
            if symbol in self.orderbook_cache:
                self.orderbook_cache[symbol].append(orderbook_data)
            
            self.orderbook_snapshots[symbol] = orderbook_data
            
            # 데이터베이스 저장
            await self.db.execute(
                """
                INSERT INTO orderbook_data 
                (symbol, timestamp, 
                 bid_price_1, bid_qty_1, bid_price_2, bid_qty_2, bid_price_3, bid_qty_3,
                 bid_price_4, bid_qty_4, bid_price_5, bid_qty_5,
                 ask_price_1, ask_qty_1, ask_price_2, ask_qty_2, ask_price_3, ask_qty_3,
                 ask_price_4, ask_qty_4, ask_price_5, ask_qty_5,
                 total_bid_qty, total_ask_qty, spread, spread_rate)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    symbol, datetime.now(),
                    bids[0]['price'], bids[0]['quantity'],
                    bids[1]['price'], bids[1]['quantity'],
                    bids[2]['price'], bids[2]['quantity'],
                    bids[3]['price'], bids[3]['quantity'],
                    bids[4]['price'], bids[4]['quantity'],
                    asks[0]['price'], asks[0]['quantity'],
                    asks[1]['price'], asks[1]['quantity'],
                    asks[2]['price'], asks[2]['quantity'],
                    asks[3]['price'], asks[3]['quantity'],
                    asks[4]['price'], asks[4]['quantity'],
                    total_bid_qty, total_ask_qty, spread, spread_rate
                )
            )
            
            # 콜백 호출
            for callback in self.data_callbacks:
                try:
                    await callback('orderbook_data', orderbook_data)
                except Exception as e:
                    self.logger.error(f"호가 데이터 콜백 오류: {e}")
            
            # 메트릭 업데이트
            self.metrics['orderbook_updates'] += 1
            self.metrics['last_update_time'] = datetime.now()
            self.metrics['active_symbols_count'] = len(self.orderbook_symbols)
            
        except Exception as e:
            self.logger.error(f"호가 데이터 처리 오류 ({symbol}): {e}")
    
    async def _analyze_orderbook_data(self):
        """호가 데이터 분석"""
        while self.is_collecting:
            try:
                for symbol in list(self.orderbook_symbols):
                    if symbol in self.orderbook_cache and len(self.orderbook_cache[symbol]) > 0:
                        await self._calculate_orderbook_metrics(symbol)
                
                await asyncio.sleep(60)  # 1분마다 분석
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"호가 데이터 분석 오류: {e}")
                await asyncio.sleep(60)
    
    async def _calculate_orderbook_metrics(self, symbol: str):
        """호가 메트릭 계산
        
        Args:
            symbol: 종목 코드
        """
        try:
            recent_data = list(self.orderbook_cache[symbol])
            
            if len(recent_data) < 10:  # 최소 10개 데이터 필요
                return
            
            # 스프레드 분석
            spreads = [data['spread'] for data in recent_data if data['spread'] > 0]
            
            if not spreads:
                return
            
            avg_spread = sum(spreads) / len(spreads)
            min_spread = min(spreads)
            max_spread = max(spreads)
            
            # 매수/매도 비율
            bid_quantities = [data['total_bid_qty'] for data in recent_data]
            ask_quantities = [data['total_ask_qty'] for data in recent_data]
            
            avg_bid_qty = sum(bid_quantities) / len(bid_quantities)
            avg_ask_qty = sum(ask_quantities) / len(ask_quantities)
            
            bid_ask_ratio = avg_bid_qty / avg_ask_qty if avg_ask_qty > 0 else 0
            
            # 시장 깊이 (총 호가 잔량)
            market_depth = avg_bid_qty + avg_ask_qty
            
            # 분석 결과 저장
            await self.db.execute(
                """
                INSERT INTO orderbook_analysis 
                (symbol, timestamp, avg_spread, min_spread, max_spread, 
                 bid_ask_ratio, market_depth, analysis_period)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    symbol, datetime.now(), avg_spread, min_spread, max_spread,
                    bid_ask_ratio, market_depth, len(recent_data)
                )
            )
            
        except Exception as e:
            self.logger.error(f"호가 메트릭 계산 오류 ({symbol}): {e}")
    
    async def _schedule_data_cleanup(self):
        """데이터 정리 작업 스케줄링"""
        while True:
            try:
                await asyncio.sleep(3600)  # 1시간마다 실행
                await self._cleanup_old_data()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"데이터 정리 작업 오류: {e}")
    
    async def _cleanup_old_data(self):
        """오래된 데이터 정리"""
        try:
            cutoff_time = datetime.now() - timedelta(days=self.data_retention_days)
            
            # 오래된 호가 데이터 삭제
            await self.db.execute(
                "DELETE FROM orderbook_data WHERE timestamp < ?",
                (cutoff_time,)
            )
            
            # 오래된 분석 데이터 삭제
            await self.db.execute(
                "DELETE FROM orderbook_analysis WHERE timestamp < ?",
                (cutoff_time,)
            )
            
            self.logger.info(f"오래된 호가 데이터 정리 완료 (기준: {cutoff_time})")
            
        except Exception as e:
            self.logger.error(f"호가 데이터 정리 오류: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """성능 메트릭 조회
        
        Returns:
            성능 메트릭 딕셔너리
        """
        return self.metrics.copy()
    
    async def close(self):
        """리소스 정리"""
        self.logger.info("호가 데이터 소스 종료")
        
        # 데이터 수집 중지
        await self.stop_collection()
        
        # 캐시 정리
        self.orderbook_symbols.clear()
        self.orderbook_cache.clear()
        self.orderbook_snapshots.clear()