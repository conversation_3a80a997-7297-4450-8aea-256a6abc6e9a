# -*- coding: utf-8 -*-
"""
뉴스 수집기

실시간 뉴스 데이터 수집 및 감정 분석
"""

import os
import importlib
from inspect import isclass
import asyncio
import aiohttp
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Callable
import json
import threading

# 한국 표준시 정의
KST = timezone(timedelta(hours=9))


# AI 모델 관련 import
try:
    from ..ai_local.stock_check.llm_classifier import LLMClassifier
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("Warning: AI 모델 라이브러리가 설치되지 않았습니다. 키워드 기반 검증만 사용됩니다.")

from ..utils.logger import get_logger
from ..utils.config_manager import ConfigManager
from ..utils.database_manager import DatabaseManager
from .news_sources import BaseNewsSource, NaverNewsSource, HankyungNewsSource, MaeilNewsSource

class NewsCollector:
    """
    뉴스 수집기 클래스
    """
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager):
        """뉴스 수집기 초기화
        
        Args:
            config_manager: 설정 관리자
            db_manager: 데이터베이스 관리자
        """
        self.config = config_manager
        self.db = db_manager
        # 전역 로거 사용
        self.logger = get_logger()
        
        # 뉴스 소스 객체 동적 생성
        self.news_sources = self._discover_and_create_news_sources()
        
        # 수집 설정
        data_config = self.config.get('data_collection', {})
        self.collection_interval = data_config.get('news_collection_interval', 60)  # 1분
        self.data_retention_days = data_config.get('data_retention_days', 30)
        
        # 시간 필터링 설정 로그
        time_filter_config = data_config.get('news_time_filter', {})
        time_filter_enabled = time_filter_config.get('enabled', True)
        
        
        # 종목 정보 캐시 (종목코드 -> [종목명, 약칭들])
        self._cached_stock_names = {}
        self._stock_cache_updated = None
        
        # 개선된 메모리 캐시 시스템
        self._news_memory_cache = {}    # {url: {'title': str, 'content': str, 'timestamp': datetime}}
        self._content_similarity_cache = []  # [{'title': str, 'content': str, 'url': str, 'timestamp': datetime}]
        self._cache_max_size = 1000     # 메모리 캐시 최대 크기
        self._cache_expire_hours = 24   # 캐시 만료 시간 (시간)
        
        # 수집 상태 관리
        self.is_collecting = False
        self.collection_tasks = []
        self.processed_urls = set()     # 처리된 URL 캐시 (세션용)
        self.news_callbacks = []        # 뉴스 콜백 함수들
        
        # AI 모델 관련 속성 (비동기 초기화 필요)
        self._ai_classifier = None
        self._ai_model_lock = threading.Lock()  # AI 모델 초기화 동시성 제어
        self._ai_model_loaded = False           # AI 모델 로드 상태 추적
        
        # 네트워크 관련 속성 (비동기 초기화 필요)
        self.session = None
        
        # 동시성 제어
        self.db_semaphore = asyncio.Semaphore(10)  # DB 동시 접근 제한
        self.article_processing_semaphore = asyncio.Semaphore(2)  # 기사 처리 동시성 제한
    
    def _discover_and_create_news_sources(self) -> List[BaseNewsSource]:
        """
        뉴스 소스 파일들을 동적으로 발견하고 객체 생성
        
        Returns:
            뉴스 소스 객체 리스트
        """
        import os
        import importlib
        from inspect import isclass
        
        sources = []
        news_sources_dir = os.path.join(os.path.dirname(__file__), 'news_sources')
        
        try:            
             # 특정 뉴스만 로드하도록 설정
            target_files = ['naver_news_source.py', 'hankyung_news_source.py', 'maeil_news_source.py']
            
            # news_sources 디렉토리의 특정 .py 파일만 스캔
            for filename in os.listdir(news_sources_dir):
                if filename.endswith('.py') and not filename.startswith('__') and filename != 'base_news_source.py' and filename in target_files:

                    module_name = filename[:-3]  # .py 제거
                    
                    try:
                        # 모듈 동적 임포트
                        module = importlib.import_module(f'.news_sources.{module_name}', package=__package__)
                        
                        # 모듈에서 BaseNewsSource를 상속한 클래스 찾기
                        for attr_name in dir(module):
                            attr = getattr(module, attr_name)
                            if (isclass(attr) and 
                                issubclass(attr, BaseNewsSource) and 
                                attr != BaseNewsSource):
                                
                                # 클래스 인스턴스 생성
                                source_instance = attr(self.config)
                                sources.append(source_instance)
                                self.logger.info(f"뉴스 소스 발견 및 생성: {attr_name}")
                                break
                    
                    except Exception as e:
                        self.logger.warning(f"뉴스 소스 모듈 로드 실패 ({module_name}): {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"뉴스 소스 디렉토리 스캔 실패: {e}")
            # 폴백: 기본 소스들 수동 생성
            try:
                sources = [
                    NaverNewsSource(self.config),
                    HankyungNewsSource(self.config),
                    MaeilNewsSource(self.config)
                ]
                self.logger.info("폴백: 기본 뉴스 소스들 생성 완료")
            except Exception as fallback_error:
                self.logger.error(f"폴백 뉴스 소스 생성 실패: {fallback_error}")
        
        return sources
    
    async def initialize(self):
        """뉴스 수집기 비동기 초기화
        
        비동기 작업이 필요한 초기화를 수행합니다:
        - 종목 정보 캐시 로드 (DB 접근)
        - AI 모델 초기화 (네트워크 통신)
        - HTTP 세션 생성
        - 뉴스 소스 설정
        """
        self.logger.info("뉴스 수집기 비동기 초기화 시작")
        
        # 종목 정보 캐시 로드 (DB 접근 필요)
        await self._load_stock_names_cache()
        
        # AI 모델 초기화 (네트워크 통신 필요)
        await self._initialize_ai_components()
        
        # HTTP 세션 생성 (네트워크 통신 필요)
        await self._create_http_session()
        
        # 뉴스 소스에 세션 설정
        self._configure_news_sources()
        
        self.logger.info("뉴스 수집기 비동기 초기화 완료")
    
    async def _initialize_ai_components(self):
        """AI 모델 관련 컴포넌트 초기화"""
        self.logger.info(f"AI_AVAILABLE 상태: {AI_AVAILABLE}")
        if AI_AVAILABLE:
            try:
                self.logger.info("AI 모델 초기화 시작...")
                await self._initialize_ai_model()
                self.logger.info("AI 모델 초기화 완료")
            except Exception as e:
                self.logger.error(f"AI 모델 초기화 실패: {e}")
                import traceback
                self.logger.error(f"AI 모델 초기화 오류 상세: {traceback.format_exc()}")
        else:
            self.logger.warning("AI 모델을 사용할 수 없습니다 (AI_AVAILABLE=False)")
    
    async def _create_http_session(self):
        """HTTP 세션 생성 및 설정"""
        # TCP 커넥터 설정 (성능 최적화)
        connector = aiohttp.TCPConnector(
            limit=300,              # 전체 커넥션 풀 크기
            limit_per_host=100,     # 호스트당 커넥션 수
            ttl_dns_cache=3600,     # DNS 캐시 TTL (1시간)
            use_dns_cache=True,
            keepalive_timeout=60,   # Keep-alive 타임아웃
            enable_cleanup_closed=True,
            force_close=False,      # 연결 재사용 활성화
            ssl=False               # SSL 검증 비활성화 (속도 향상)
        )
        
        # 모바일 User-Agent 설정
        mobile_user_agent = (
            "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) "
            "AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 "
            "Mobile/15E148 Safari/604.1"
        )
        
        # HTTP 세션 생성
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(
                total=8,    # 전체 타임아웃
                connect=3,  # 연결 타임아웃
                sock_read=5 # 읽기 타임아웃
            ),
            headers={
                'User-Agent': mobile_user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'ko-KR,ko;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        )
        self.logger.info("HTTP 세션 생성 완료")
    
    def _configure_news_sources(self):
        """뉴스 소스에 세션과 캐시 설정"""
        for source in self.news_sources:
            source.set_session(self.session)
            source.set_processed_urls(self.processed_urls)
        self.logger.info(f"{len(self.news_sources)}개 뉴스 소스 설정 완료")
    

    
    async def _load_stock_names_cache(self):
        """종목 정보 캐시 로드 (DB에서 실제 종목명 가져오기)
        
        주기적으로 DB에서 종목 정보를 가져와서 캐시에 저장
        """
        try:
            from datetime import datetime, timedelta
            
            # 캐시가 1시간 이내에 업데이트되었으면 스킵
            if (self._stock_cache_updated and 
                datetime.now() - self._stock_cache_updated < timedelta(hours=1)):
                return
            
            # DB에서 종목 정보 조회
            query = """
                SELECT symbol, name 
                FROM stock_master 
                WHERE market IN ('KOSPI', 'KOSDAQ') 
                AND symbol IS NOT NULL 
                AND name IS NOT NULL
                ORDER BY symbol
            """
            
            async with self.db.get_connection() as db:
                cursor = await db.execute(query)
                rows = await cursor.fetchall()
                
                # 캐시 구축
                self._cached_stock_names = {}
                for row in rows:
                    symbol, name = row
                    if symbol and name:
                        # 종목명과 약칭 생성
                        names = [name.strip()]
                        
                        # 회사명에서 "주식회사", "(주)", "㈜" 제거한 약칭 추가
                        short_name = name.replace('주식회사', '').replace('(주)', '').replace('㈜', '').strip()
                        if short_name and short_name != name:
                            names.append(short_name)
                        
                        # 영문명이 포함된 경우 분리
                        if '(' in name and ')' in name:
                            korean_part = name.split('(')[0].strip()
                            english_part = name.split('(')[1].split(')')[0].strip()
                            if korean_part:
                                names.append(korean_part)
                            if english_part:
                                names.append(english_part)
                        
                        self._cached_stock_names[symbol] = list(set(names))  # 중복 제거
                
                self._stock_cache_updated = datetime.now()
                self.logger.info(f"종목 정보 캐시 로드 완료: {len(self._cached_stock_names)}개 종목")
                
        except Exception as e:
            self.logger.error(f"종목 정보 캐시 로드 실패: {e}")
            # 폴백: 기본 매핑 사용
            self._cached_stock_names = {
                '005930': ['삼성전자', '삼성'],
                '000660': ['SK하이닉스', 'SK하이닉스'],
                '035420': ['NAVER', '네이버'],
                '005380': ['현대차', '현대자동차'],
                '035720': ['카카오'],
                '068270': ['셀트리온'],
                '207940': ['삼성바이오로직스', '바이오로직스'],
                '373220': ['LG에너지솔루션', 'LG에너지']
            }
    
    # 로컬 AI 모델 관련 코드 제거됨
    

    
    def add_news_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """뉴스 수신 콜백 추가
        
        Args:
            callback: 뉴스 수신시 호출될 콜백 함수
        """
        self.news_callbacks.append(callback)
    
    async def start_collection(self):
        """뉴스 수집 시작"""
        if self.is_collecting:
            self.logger.warning("이미 뉴스 수집이 진행 중입니다")
            return
        
        self.is_collecting = True
        self.logger.info("뉴스 수집 시작")
        
        # 메모리 캐시 초기화
        await self._cleanup_memory_cache()
        self.logger.info(f"메모리 캐시 초기화 완료: URL 캐시 {len(self._news_memory_cache)}개, 내용 캐시 {len(self._content_similarity_cache)}개")
        
        # 뉴스 수집 작업
        collection_task = asyncio.create_task(self._collect_news_loop())
        self.collection_tasks.append(collection_task)
        
        # 뉴스 정리 작업
        cleanup_task = asyncio.create_task(self._cleanup_old_news())
        self.collection_tasks.append(cleanup_task)
    
    async def stop_collection(self):
        """뉴스 수집 중지"""
        self.is_collecting = False
        
        # 모든 수집 작업 취소
        for task in self.collection_tasks:
            if not task.done():
                task.cancel()
        
        # 작업 완료 대기
        if self.collection_tasks:
            await asyncio.gather(*self.collection_tasks, return_exceptions=True)
        
        self.collection_tasks.clear()
        self.logger.info("뉴스 수집 중지")
    
    async def _collect_news_loop(self):
        """뉴스 수집 루프"""
        while self.is_collecting:
            try:
                start_time = datetime.now()
                
                # 1단계: 모든 뉴스 소스에서 뉴스 리스트만 동시에 수집
                tasks = []
                for source in self.news_sources:
                    task = asyncio.create_task(self._collect_news_list_from_source(source))
                    tasks.append(task)
                
                # 모든 소스에서 뉴스 리스트 수집 완료 대기
                if tasks:
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # 2단계: 수집된 모든 뉴스를 통합하여 처리
                    all_collected_articles = []
                    successful_sources = 0
                    failed_sources = 0
                    
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            failed_sources += 1
                            self.logger.error(f"뉴스 소스 [{self.news_sources[i].name}] 수집 실패: {result}")
                        else:
                            successful_sources += 1
                            source_name, articles = result
                            all_collected_articles.extend([(article, source_name) for article in articles])
                    
                    # 3단계: 통합된 뉴스들에 대해 중복 처리, 유사성 처리, 상세 수집, DB 저장
                    if all_collected_articles:
                        total_collected = len(all_collected_articles)
                        processed_count = await self._process_unified_articles(all_collected_articles)
                        
                        # 수집 결과 로깅
                        end_time = datetime.now()
                        duration = (end_time - start_time).total_seconds()
                        
                        self.logger.info(
                            f"뉴스 수집 완료 - 소스: {successful_sources}개 성공, {failed_sources}개 실패, "
                            f"전체 수집: {total_collected}개, 처리 완료: {processed_count}개, "
                            f"소요시간: {duration:.2f}초, 메모리 캐시: URL {len(self._news_memory_cache)}개, 내용 {len(self._content_similarity_cache)}개"
                        )
                    else:
                        # 수집된 뉴스가 없는 경우에도 로깅
                        end_time = datetime.now()
                        duration = (end_time - start_time).total_seconds()
                        
                        self.logger.info(
                            f"뉴스 수집 완료 - 소스: {successful_sources}개 성공, {failed_sources}개 실패, "
                            f"전체 수집: 0개, 소요시간: {duration:.2f}초, 메모리 캐시: URL {len(self._news_memory_cache)}개, 내용 {len(self._content_similarity_cache)}개"
                        )
                
                # 수집 주기 대기
                await asyncio.sleep(self.collection_interval)
            
            except Exception as e:
                self.logger.error(f"뉴스 수집 루프 오류: {e}")
                await asyncio.sleep(60)  # 오류 발생시 1분 대기
    
    async def _collect_news_list_from_source(self, source):
        """개별 뉴스 소스에서 뉴스 리스트만 수집
        
        Args:
            source: 뉴스 소스 객체
            
        Returns:
            tuple: (source_name, articles_list)
        """
        try:
            # 뉴스 소스 실행 시작 로그
            self.logger.info(f"[{source.name}] 뉴스 수집 실행 시작")
            
            # 뉴스 리스트만 수집 (상세 내용 수집 없음)
            articles = await source.collect_news()
            
            # 수집 완료 로그
            self.logger.info(f"{source.source_type} 새로운 뉴스 수집 완료 ({source.name}): {len(articles)}개")
            
            return source.name, articles
            
        except Exception as e:
             self.logger.error(f"[{source.name}] 뉴스 리스트 수집 오류: {e}")
             return source.name, []
    
    async def _process_unified_articles(self, all_collected_articles):
        """통합된 뉴스들에 대해 중복 처리, 유사성 처리, 상세 수집, DB 저장
        
        Args:
            all_collected_articles: [(article, source_name), ...] 형태의 리스트
            
        Returns:
            int: 처리 완료된 기사 수
        """
        try:
            if not all_collected_articles:
                return 0
            
            # 날짜 필터링: 설정된 시간보다 오래된 기사 제거
            time_filter_config = self.config.get('data_collection', {}).get('news_time_filter', {})
            time_filter_enabled = time_filter_config.get('enabled', True)
            
            current_time = datetime.now()
            time_threshold = current_time - timedelta(minutes=3)

            self.logger.info(f"기사 기준 시간 기사 스킵: {time_threshold})")
            
            filtered_by_time = []
            old_articles_count = 0
            
            # 날짜 필터링이 비활성화된 경우 모든 기사 통과
            if not time_filter_enabled:
                filtered_by_time = all_collected_articles
            else:
                for article, source_name in all_collected_articles:
                    published_date = article.get('published')
                    
                    # 설정된 시간보다 오래된 기사는 제외
                    if published_date < time_threshold:
                        old_articles_count += 1
                        self.logger.debug(f"오래된 기사 스킵: {article.get('title', '')[:50]}... (발행: {published_date})")
                        continue
                    
                    filtered_by_time.append((article, source_name))
            
            # 필터링된 기사가 없으면 종료
            if not filtered_by_time:
                return 0
            
            # 메모리 캐시 정리 (만료된 항목 제거)
            await self._cleanup_memory_cache()
            
            # 1단계: 메모리 캐시 기반 중복 확인 (URL + 내용 유사도)
            memory_filtered = []
            url_cached_count = 0
            content_cached_count = 0
            
            for article, source_name in filtered_by_time:
                article_url = article['url']
                article_title = article.get('title', '')
                
                # URL 기반 중복 확인
                if article_url in self.processed_urls or article_url in self._news_memory_cache:
                    url_cached_count += 1
                    continue
                
                # 메모리 캐시 내용 유사도 확인
                is_similar_in_cache = await self._check_similarity_in_memory_cache(article_title, article.get('content', ''))
                if is_similar_in_cache:
                    content_cached_count += 1
                    continue
                
                # 중복이 아닌 경우 처리 대상에 추가
                memory_filtered.append((article, source_name, article_url))
            
            if url_cached_count > 0:
                self.logger.debug(f"메모리 URL 캐시 중복 스킵: {url_cached_count}개")
            if content_cached_count > 0:
                self.logger.debug(f"메모리 내용 유사도 중복 스킵: {content_cached_count}개")
            
            # 2단계: DB 중복 확인
            valid_articles = []
            db_duplicate_count = 0
            
            if memory_filtered:
                db_check_urls = [article_url for _, _, article_url in memory_filtered]
                async with self.db_semaphore:
                    duplicate_flags = await self.db.check_news_duplicates_batch(db_check_urls)
                
                # DB 중복이 아닌 기사들만 선별하고 즉시 메모리 캐시에 URL 추가 (동시성 문제 방지)
                for (article, source_name, article_url), is_duplicate in zip(memory_filtered, duplicate_flags):
                    if not is_duplicate:
                        valid_articles.append((article, source_name, article_url))
                        # 처리 예정 URL을 즉시 캐시에 추가하여 동시성 문제 방지
                        self.processed_urls.add(article_url)
                        # 임시로 메모리 캐시에도 추가 (내용은 나중에 업데이트)
                        self._news_memory_cache[article_url] = {
                            'title': article.get('title', ''),
                            'content': article.get('content', ''),
                            'timestamp': datetime.now()
                        }
                    else:
                        db_duplicate_count += 1
            
            if db_duplicate_count > 0:
                self.logger.debug(f"DB 중복 스킵: {db_duplicate_count}개")
            
            if not valid_articles:
                return 0
            
            # 3단계: 상세 내용 수집 (소스별로 그룹화하여 효율적으로 처리)
            source_groups = {}
            for article, source_name, article_url in valid_articles:
                if source_name not in source_groups:
                    source_groups[source_name] = []
                source_groups[source_name].append((article, article_url))
            
            detailed_articles_with_info = []
            
            for source_name, articles_group in source_groups.items():
                # 해당 소스 객체 찾기
                source_obj = None
                for source in self.news_sources:
                    if source.name == source_name:
                        source_obj = source
                        break
                
                if source_obj is None:
                    self.logger.warning(f"소스 객체를 찾을 수 없음: {source_name}")
                    continue
                
                # 상세 내용 수집
                articles_only = [article for article, _ in articles_group]
                
                try:
                    detailed_articles = await source_obj.fetch_detailed_content_for_new_articles(articles_only)
                    
                    # 상세 내용이 수집된 기사들을 URL과 함께 저장
                    for i, detailed_article in enumerate(detailed_articles):
                        if i < len(articles_group):
                            _, article_url = articles_group[i]
                            detailed_articles_with_info.append((detailed_article, source_name, article_url))
                
                except Exception as e:
                    self.logger.error(f"[{source_name}] 상세 내용 수집 오류: {e}")
                    continue
            
            if not detailed_articles_with_info:
                return 0
            
            # 4단계: 소스 간 유사도 필터링
            filtered_articles = await self._filter_cross_source_similarity(detailed_articles_with_info)
            
            # 5단계: DB 저장
            processed_count = 0
            save_tasks = []
            
            for detailed_article, source_name, article_url in filtered_articles:
                task = asyncio.create_task(self._process_and_save_article(detailed_article, article_url))
                save_tasks.append(task)
            
            if save_tasks:
                results = await asyncio.gather(*save_tasks, return_exceptions=True)
                processed_count = sum(1 for result in results if not isinstance(result, Exception))
            
            return processed_count
            
        except Exception as e:
             self.logger.error(f"통합 기사 처리 오류: {e}")
             return 0
    
    async def _filter_cross_source_similarity(self, detailed_articles_with_info):
        """소스 간 유사도 필터링
        
        Args:
            detailed_articles_with_info: [(detailed_article, source_name, article_url), ...]
            
        Returns:
            list: 필터링된 기사 리스트
        """
        try:
            if not detailed_articles_with_info:
                return []
            
            # 유사도 설정 가져오기
            similarity_config = self.config.get('data_collection', {}).get('news_similarity', {})
            similarity_threshold = similarity_config.get('threshold', 0.8)
            
            self.logger.info(f"소스 간 유사도 필터링 시작: {len(detailed_articles_with_info)}개")
            
            filtered_articles = []
            processed_articles = []
            
            for current_article, source_name, article_url in detailed_articles_with_info:
                current_title = current_article.get('title', '')
                current_content = current_article.get('content', '')
                
                # 이미 처리된 기사들과 유사도 비교
                is_similar_to_processed = False
                max_similarity = 0.0
                similar_article_info = None
                
                for processed_article, processed_source, _ in processed_articles:
                    # 공통 유사도 체크 함수 사용
                    is_similar, similarity = await self._check_article_similarity(
                        current_title, current_content,
                        processed_article['title'], processed_article['content'],
                        similarity_threshold
                    )
                    
                    if similarity > max_similarity:
                        max_similarity = similarity
                        similar_article_info = (processed_article, processed_source)
                    
                    if is_similar:
                        is_similar_to_processed = True
                        self.logger.info(
                            f"소스 간 유사도 중복 기사 스킵: [{source_name}] {current_title[:50]}... "
                            f"(유사도: {similarity:.3f}, 유사 기사: [{processed_source}] {processed_article['title'][:30]}...)"
                        )
                        break
                
                if not is_similar_to_processed:
                    # 중복되지 않은 기사만 결과에 추가
                    filtered_articles.append((current_article, source_name, article_url))
                    processed_articles.append((current_article, source_name, article_url))
                    
                    self.logger.debug(
                        f"소스 간 유사도 체크 통과: [{source_name}] {current_title[:50]}... "
                        f"(최대 유사도: {max_similarity:.3f})"
                    )
            
            self.logger.info(f"소스 간 유사도 필터링 완료: {len(detailed_articles_with_info)}개 → {len(filtered_articles)}개")
            return filtered_articles
            
        except Exception as e:
            self.logger.error(f"소스 간 유사도 필터링 오류: {e}")
            # 오류 발생 시 원본 리스트 반환 (안전한 방향)
            return detailed_articles_with_info
    
    async def _filter_similar_articles_within_batch(self, detailed_articles, valid_articles):
        """배치 내 기사들 간의 유사도 체크 및 필터링
        
        Args:
            detailed_articles: 상세 내용이 수집된 기사 리스트
            valid_articles: 유효한 기사 정보 리스트 (article, url 튜플)
        
        Returns:
            List[Tuple]: 필터링된 (detailed_article, article_url) 튜플 리스트
        """
        try:
            # 유사도 검사 설정 확인
            similarity_config = self.config.get('data_collection', {}).get('news_similarity', {})
            similarity_enabled = similarity_config.get('enabled', True)
            
            if not similarity_enabled or len(detailed_articles) <= 1:
                # 유사도 검사 비활성화이거나 기사가 1개 이하면 모든 기사 반환
                return [(detailed_articles[i], valid_articles[i][1]) 
                       for i in range(min(len(detailed_articles), len(valid_articles)))]
            
            similarity_threshold = similarity_config.get('threshold', 0.8)
            filtered_results = []
            processed_articles = []  # 이미 처리된 기사들의 정보 저장
            
            for i in range(len(detailed_articles)):
                if i >= len(valid_articles):
                    break
                    
                current_article = detailed_articles[i]
                current_url = valid_articles[i][1]
                
                # 현재 기사 정보 추출
                current_title = current_article.get('title', '')
                current_content = current_article.get('content', '')
                
                # 이미 처리된 기사들과 유사도 비교
                is_similar_to_processed = False
                max_similarity = 0.0
                
                for processed_article in processed_articles:
                    # 공통 유사도 체크 함수 사용
                    is_similar, similarity = await self._check_article_similarity(
                        current_title, current_content,
                        processed_article['title'], processed_article['content'],
                        similarity_threshold
                    )
                    
                    if similarity > max_similarity:
                        max_similarity = similarity
                    
                    if is_similar:
                        is_similar_to_processed = True
                        self.logger.info(
                            f"배치 내 유사도 중복 기사 스킵: {current_title[:50]}... "
                            f"(유사도: {similarity:.3f}, 유사 기사: {processed_article['title'][:30]}...)"
                        )
                        break
                
                if not is_similar_to_processed:
                    # 중복되지 않은 기사만 결과에 추가
                    filtered_results.append((current_article, current_url))
                    processed_articles.append({
                        'title': current_title,
                        'content': current_content
                    })
                    self.logger.debug(
                        f"배치 내 유사도 체크 통과: {current_title[:50]}... "
                        f"(최대 유사도: {max_similarity:.3f})"
                    )
            
            self.logger.info(
                f"배치 내 유사도 필터링 완료: {len(detailed_articles)}개 → {len(filtered_results)}개"
            )
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"배치 내 유사도 체크 오류: {e}")
            # 오류 발생 시 모든 기사 반환 (안전장치)
            return [(detailed_articles[i], valid_articles[i][1]) 
                   for i in range(min(len(detailed_articles), len(valid_articles)))]
    
    async def _calculate_text_similarity(self, text1, text2):
        """두 텍스트 간의 유사도 계산 (TF-IDF + 코사인 유사도)
        
        Args:
            text1: 첫 번째 텍스트
            text2: 두 번째 텍스트
        
        Returns:
            float: 유사도 (0.0 ~ 1.0)
        """
        try:
            # database_manager의 유사도 계산 로직 재사용
            return await self.db.calculate_similarity_between_texts(text1, text2)
        except Exception as e:
            self.logger.error(f"텍스트 유사도 계산 오류: {e}")
            return 0.0
    
    async def _check_article_similarity(self, article1_title, article1_content, article2_title, article2_content, threshold=0.8):
        """두 기사 간의 유사도 체크 (공통 로직)
        
        Args:
            article1_title: 첫 번째 기사 제목
            article1_content: 첫 번째 기사 내용
            article2_title: 두 번째 기사 제목
            article2_content: 두 번째 기사 내용
            threshold: 유사도 임계값 (기본값: 0.8)
        
        Returns:
            tuple: (is_similar: bool, similarity: float)
        """
        try:
            # 제목과 내용을 결합하여 유사도 계산
            text1 = f"{article1_title} {article1_content}"
            text2 = f"{article2_title} {article2_content}"
            
            similarity = await self._calculate_text_similarity(text1, text2)
            is_similar = similarity >= threshold
            
            return is_similar, similarity
            
        except Exception as e:
            self.logger.error(f"기사 유사도 체크 오류: {e}")
            return False, 0.0
    
    async def _cleanup_memory_cache(self):
        """메모리 캐시 정리 - 만료된 항목 제거 및 크기 제한"""
        try:
            current_time = datetime.now()
            expire_time = current_time - timedelta(hours=self._cache_expire_hours)
            
            # URL 캐시 정리
            expired_urls = []
            for url, cache_data in self._news_memory_cache.items():
                if cache_data['timestamp'] < expire_time:
                    expired_urls.append(url)
            
            for url in expired_urls:
                del self._news_memory_cache[url]
            
            # 내용 유사도 캐시 정리
            self._content_similarity_cache = [
                item for item in self._content_similarity_cache 
                if item['timestamp'] >= expire_time
            ]
            
            # 크기 제한 (가장 오래된 항목부터 제거)
            if len(self._content_similarity_cache) > self._cache_max_size:
                self._content_similarity_cache.sort(key=lambda x: x['timestamp'])
                self._content_similarity_cache = self._content_similarity_cache[-self._cache_max_size:]
            
            if len(self._news_memory_cache) > self._cache_max_size:
                # URL 캐시도 크기 제한
                sorted_items = sorted(self._news_memory_cache.items(), key=lambda x: x[1]['timestamp'])
                self._news_memory_cache = dict(sorted_items[-self._cache_max_size:])
            
            if expired_urls or len(self._content_similarity_cache) > self._cache_max_size:
                self.logger.debug(f"메모리 캐시 정리 완료: URL {len(expired_urls)}개 만료, 내용 캐시 {len(self._content_similarity_cache)}개 유지")
                
        except Exception as e:
            self.logger.error(f"메모리 캐시 정리 오류: {e}")
    
    async def _check_similarity_in_memory_cache(self, title, content):
        """메모리 캐시 내 뉴스와의 유사도 검사
        
        Args:
            title: 검사할 기사 제목
            content: 검사할 기사 내용
            
        Returns:
            bool: 유사한 기사가 캐시에 있으면 True
        """
        try:
            if not self._content_similarity_cache:
                return False
            
            # 유사도 설정 가져오기
            similarity_config = self.config.get('data_collection', {}).get('news_similarity', {})
            similarity_enabled = similarity_config.get('enabled', True)
            
            if not similarity_enabled:
                return False
            
            threshold = similarity_config.get('threshold', 0.8)
            
            # 캐시된 뉴스들과 유사도 비교
            for cached_item in self._content_similarity_cache:
                cached_title = cached_item['title']
                cached_content = cached_item['content']
                
                # 유사도 계산
                is_similar, similarity = await self._check_article_similarity(
                    title, content, cached_title, cached_content, threshold
                )
                
                if is_similar:
                    self.logger.debug(
                        f"메모리 캐시 유사도 중복 발견: {title[:30]}... "
                        f"(유사도: {similarity:.3f}, 캐시 기사: {cached_title[:30]}...)"
                    )
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"메모리 캐시 유사도 검사 오류: {e}")
            return False
    
    async def _add_to_memory_cache(self, url, title, content):
        """메모리 캐시에 뉴스 추가
        
        Args:
            url: 뉴스 URL
            title: 뉴스 제목
            content: 뉴스 내용
        """
        try:
            current_time = datetime.now()
            
            # URL 캐시에 추가
            self._news_memory_cache[url] = {
                'title': title,
                'content': content,
                'timestamp': current_time
            }
            
            # 내용 유사도 캐시에 추가
            self._content_similarity_cache.append({
                'title': title,
                'content': content,
                'url': url,
                'timestamp': current_time
            })
            
            # 세션용 URL 캐시에도 추가
            self.processed_urls.add(url)
            
        except Exception as e:
            self.logger.error(f"메모리 캐시 추가 오류: {e}")
    
    async def _update_memory_cache_content(self, url, title, content):
        """메모리 캐시의 기존 항목 내용 업데이트
        
        Args:
            url: 뉴스 URL
            title: 뉴스 제목
            content: 뉴스 내용
        """
        try:
            current_time = datetime.now()
            
            # URL 캐시 내용 업데이트
            if url in self._news_memory_cache:
                self._news_memory_cache[url].update({
                    'title': title,
                    'content': content,
                    'timestamp': current_time
                })
            
            # 내용 유사도 캐시에 추가
            self._content_similarity_cache.append({
                'title': title,
                'content': content,
                'url': url,
                'timestamp': current_time
            })
            
        except Exception as e:
            self.logger.error(f"메모리 캐시 내용 업데이트 오류: {e}")
    
    async def _check_db_similarity(self, title, content):
         """DB와의 유사도 체크 (간소화된 버전)
         
         Args:
             title: 기사 제목
             content: 기사 내용
         
         Returns:
             tuple: (is_duplicate: bool, max_similarity: float)
         """
         try:
             # 유사도 검사 설정 확인
             similarity_config = self.config.get('data_collection', {}).get('news_similarity', {})
             similarity_enabled = similarity_config.get('enabled', True)
             
             if not similarity_enabled:
                 return False, 0.0
             
             # DB의 유사도 검사 기능 사용 (최근 뉴스와 비교)
             threshold = similarity_config.get('threshold', 0.8)
             is_duplicate, max_similarity = await self.db.check_content_similarity(
                 title, content, threshold, days_back=2
             )
             
             if is_duplicate:
                 self.logger.debug(f"DB 유사 뉴스 발견 (유사도: {max_similarity:.3f})")
             
             return is_duplicate, max_similarity
             
         except Exception as e:
             self.logger.error(f"DB 유사도 체크 오류: {e}")
             return False, 0.0
    

    
    async def _process_and_save_article(self, article, article_url):
        """기사 처리 및 저장
        
        Args:
            article: 기사 정보
            article_url: 기사 URL (중복 방지용)
        """
        try:
            # 기사 처리 시 세마포어 사용하여 동시성 제한
            async with self.article_processing_semaphore:
                processed_article = await self._process_article(article)
                if processed_article is None:
                    self.logger.debug(f"주식 관련성 없음으로 필터링된 기사: {article.get('title', '')[:50]}...")
                    return
            
            if processed_article:
                title = processed_article.get('title', '')
                content = processed_article.get('content', '')
                
                # 데이터베이스 저장 시 세마포어 사용
                async with self.db_semaphore:
                    # 최종 중복 검사 (DB 저장 직전)
                    # 1. 제목 완전 일치 검사 (엄격한 중복 검사)
                    strict_title_check = self.config.get('data_collection', {}).get('news_similarity', {}).get('strict_title_check', True)
                    if strict_title_check:
                        is_title_duplicate = await self.db.check_title_duplicate(title)
                        if is_title_duplicate:
                            self.logger.info(f"제목 완전 일치 중복 기사 스킵: {title[:50]}...")
                            return
                    
                    # 2. 내용 유사도 검사 (기존 로직)
                    is_content_duplicate, max_similarity = await self._check_db_similarity(title, content)
                    if is_content_duplicate:
                        self.logger.info(f"DB 유사도 중복 기사 스킵: {title[:50]}... (유사도: {max_similarity:.3f})")
                        return
                    
                    # 기사 저장
                    await self._save_article(processed_article)
                    
                    # 메모리 캐시 내용 업데이트 (URL은 이미 추가됨)
                    await self._update_memory_cache_content(article_url, title, content)
                    
                    self.logger.debug(f"기사 저장 완료: {title[:50]}...")
                    
        except Exception as e:
            self.logger.error(f"기사 처리 및 저장 오류: {e}")
            # 처리 실패 시 캐시에서 URL 제거 (재시도 가능하도록)
            if article_url in self.processed_urls:
                self.processed_urls.discard(article_url)
            # 메모리 캐시에서도 제거
            if article_url in self._news_memory_cache:
                del self._news_memory_cache[article_url]
    

    

    async def _process_article(self, article: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """기사 처리 및 분석
        
        Args:
            article: 기사 정보
        
        Returns:
            처리된 기사 정보
        """
        try:
            # 기사 내용이 없으면 제목만 사용 (개별 본문 수집 제거로 성능 최적화)
            if not article.get('content'):
                article['content'] = article.get('title', '')
            
            # 뉴스가 주식에 영향을 줄 수 있는지 판단 (비동기 호출)
            content = article['title'] + ' ' + article.get('content', '')
            is_stock_result = await self._is_stock_relevant_news(content)

            
            # 주식 관련 뉴스가 아니면 처리하지 않음
            if not is_stock_result:
                return None
        
            
            return {
                'title': article['title'],
                'content': article.get('content', ''),
                'source': article['source'],
                'url': article['url'],
                'published_at': article.get('published_date') or article.get('published'),
                'symbols': [],  # 종목 추출하지 않음
                'sentiment': None,
                'importance': None
            }
        
        except Exception as e:
            self.logger.error(f"기사 처리 오류: {e}")
            return None
    
    async def _fetch_article_content(self, url: str) -> str:
        """
        기사 본문 수집 - 직접 구현
        
        Args:
            url: 기사 URL
        
        Returns:
            기사 본문
        """
        try:
            async with self.session.get(url, timeout=5) as response:
                if response.status == 200:
                    html = await response.text()
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # 일반적인 기사 본문 선택자들
                    content_selectors = [
                        '[itemprop="articleBody"]',
                        '.article-content',
                        '.news-content',
                        '.content-body',
                        '#articletxt',
                        '.article-body'
                    ]
                    
                    for selector in content_selectors:
                        content_elem = soup.select_one(selector)
                        if content_elem:
                            # 불필요한 요소 제거
                            for unwanted in content_elem.find_all(['script', 'style', 'iframe', 'ins']):
                                unwanted.decompose()
                            
                            text = content_elem.get_text(strip=True)
                            if text and len(text) > 50:
                                return text
                    
                    # 폴백: p 태그들에서 추출
                    paragraphs = soup.find_all('p')
                    if paragraphs:
                        content = ' '.join([p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True)])
                        if content and len(content) > 50:
                            return content
                            
        except Exception as e:
            self.logger.debug(f"기사 본문 수집 실패 ({url}): {e}")
        
        return ''
    

    def _analyze_sentiment(self, text: str) -> float:
        """감정 분석 (최적화)
        
        Args:
            text: 분석할 텍스트
        
        Returns:
            감정 점수 (-1.0 ~ 1.0)
        """
        # 텍스트를 소문자로 변환하여 검색 성능 향상
        text_lower = text.lower()
        
        # 키워드 검색 최적화
        positive_count = sum(1 for keyword in self.positive_keywords if keyword in text_lower)
        negative_count = sum(1 for keyword in self.negative_keywords if keyword in text_lower)
        
        total_count = positive_count + negative_count
        if total_count == 0:
            return 0.0
        
        sentiment = (positive_count - negative_count) / total_count
        return max(-1.0, min(1.0, sentiment))
    
    async def _initialize_ai_model(self):
        """LLM AI 모델 초기화 (비동기)
        
        LLM 모델을 사용하여 대화형 AI 기반 주식 관련성 분류
        """
        if not AI_AVAILABLE:
            return
            
        # AI 모델 설정 확인
        ai_config = self.config.get('data_collection', {}).get('ai_model', {}).get('stock_check', {})
        if not ai_config.get('enabled', True):
            self.logger.info("AI 모델이 비활성화되어 있습니다.")
            return
            
        try:
            with self._ai_model_lock:
                if self._ai_model_loaded:
                    return
                    
                self.logger.info("LLM AI 분류기 초기화 시작...")
                
                # 외부 LLM 서버로 직접 연결 (서버 자체 실행 안함)
                # LLM 분류기 생성 - 외부 서버 URL 사용
                self._ai_classifier = LLMClassifier(server_url="http://192.168.31.47:1234")
                
                # 분류기 초기화 (비동기 호출)
                await self._ai_classifier.initialize()
                self._ai_model_loaded = True
                self.logger.info("LLM AI 분류기 초기화 완료 (외부 서버 연결)")
                
        except Exception as e:
            self.logger.error(f"AI 분류기 초기화 실패: {e}")
            self._ai_model_loaded = False
    
    async def _ai_classify_stock_relevance(self, text: str) -> bool:
        """LLM AI 모델을 사용한 주식 관련성 분류
        
        Args:
            text: 분석할 텍스트
            
        Returns:
            주식관련여부
        """
        if not AI_AVAILABLE or not self._ai_model_loaded or not self._ai_classifier:
            return False
            
        try:
            import time
            start_time = time.time()
            
            # LLM 분류기를 사용한 시장 관련성 분석 (비동기 호출)
            result = await self._ai_classifier.classify_news(text)
            
            # 시장 관련성 여부 반환
            is_result = result.is_market_result

            # 분류 결과 로깅 (디버깅용)
            self.logger.info('')
            self.logger.info(f"LLM 분류 결과")
            self.logger.info(f"관련성: {is_result}")
            
            # 처리 시간 측정
            processing_time = time.time() - start_time
            
            # 분류 결과 로깅 (한 줄로 최적화)
            self.logger.info(f"LLM 분류: {is_result} ({processing_time:.3f}s)")
            
            return is_result
                    
        except Exception as e:
            self.logger.error(f"LLM AI 분류 오류: {e}")
            return False
    
    async def _is_stock_relevant_news(self, news_data) -> bool:
        """뉴스가 주식 시장에 영향을 줄 수 있는지 판단 (순수 AI 기반)
        
        Args:
            news_data: 뉴스 데이터 (딕셔너리 또는 문자열)
        
        Returns:
            주식 관련 여부
        """
        import time
        start_time = time.time()
        
        # 뉴스 데이터에서 텍스트 추출
        if isinstance(news_data, dict):
            title = news_data.get('title', '')
            content = news_data.get('content', '')
            text = f"{title} {content}"
            news_title = title[:50]
        else:
            text = str(news_data)
            news_title = text[:50]
        
        # LLM AI 모델을 사용한 시장 관련성 분석 (비동기 호출)
        ai_result = await self._ai_classify_stock_relevance(text)
        
        # 전체 처리 시간 측정
        total_time = time.time() - start_time
        
        # 결과 로깅 (성능 정보 포함)
        self.logger.info(f"주식 관련성 검증 완료: {ai_result} (총 {total_time:.3f}s) - {news_title}...")
        
        return ai_result
    
    def _calculate_importance(self, article: Dict[str, Any], symbols: List[str]) -> int:
        """기사 중요도 계산
        
        Args:
            article: 기사 정보
            symbols: 관련 종목 리스트
        
        Returns:
            중요도 (1-10)
        """
        importance = 1
        
        # 관련 종목 수에 따른 가중치
        importance += min(len(symbols) * 2, 6)
        
        # 제목에 중요 키워드 포함 여부
        important_keywords = ['급등', '급락', '신고가', '최저가', '실적', '공시', '인수합병', 'M&A']
        for keyword in important_keywords:
            if keyword in article['title']:
                importance += 1
        
        # 소스별 가중치
        source_weights = {
            '한국경제': 2,
            '매일경제': 2,
            '네이버 증권': 1
        }
        importance += source_weights.get(article['source'], 0)
        
        return min(importance, 10)
    
    async def _save_article(self, article: Dict[str, Any]):
        """기사 저장
        
        Args:
            article: 기사 정보
        """
        try:
            # 데이터베이스 저장 및 ID 반환
            news_id = await self.db.insert_news_data(article)
            
            # 기사 데이터에 ID 추가
            article_with_id = article.copy()
            article_with_id['id'] = news_id
            article_with_id['created_at'] = datetime.now()  # 저장 시간 추가
            
            # 메모리 캐시 업데이트 (새로운 뉴스 추가)
            try:
                await self._add_to_memory_cache(
                    article_with_id.get('url', ''),
                    article_with_id.get('title', ''),
                    article_with_id.get('content', '')
                )
            except Exception as cache_error:
                self.logger.error(f"메모리 캐시 업데이트 오류: {cache_error}")
            
            # 콜백 호출 (ID가 포함된 기사 데이터 전달) - 비동기로 실행하되 결과를 기다리지 않음
            for callback in self.news_callbacks:
                try:
                    # 콜백을 백그라운드에서 실행하고 결과를 기다리지 않음
                    asyncio.create_task(callback(article_with_id))
                except Exception as e:
                    self.logger.error(f"뉴스 콜백 오류: {e}")
            
            self.logger.debug(f"뉴스 저장 완료 (ID: {news_id}): {article['title'][:50]}...")
        
        except Exception as e:
            self.logger.error(f"뉴스 저장 오류: {e}")
    
    async def get_recent_news(self, symbol: Optional[str] = None, hours: int = 24) -> List[Dict[str, Any]]:
        """최근 뉴스 조회
        
        Args:
            symbol: 종목코드 (None이면 전체)
            hours: 조회할 시간 범위
        
        Returns:
            뉴스 리스트
        """
        try:
            news_list = await self.db.get_recent_news(hours)
            
            if symbol:
                # 특정 종목 관련 뉴스만 필터링
                filtered_news = []
                for news in news_list:
                    symbols = json.loads(news.get('symbols', '[]'))
                    if symbol in symbols:
                        filtered_news.append(news)
                return filtered_news
            
            return news_list
        
        except Exception as e:
            self.logger.error(f"뉴스 조회 오류: {e}")
            return []
    
    def get_news_summary(self) -> Dict[str, Any]:
        """뉴스 수집 요약 정보
        
        Returns:
            뉴스 수집 요약
        """
        return {
            'total_sources': len(self.news_sources),
            'processed_urls': len(self.processed_urls),
            'collection_status': self.is_collecting,
            'collection_interval': self.collection_interval
        }
    
    async def _cleanup_old_news(self):
        """오래된 뉴스 정리"""
        while self.is_collecting:
            try:
                # 6시간마다 정리
                await asyncio.sleep(21600)
                
                # 처리된 URL 캐시 크기 제한 및 DB와 동기화
                if len(self.processed_urls) > 15000:
                    try:
                        # DB에서 최근 7일간의 뉴스 URL 조회
                        async with self.db_semaphore:
                            recent_urls = await self.db.get_recent_news_urls(days=7)
                        
                        if recent_urls:
                            # 캐시와 DB의 교집합만 유지 (실제 DB에 있는 URL만)
                            self.processed_urls = self.processed_urls.intersection(set(recent_urls))
                            
                            # 여전히 크기가 크면 최근 10000개만 유지
                            if len(self.processed_urls) > 10000:
                                self.processed_urls = set(list(self.processed_urls)[-10000:])
                        else:
                            # DB 조회 실패 시 단순 크기 제한
                            self.processed_urls = set(list(self.processed_urls)[-10000:])
                            
                    except Exception as db_error:
                        self.logger.warning(f"DB 연동 캐시 정리 실패, 단순 정리 수행: {db_error}")
                        # DB 연동 실패 시 단순 크기 제한
                        self.processed_urls = set(list(self.processed_urls)[-10000:])
                
                self.logger.info(f"뉴스 캐시 정리 완료 (현재 캐시 크기: {len(self.processed_urls)})")
            
            except Exception as e:
                self.logger.error(f"뉴스 정리 오류: {e}")
                # 오류 발생 시 긴급 캐시 정리
                try:
                    if len(self.processed_urls) > 20000:
                        self.processed_urls = set(list(self.processed_urls)[-5000:])
                        self.logger.warning("긴급 캐시 정리 수행")
                except Exception:
                    pass
    
    async def close(self):
        """뉴스 수집기 종료"""
        try:
            await self.stop_collection()
            
            if self.session:
                try:
                    # 모든 연결을 강제로 닫고 정리
                    if not self.session.closed:
                        await self.session.close()
                        # 세션이 완전히 닫힐 때까지 잠시 대기
                        await asyncio.sleep(0.1)
                except Exception as e:
                    self.logger.debug(f"세션 종료 중 오류 (무시됨): {e}")
            
            self.logger.info("뉴스 수집기 종료")
        except Exception as e:
            self.logger.error(f"뉴스 수집기 종료 오류: {e}")