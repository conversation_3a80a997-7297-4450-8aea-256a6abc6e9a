#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lightweight AI Classifier CLI 관리 도구

주식 뉴스 분류기를 수동으로 관리하고 테스트할 수 있는 CLI 인터페이스
"""

import os
import sys
import json
from datetime import datetime

# 현재 디렉토리를 Python 경로에 추가
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.ai_local.stock_check.kf_deberta_classifier import KFDeBERTaStockClassifier


class ClassifierCLI:
    """분류기 CLI 관리 클래스"""
    
    def __init__(self):
        self.classifier = None
        self.training_data_path = os.path.join(project_root, "src", "ai_local", "stock_check", "training_data.json")
        
    def initialize_classifier(self, auto_retrain=True):
        """분류기 초기화"""
        try:
            print("🤖 AI 분류기 초기화 중...")
            self.classifier = KFDeBERTaStockClassifier(
                auto_retrain=auto_retrain,
                training_data_path=self.training_data_path
            )
            print("✅ 분류기 초기화 완료!")
            return True
        except Exception as e:
            print(f"❌ 분류기 초기화 실패: {e}")
            return False
    
    def show_main_menu(self):
        """메인 메뉴 표시"""
        print("\n" + "="*50)
        print("🚀 KF-DeBERTa AI Classifier 관리 도구")
        print("="*50)
        print("1. 분류기 정보 확인")
        print("2. 학습 데이터 통계")
        print("3. 텍스트 분류 테스트")
        print("4. 모델 훈련/재훈련")
        print("5. 학습 데이터 추가")
        print("6. 학습 데이터 파일 편집")
        print("7. 배치 테스트 실행")
        print("8. 자동 재훈련 설정")
        print("0. 종료")
        print("="*50)
    
    def show_classifier_info(self):
        """분류기 정보 표시"""
        if not self.classifier:
            print("❌ 분류기가 초기화되지 않았습니다.")
            return
            
        print("\n📊 분류기 정보")
        print("-" * 30)
        
        info = self.classifier.get_model_info()
        stats = self.classifier.get_training_data_stats()
        
        print(f"모델 훈련 상태: {'✅ 훈련됨' if info['is_trained'] else '❌ 미훈련'}")
        print(f"모델 파일 경로: {info['model_file_path']}")
        print(f"학습 데이터 경로: {info['training_data_path']}")
        print(f"벡터라이저 특성 수: {info['vectorizer_features']}")
        print(f"총 학습 데이터: {stats['total_count']}개")
        print(f"  - 주식 관련: {stats['stock_texts_count']}개")
        print(f"  - 비주식 관련: {stats['non_stock_texts_count']}개")
        
        # 파일 수정 시간 확인
        if os.path.exists(self.training_data_path):
            mtime = os.path.getmtime(self.training_data_path)
            mtime_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            print(f"학습 데이터 파일 수정 시간: {mtime_str}")
    
    def test_classification(self):
        """텍스트 분류 테스트"""
        if not self.classifier:
            print("❌ 분류기가 초기화되지 않았습니다.")
            return
            
        print("\n🧪 텍스트 분류 테스트")
        print("-" * 30)
        print("분류할 텍스트를 입력하세요 (종료: 'quit' 또는 'q'):")
        
        while True:
            text = input("\n> ").strip()
            if text.lower() in ['quit', 'q', '종료']:
                break
                
            if not text:
                continue
                
            try:
                is_stock, confidence = self.classifier.classify(text)
                result = "주식 관련" if is_stock else "비주식 관련"
                confidence_percent = confidence * 100
                
                print(f"📝 입력: {text}")
                print(f"🎯 결과: {result} (신뢰도: {confidence_percent:.1f}%)")
                
                # 신뢰도에 따른 이모지 표시
                if confidence_percent >= 80:
                    print("🟢 높은 신뢰도")
                elif confidence_percent >= 60:
                    print("🟡 보통 신뢰도")
                else:
                    print("🔴 낮은 신뢰도")
                    
            except Exception as e:
                print(f"❌ 분류 중 오류: {e}")
    
    def train_model(self):
        """모델 훈련/재훈련"""
        if not self.classifier:
            print("❌ 분류기가 초기화되지 않았습니다.")
            return
            
        print("\n🎓 모델 훈련")
        print("-" * 30)
        print("1. 새로 훈련")
        print("2. 재훈련 (기존 모델 삭제 후 훈련)")
        
        choice = input("선택 (1-2): ").strip()
        
        try:
            if choice == '1':
                print("🔄 모델 훈련 시작...")
                success = self.classifier.train_model()
            elif choice == '2':
                print("🔄 모델 재훈련 시작...")
                success = self.classifier.retrain_model()
            else:
                print("❌ 잘못된 선택입니다.")
                return
                
            if success:
                print("✅ 훈련 완료!")
                self.show_classifier_info()
            else:
                print("❌ 훈련 실패!")
                
        except Exception as e:
            print(f"❌ 훈련 중 오류: {e}")
    
    def add_training_data(self):
        """학습 데이터 추가"""
        if not self.classifier:
            print("❌ 분류기가 초기화되지 않았습니다.")
            return
            
        print("\n📚 학습 데이터 추가")
        print("-" * 30)
        print("1. 주식 관련 텍스트 추가")
        print("2. 비주식 관련 텍스트 추가")
        print("3. 둘 다 추가")
        
        choice = input("선택 (1-3): ").strip()
        
        stock_texts = []
        non_stock_texts = []
        
        if choice in ['1', '3']:
            print("\n주식 관련 텍스트를 입력하세요 (완료: 빈 줄):")
            while True:
                text = input("> ").strip()
                if not text:
                    break
                stock_texts.append(text)
                
        if choice in ['2', '3']:
            print("\n비주식 관련 텍스트를 입력하세요 (완료: 빈 줄):")
            while True:
                text = input("> ").strip()
                if not text:
                    break
                non_stock_texts.append(text)
        
        if stock_texts or non_stock_texts:
            try:
                self.classifier.add_training_data(stock_texts, non_stock_texts)
                print(f"✅ 데이터 추가 완료! (주식: {len(stock_texts)}개, 비주식: {len(non_stock_texts)}개)")
                print("⚠️  모델 재훈련이 필요합니다.")
            except Exception as e:
                print(f"❌ 데이터 추가 실패: {e}")
        else:
            print("❌ 추가할 데이터가 없습니다.")
    
    def edit_training_data_file(self):
        """학습 데이터 파일 편집"""
        print("\n📝 학습 데이터 파일 편집")
        print("-" * 30)
        print(f"파일 경로: {self.training_data_path}")
        
        if not os.path.exists(self.training_data_path):
            print("❌ 학습 데이터 파일이 존재하지 않습니다.")
            return
            
        try:
            # 기본 에디터로 파일 열기
            if sys.platform == "darwin":  # macOS
                os.system(f"open -t '{self.training_data_path}'")
            elif sys.platform == "linux":
                os.system(f"nano '{self.training_data_path}'")
            elif sys.platform == "win32":
                os.system(f"notepad '{self.training_data_path}'")
            else:
                print(f"수동으로 파일을 편집하세요: {self.training_data_path}")
                
            print("📝 파일이 기본 에디터에서 열렸습니다.")
            print("⚠️  파일 수정 후 자동 재훈련이 활성화되어 있으면 다음 분류 시 자동으로 재훈련됩니다.")
            
        except Exception as e:
            print(f"❌ 파일 열기 실패: {e}")
    
    def batch_test(self):
        """배치 테스트 실행"""
        if not self.classifier:
            print("❌ 분류기가 초기화되지 않았습니다.")
            return
            
        print("\n🧪 배치 테스트")
        print("-" * 30)
        
        # 테스트 데이터
        test_cases = [
            ("삼성전자 주가가 10% 상승했습니다", True),
            ("코스피 지수가 하락세를 보이고 있습니다", True),
            ("반도체 관련주들이 강세입니다", True),
            ("네이버 주식을 매수했습니다", True),
            ("투자자들이 관심을 보이는 종목입니다", True),
            ("오늘 날씨가 맑습니다", False),
            ("새로운 영화가 개봉되었습니다", False),
            ("교통 체증이 심합니다", False),
            ("축구 경기가 열렸습니다", False),
            ("맛있는 음식을 먹었습니다", False)
        ]
        
        correct = 0
        total = len(test_cases)
        
        print(f"총 {total}개 테스트 케이스 실행 중...\n")
        
        for i, (text, expected) in enumerate(test_cases, 1):
            try:
                is_stock, confidence = self.classifier.classify(text)
                is_correct = is_stock == expected
                
                if is_correct:
                    correct += 1
                    status = "✅"
                else:
                    status = "❌"
                
                expected_str = "주식" if expected else "비주식"
                result_str = "주식" if is_stock else "비주식"
                
                print(f"{status} {i:2d}. {text}")
                print(f"     예상: {expected_str} | 결과: {result_str} | 신뢰도: {confidence*100:.1f}%")
                
            except Exception as e:
                print(f"❌ {i:2d}. 테스트 실패: {e}")
        
        accuracy = (correct / total) * 100
        print(f"\n📊 테스트 결과: {correct}/{total} ({accuracy:.1f}% 정확도)")
        
        if accuracy >= 80:
            print("🟢 우수한 성능")
        elif accuracy >= 60:
            print("🟡 보통 성능")
        else:
            print("🔴 성능 개선 필요")
    
    def toggle_auto_retrain(self):
        """자동 재훈련 설정 변경"""
        if not self.classifier:
            print("❌ 분류기가 초기화되지 않았습니다.")
            return
            
        current_status = "활성화" if self.classifier.auto_retrain else "비활성화"
        print(f"\n⚙️  현재 자동 재훈련 상태: {current_status}")
        
        new_status = not self.classifier.auto_retrain
        new_status_str = "활성화" if new_status else "비활성화"
        
        confirm = input(f"자동 재훈련을 {new_status_str}하시겠습니까? (y/n): ").strip().lower()
        
        if confirm in ['y', 'yes', '예', 'ㅇ']:
            self.classifier.auto_retrain = new_status
            print(f"✅ 자동 재훈련이 {new_status_str}되었습니다.")
        else:
            print("❌ 설정이 변경되지 않았습니다.")
    
    def run(self):
        """CLI 실행"""
        print("🚀 Lightweight AI Classifier CLI 시작")
        
        # 분류기 초기화
        if not self.initialize_classifier():
            return
        
        while True:
            try:
                self.show_main_menu()
                choice = input("\n선택하세요: ").strip()
                
                if choice == '0':
                    print("👋 프로그램을 종료합니다.")
                    break
                elif choice == '1':
                    self.show_classifier_info()
                elif choice == '2':
                    stats = self.classifier.get_training_data_stats()
                    print(f"\n📊 학습 데이터 통계")
                    print(f"주식 관련: {stats['stock_texts_count']}개")
                    print(f"비주식 관련: {stats['non_stock_texts_count']}개")
                    print(f"총 데이터: {stats['total_count']}개")
                    print(f"훈련 상태: {'✅ 훈련됨' if stats['is_trained'] else '❌ 미훈련'}")
                elif choice == '3':
                    self.test_classification()
                elif choice == '4':
                    self.train_model()
                elif choice == '5':
                    self.add_training_data()
                elif choice == '6':
                    self.edit_training_data_file()
                elif choice == '7':
                    self.batch_test()
                elif choice == '8':
                    self.toggle_auto_retrain()
                else:
                    print("❌ 잘못된 선택입니다. 다시 선택해주세요.")
                    
                input("\n계속하려면 Enter를 누르세요...")
                
            except KeyboardInterrupt:
                print("\n\n👋 프로그램을 종료합니다.")
                break
            except Exception as e:
                print(f"\n❌ 오류 발생: {e}")
                input("계속하려면 Enter를 누르세요...")


if __name__ == "__main__":
    cli = ClassifierCLI()
    cli.run()