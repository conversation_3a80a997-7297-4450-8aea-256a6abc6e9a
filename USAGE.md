# StockBot 사용법 가이드

## 🚀 빠른 시작

### 1. 기본 실행
```bash
# 가장 간단한 실행 방법
./run_stockbot.sh
```

### 2. 디버그 모드 실행
```bash
# 상세한 로그와 함께 실행
./run_stockbot.sh --debug
```

### 3. 상태 확인
```bash
# StockBot이 실행 중인지 확인
./run_stockbot.sh --status
```

### 4. 중지
```bash
# 실행 중인 StockBot 중지
./run_stockbot.sh --stop
```

## 📋 실행 스크립트 비교

| 기능 | 셸 스크립트 | Python 스크립트 |
|------|-------------|------------------|
| 실행 명령어 | `./run_stockbot.sh` | `python3 run_stockbot.py` |
| 환경 확인 | ✅ | ✅ |
| 의존성 설치 | ✅ | ✅ |
| 프로세스 관리 | ✅ | ✅ |
| 색상 출력 | ✅ | ✅ |
| 크로스 플랫폼 | Linux/macOS | 모든 플랫폼 |

## 🔧 고급 사용법

### 환경 변수 설정
```bash
# .env 파일 편집
vim .env

# 필수 설정 항목:
# - KIS_APP_KEY: 한국투자증권 앱 키
# - KIS_APP_SECRET: 한국투자증권 앱 시크릿
# - OPENAI_API_KEY: OpenAI API 키
# - TELEGRAM_BOT_TOKEN: 텔레그램 봇 토큰
# - TELEGRAM_CHAT_ID: 텔레그램 채팅 ID
```

### 로그 확인
```bash
# 실시간 로그 확인
tail -f logs/stockbot.log

# 최근 로그 확인
tail -n 50 logs/stockbot.log

# 특정 키워드로 로그 검색
grep "ERROR" logs/stockbot.log
```

### 데이터베이스 확인
```bash
# SQLite 데이터베이스 접속
sqlite3 data/stockbot.db

# 테이블 목록 확인
.tables

# 포지션 확인
SELECT * FROM positions;

# 거래 내역 확인
SELECT * FROM trades ORDER BY created_at DESC LIMIT 10;
```

## 🐛 문제 해결

### 일반적인 문제들

#### 1. 권한 오류
```bash
# 실행 권한 부여
chmod +x run_stockbot.sh
chmod +x run_stockbot.py
```

#### 2. Python 모듈 오류
```bash
# 의존성 재설치
pip3 install -r requirements.txt

# 가상환경 사용 (권장)
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 3. API 키 오류
```bash
# .env 파일 확인
cat .env

# 환경 변수 확인
echo $KIS_APP_KEY
echo $OPENAI_API_KEY
```

#### 4. 포트 충돌
```bash
# 실행 중인 프로세스 확인
ps aux | grep python | grep main.py

# 강제 종료
./run_stockbot.sh --stop
```

### 로그 레벨별 의미

- 🔍 **DEBUG**: 상세한 디버깅 정보
- ℹ️ **INFO**: 일반적인 정보 메시지
- ⚠️ **WARNING**: 주의가 필요한 상황
- ❌ **ERROR**: 오류 발생 (기능 제한적 동작)
- 🚨 **CRITICAL**: 치명적 오류 (시스템 중단 가능)

### 색상 코드 의미

- 🟢 **녹색**: 정상 동작, 수익
- 🟡 **노란색**: 경고, 주의 필요
- 🔴 **빨간색**: 오류, 손실
- 🔵 **파란색**: 주문, API 관련
- 🟣 **보라색**: AI 분석, KIS API

## 📞 지원

문제가 발생하면 다음 정보와 함께 문의해주세요:

1. 실행 환경 (OS, Python 버전)
2. 오류 메시지 전문
3. 최근 로그 파일 (`logs/stockbot.log`)
4. 설정 파일 (민감한 정보 제외)

## 📚 추가 자료

- [한국투자증권 OpenAPI 문서](https://apiportal.koreainvestment.com/)
- [OpenAI API 문서](https://platform.openai.com/docs)
- [Telegram Bot API 문서](https://core.telegram.org/bots/api)