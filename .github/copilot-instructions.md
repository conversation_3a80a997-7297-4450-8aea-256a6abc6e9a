# AI 코딩 에이전트를 위한 StockBot 가이드

## 프로젝트 개요

StockBot은 한국투자증권 API를 활용한 AI 기반 초단타/단타 중심의 자동 주식 매매 시스템입니다.
핵심 목표는 뉴스·공시 감지 후 ChatGPT API 분석을 통한 3초 이내 매매 실행입니다.

## 주요 아키텍처 및 데이터 흐름

### 핵심 컴포넌트

1. **AI 분석 엔진** (`src/ai_analysis/`)

   - `decision_engine.py`: OpenAI GPT 기반 매매 의사결정
   - `prompt_manager.py`: 동적 프롬프트 관리
   - `risk_analyzer.py`: 리스크 분석 및 포지션 관리

2. **데이터 수집** (`src/data_collection/`)

   - `market_data_manager.py`: 단일 진입점으로 모든 시장 데이터 통합 관리
   - `kis_api.py`: 한국투자증권 API 연동
   - `market_sources/`: 실시간 시세, 주식 정보, 호가 데이터 수집
   - `news_sources/`: 뉴스 데이터 수집

3. **거래 시스템** (`src/trading/`)
   - `execution_engine.py`: 주문 실행 엔진
   - `order_manager.py`: 포지션 및 주문 관리

## 개발자 워크플로우

### 환경 설정

```bash
# 1. 설정 파일 생성
cp config.yaml.example config.yaml
# 2. API 키 및 설정 수정
vi config.yaml
```

### 실행

```bash
# 일반 실행
./stockbot.sh

# 디버그 모드
./stockbot.sh --debug
```

### 로깅

- 모든 로그는 `logs/` 디렉토리에 저장
  - `stockbot.log`: 일반 로그
  - `errors.log`: 오류 로그
  - `performance.log`: 성능 메트릭

## 프로젝트 특화 패턴

### 초고속 거래 최적화

- 모든 API 호출은 비동기(`async/await`) 사용 필수
- 데이터베이스 작업은 배치 처리로 성능 최적화
- 메모리 캐시를 적극 활용하여 지연 최소화

### 에러 처리

- 중요 거래 작업은 반드시 재시도 메커니즘 구현
- 네트워크 오류는 자동 재연결 처리
- 치명적 오류 발생 시 즉시 포지션 정리

### AI 통합

- AI 요청은 `DecisionEngine` 클래스를 통해 중앙 집중화
- 프롬프트는 `PromptManager`로 동적 관리
- AI 응답은 정형화된 JSON 형식으로 파싱

## 주요 참조 파일

- `src/ai_analysis/decision_engine.py`: AI 의사결정 엔진 구현 예시
- `src/data_collection/market_data_manager.py`: 데이터 통합 관리 패턴
- `stockbot_설계서.txt`: 상세 설계 문서
